# s2

sudo apt install protobuf-compiler
go install github.com/gogo/protobuf/protoc-gen-gofast@latest
cd fakecli  & npm install
cd core/env & docker compose up nats -d
配置:service/dev/configs.yaml

make httpcli
pb.WalletLoginReq({Msg:"m", Address:"0x1", Sign:"xx"}, "Account")
pb.UserInfoReq()
pb.GameSpinReq({GameID:100001,DeskID:1,SpinIndex:1,Currency:10000})

nuc
make dev config=configs/dev.yaml

//cs2/message 客户端/服务器内部
//Response/Handle 有回复/无回复
//userops/message 有玩家/无玩家

make wscli
pb.WinnersMsg({})