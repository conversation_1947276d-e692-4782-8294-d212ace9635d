package gsconf

import (
	"fmt"
	"s2/pb"
	"strings"

	"github.com/jfcwrlight/core/conf/basic"
	"github.com/jfcwrlight/core/conf/table"
)

var (
	_ = table.Add(parseGameInfo)
)

type GameInfoConf struct {
	ID            int32
	Name          string
	Platform      string //显示分类区分平台
	PlatformS     string //服务商平台区分
	DeskNumber    int32
	Open          bool
	GameType      int32
	Sort          int32
	JackPotOpen   int32
	Third         []any
	AlgoMode      []int32
	Tax           float64
	ListeningAPI  []string
	GameUrl       string
	Language      []string
	ThirdGameCode string
	Recommend     bool
}

func (t GameInfoConf) GetID() int32 {
	return t.ID
}

func (t GameInfoConf) TableName() string {
	return "GameInfo"
}

func (t *GameInfoConf) KeyWord() string {
	words := ""
	key := fmt.Sprintf("TID_NAME_%d", t.ID)
	ls := table.GetALL[LanguageStringConf]()
	for _, l := range ls {
		if l.TID == key {
			words = l.Zh + "|" + l.En + "|" + l.Pt + "|" + l.Vn + "|" + l.Rus + "|" + l.Esp
			words = strings.ToLower(words)
			break
		}
	}
	if words == "" {
		words = strings.ToLower(t.Name)
	}
	return words
}

func (t GameInfoConf) ToPB() *pb.GameInfo {
	return &pb.GameInfo{
		ID:          t.ID,
		Name:        t.Name,
		Platform:    t.Platform,
		DeskNumber:  t.DeskNumber,
		GameType:    t.GameType,
		Open:        t.Open,
		JackPotOpen: t.JackPotOpen,
	}
}

// 获取语言 返回value, key
func (t GameInfoConf) GetLanguage(lang pb.EnumLanguage) (string, string) {
	name := pb.EnumLanguage_name[int32(lang)]
	key := ""
	value := ""
	defaultLang := ""
	for i, l := range t.Language {
		if l == "DEFAULT" {
			defaultLang = t.Language[i+1]
		} else if l == "KEY" {
			key = t.Language[i+1]
		} else if l == name {
			value = t.Language[i+1]
		}
	}
	if value == "" {
		value = defaultLang
	}
	return value, key
}
func parseGameInfo(raw basic.Raw) (*GameInfoConf, error) {
	return table.Default[GameInfoConf](raw)
}
