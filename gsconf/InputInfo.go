package gsconf

import (
	"s2/pb"

	"github.com/jfcwrlight/core/conf/basic"
	"github.com/jfcwrlight/core/conf/table"
)

var (
	_ = table.Add(parseInputInfo)
)

type InputInfoConf struct {
	ID         int32
	GameID     int32
	CurrencyID int32
	ValueList  []float64
}

func (t InputInfoConf) GetID() int32 {
	return t.ID
}

func (t InputInfoConf) TableName() string {
	return "InputInfo"
}

func (t InputInfoConf) ToPB() []*pb.IDValFloat {
	list := []*pb.IDValFloat{}
	for _, value := range t.ValueList {
		list = append(list, &pb.IDValFloat{
			ID:    int64(t.CurrencyID),
			Value: value,
		})
	}
	return list
}
func parseInputInfo(raw basic.Raw) (*InputInfoConf, error) {
	raw.TranslateJSON("ValueList")
	return table.Default[InputInfoConf](raw)
}
