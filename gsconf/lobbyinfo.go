package gsconf

import (
	"github.com/jfcwrlight/core/conf/basic"
	"github.com/jfcwrlight/core/conf/table"
)

var (
	_ = table.Add(parseLobbyInfo)
)

type LobbyInfoConf struct {
	ID             int32
	CurrencyList   []int32
	RecommendGames []int32
}

func (t LobbyInfoConf) GetID() int32 {
	return t.ID
}

func (t LobbyInfoConf) TableName() string {
	return "LobbyInfo"
}

func parseLobbyInfo(raw basic.Raw) (*LobbyInfoConf, error) {
	return table.Default[LobbyInfoConf](raw)
}
