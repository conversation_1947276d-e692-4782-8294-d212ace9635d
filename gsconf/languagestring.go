package gsconf

import (
	"math/rand/v2"

	"github.com/jfcwrlight/core/conf/basic"
	"github.com/jfcwrlight/core/conf/table"
)

var (
	_ = table.Add(parseLanguageString)
)

type LanguageStringConf struct {
	TID string
	Zh  string `json:"zh"`
	En  string `json:"en"`
	Pt  string `json:"pt"`
	Vn  string `json:"vn"`
	Rus string `json:"rus"`
	Esp string `json:"esp"`
}

func (t LanguageStringConf) GetID() int32 {
	return rand.Int32()
}

func (t LanguageStringConf) TableName() string {
	return "LanguageString"
}

func parseLanguageString(raw basic.Raw) (*LanguageStringConf, error) {
	return table.Default[LanguageStringConf](raw)
}
