package gsconf

import (
	"github.com/jfcwrlight/core/conf/basic"
	"github.com/jfcwrlight/core/conf/table"
)

var (
	_ = table.Add(parsePropsInfo)
)

type PropsInfoConf struct {
	ID       int32
	Describe string
}

func (t PropsInfoConf) GetID() int32 {
	return t.ID
}

func (t PropsInfoConf) TableName() string {
	return "PropsInfo"
}

func parsePropsInfo(raw basic.Raw) (*PropsInfoConf, error) {
	return table.Default[PropsInfoConf](raw)
}
