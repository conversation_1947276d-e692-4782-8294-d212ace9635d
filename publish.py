#!/usr/bin/env python3
import os
import sys
import subprocess
import shutil
from pathlib import Path

# Define the services and their corresponding directories
SERVICES = {
    'account': 'service/account',
    'defi': 'service/defi',
    'game': 'service/game',
    'history': 'service/game',
    'gate': 'service/gate',
    'gamedoor': 'service/gamedoor',
    'lobby': 'service/lobby',
    'order': 'service/order',
    'blockhash': 'service/blockhash',
    'hub': 'service/hub',
    'gms': 'service/gms',
    'telegram': 'service/telegram',
    'thirdapp': 'service/thirdapp',
    'statistics': 'service/statistics'
}

def build_service(service_dir):
    """Build the service in the specified directory."""
    print(f"Building {service_dir}...")
    try:
        result = subprocess.run(['go', 'build', '-v', '-o', 'server'], 
                              cwd=service_dir, 
                              capture_output=True, 
                              text=True)
        if result.returncode != 0:
            print(f"Error building {service_dir}:")
            print(result.stderr)
            return False
        print(f"Successfully built {service_dir}")
        return True
    except Exception as e:
        print(f"Error building {service_dir}: {str(e)}")
        return False

def move_server_file(service_dir, service_name):
    """Move the built server file to the release directory."""
    source = os.path.join(service_dir, 'server')
    target_dir = os.path.join('datax.release', service_name)
    target = os.path.join(target_dir, 'server')

    # Create target directory if it doesn't exist
    os.makedirs(target_dir, exist_ok=True)

    try:
        shutil.move(source, target)
        print(f"Successfully moved server file to {target}")
        return True
    except Exception as e:
        print(f"Error moving server file for {service_name}: {str(e)}")
        return False

def process_service(service_name):
    """Process a single service: build and move the server file."""
    if service_name not in SERVICES:
        print(f"Unknown service: {service_name}")
        return False

    service_dir = SERVICES[service_name]
    if not os.path.exists(service_dir):
        print(f"Service directory not found: {service_dir}")
        return False

    if not build_service(service_dir):
        return False

    return move_server_file(service_dir, service_name)

def main():
    if len(sys.argv) < 2:
        # If no arguments provided, process all services
        services_to_process = list(SERVICES.keys())
    else:
        # Process only the specified services
        services_to_process = sys.argv[1:]

    success = True
    for service in services_to_process:
        if not process_service(service):
            success = False
            print(f"Failed to process service: {service}")

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main() 