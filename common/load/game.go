package load

import (
	"igame"
	"igameKing"
	"s2/gsconf"

	"github.com/jfcwrlight/core/conf/table"
)

func Game() {
	allGame := table.GetALL[gsconf.GameInfoConf]()
	for _, game := range allGame {
		for _, mode := range game.AlgoMode {
			if mode == -1 {
				continue
			}
			if game.Platform == "xking" {
				igameKing.InitFromSnapshoot(game.ID, mode)
			} else {
				igame.InitFromSnapshoot(game.ID, mode)
			}
		}
	}
}
