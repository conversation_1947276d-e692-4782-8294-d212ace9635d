package common

import (
	"bytes"
	"compress/gzip"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"s2/pb"
	"strconv"
	"time"

	"github.com/jfcwrlight/core/utils"
)

func GenToken() string {
	bytes := make([]byte, 32)
	_, err := rand.Read(bytes)
	if err != nil {
		panic(err)
	}
	return hex.EncodeToString(bytes)
}

func FormatMsgIgnoreByCode(msg any) string {
	body, ok := msg.(interface{ GetCode() pb.ErrCode })
	if !ok || body.GetCode() != pb.SUCCESS {
		return utils.FormatMsg(msg)
	}
	return "[OK...]"
}

func Days(t ...time.Time) int32 {
	if len(t) == 0 {
		t = []time.Time{time.Now()}
	}
	y, m, d := t[0].Date()
	return int32(y)*10000 + int32(m)*100 + int32(d)
}

func Months(t ...time.Time) int32 {
	if len(t) == 0 {
		t = []time.Time{time.Now()}
	}
	y, m, _ := t[0].Date()
	return int32(y)*100 + int32(m)
}

func UTC0() time.Time {
	now := time.Now().UTC()
	year, month, day := now.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, time.UTC)
}

func GenerateSecureRandomString(length int) string {
	bytes := make([]byte, length)
	_, err := rand.Read(bytes)
	if err != nil {
		return ""
	}
	return base64.URLEncoding.EncodeToString(bytes)[:length]
}

// 通过玩家基础数据模拟生成telegram登陆token
func CreateTelegramLoginToken(id int64, name string, botToken string) (string, error) {
	query_id := GenerateSecureRandomString(24)
	if query_id == "" {
		return "", errors.New("query_id == nil")
	}
	user_ := map[string]any{
		"id":         id,
		"first_name": name,
		"last_name":  "",
	}
	user_B, err := json.Marshal(user_)
	if err != nil {
		return "", err
	}
	user := string(user_B)
	auth_date := strconv.FormatInt(time.Now().Unix(), 10)
	data_check_string := "auth_date=" + auth_date + "\nquery_id=" + query_id + "\nuser=" + user
	secret_key := HmacSha256("WebAppData", botToken)
	hash_ := HmacSha256(string(secret_key), data_check_string)
	hash := hex.EncodeToString(hash_)
	s := "query_id=" + query_id + "&user=" + user + "&auth_date=" + auth_date + "&hash=" + hash
	return s, nil
}

// HmacSha256 计算HmacSha256
// key 是加密所使用的key
// data 是加密的内容
func HmacSha256(key string, data string) []byte {
	mac := hmac.New(sha256.New, []byte(key))
	_, _ = mac.Write([]byte(data))

	return mac.Sum(nil)
}

func NewEnum[T any]() T {
	var enum T
	valueOf := reflect.ValueOf(&enum)
	typeOf := reflect.TypeOf(&enum)
	valueOf = valueOf.Elem()
	typeOf = typeOf.Elem()
	for i := 0; i < valueOf.NumField(); i++ {
		field := valueOf.Field(i)
		fieldType := typeOf.Field(i)
		if !field.CanSet() {
			continue
		}
		switch k := field.Kind(); k {
		default:
			panic("enum field type must be string or int")
		case reflect.String:
			field.SetString(fieldType.Name)
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			field.SetInt(int64(i))
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			field.SetUint(uint64(i))
		}
	}

	return enum
}

func CompressAndBase64(data []byte) string {
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	_, err := gzipWriter.Write(data)
	if err != nil {
		panic(err)
	}
	gzipWriter.Close()

	return base64.StdEncoding.EncodeToString(buf.Bytes())
}

func Int64ToHex(id int64) string {
	// 使用 strconv.FormatInt 将 int64 转为 16 进制字符串
	// 16 表示使用 16 进制，输出为小写
	return strconv.FormatInt(id, 16)
}

func HexToInt64(s string) (int64, error) {
	// 使用 strconv.ParseInt 将 16 进制字符串转为 int64
	// 16 表示输入是 16 进制，64 表示返回 int64 类型
	id, err := strconv.ParseInt(s, 16, 64)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// ParseStatisticsDateRange 解析统计日期范围
func ParseStatisticsDateRange(startDateStr, endDateStr string) (int32, int32, error) {
	// 如果没有提供日期，默认查询所有
	if startDateStr == "" || endDateStr == "" {
		return 0, 0, nil
	}

	// 解析开始日期
	startTime, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid start date format: %v", err)
	}

	// 解析结束日期
	endTime, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid end date format: %v", err)
	}

	startDate := Days(startTime)
	endDate := Days(endTime)

	return startDate, endDate, nil
}

// DateYYYYMMDDToUnix 把 "YYYYMMDD" 格式的日期字符串转为 Unix 时间戳（秒）
func DateYYYYMMDDToUnix(s string) (int64, error) {
	t, err := time.Parse("20060102", s)
	if err != nil {
		return 0, err
	}
	return t.Unix(), nil
}
