package s2c

import (
	"s2/define"
	"s2/pb"

	"github.com/gogo/protobuf/proto"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/message/codec"
)

func Broadcast(msg proto.Message) {
	b := codec.Encode(msg)
	message.Broadcast(define.ModuleName.Door, &pb.S2CBroadcastMsg{
		Body: b,
	})
}

func Multicast(msg proto.Message, userIDs ...int64) {
	if len(userIDs) == 0 {
		return
	}
	b := codec.Encode(msg)
	message.Broadcast(define.ModuleName.Door, &pb.S2CMulticastMsg{
		UserIDs: userIDs,
		Body:    b,
	})
}
