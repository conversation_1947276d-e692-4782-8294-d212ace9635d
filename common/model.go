package common

import "s2/pb"

var TableNameNavigationModule = "navigation_modules"

// NavigationModule 导航模块数据模型
type NavigationModule struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement"`
	SortID      int32  `gorm:"column:sort_id;not null;default:0"`
	Section     string `gorm:"column:section;type:varchar(50);not null;default:''"`
	ModuleName  string `gorm:"column:module_name;type:varchar(100);not null"`
	LangID      string `gorm:"column:lang_id;type:varchar(100);not null;default:''"`
	ModuleID    string `gorm:"column:module_id;type:varchar(50);not null"`
	ModuleIcon  string `gorm:"column:module_icon;type:varchar(200);not null;default:''"`
	JumpAddress string `gorm:"column:jump_address;type:varchar(200);not null;default:''"`
	ParentID    int32  `gorm:"column:parent_id;not null;default:0"`
	IsActive    bool   `gorm:"column:is_active;not null;default:true"`
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime"`
}

func (NavigationModule) TableName() string {
	return TableNameNavigationModule
}

// ToPB 转换为protobuf结构
func (n *NavigationModule) ToPB() *pb.NavigationModule {
	return &pb.NavigationModule{
		ID:          n.ID,
		SortID:      n.SortID,
		Section:     n.Section,
		ModuleName:  n.ModuleName,
		LangID:      n.LangID,
		ModuleID:    n.ModuleID,
		ModuleIcon:  n.ModuleIcon,
		JumpAddress: n.JumpAddress,
		ParentID:    n.ParentID,
		IsActive:    n.IsActive,
	}
}
