package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"s2/common"
	"time"

	"github.com/bluele/gcache"
	"github.com/jfcwrlight/core/infra/rdb"
	"github.com/jfcwrlight/core/log"
	"github.com/redis/go-redis/v9"
)

func uidHashKey(uid int64) string {
	return fmt.Sprintf("user.basicInfo:{%d}", uid) // redis如果为集群分片是按{}中的值hash
}

func tokenToIDKey(token string) string {
	return fmt.Sprintf("user.tokenToID:{%s}", token) // redis如果为集群分片是按{}中的值hash
}

func idToTokenKey(uid int64) string {
	return fmt.Sprintf("user.IDToToken:{%d}", uid)
}

func UpdateOrSetUserToken(uid int64) (string, error) {
	ctx := context.Background()
	token, err := rdb.Default().Get(ctx, idToTokenKey(uid)).Result()
	if err != redis.Nil {
		return token, err
	}
	expiration := 7 * 24 * time.Hour
	expiration = -1
	token = common.GenToken()
	pipe := rdb.Default().TxPipeline()
	pipe.Set(ctx, tokenToIDKey(token), uid, expiration)
	pipe.Set(ctx, idToTokenKey(uid), token, expiration)
	_, err = pipe.Exec(ctx)
	if err != nil {
		log.Error(err)
		return "", err
	}
	return token, nil
}

type UserBasicInfo struct {
	ID        int64
	ServerID  uint32
	Address   string
	Email     string
	Telegram  int64
	XId       string
	DiscordId string
	HeadURL   string
	Name      string
	RegTime   int64
	Channel   string
	ParentID  int64
	IsRobot   bool
	ZoneID    map[uint32]uint32
}

var (
	ucache = gcache.New(5000).Expiration(time.Minute).LRU().Build()
	tcache = gcache.New(5000).Expiration(time.Hour).LRU().Build()
)

// 查询, 有缓存用缓存
func QueryUserBasicInfo(uid int64) (*UserBasicInfo, error) {
	if bc, err := ucache.Get(uid); err == nil {
		return bc.(*UserBasicInfo), nil
	}
	return QueryUserBasicInfoWithUpdate(uid)
}

func QueryUserBasicInfoByToken(token string) (*UserBasicInfo, error) {
	var uid int64
	if v, err := tcache.Get(token); err == nil {
		uid = v.(int64)
	} else {
		n, err := rdb.Default().Get(context.Background(), tokenToIDKey(token)).Int64()
		if err != nil {
			return nil, err
		}
		uid = n
	}
	return QueryUserBasicInfo(uid)
}

func QueryUserBasicInfoN(uids ...int64) (map[int64]*UserBasicInfo, error) {
	bcs := make(map[int64]*UserBasicInfo, len(uids))
	var loadKeys []string
	for _, uid := range uids {
		if bc, err := ucache.Get(uid); err == nil {
			bcs[uid] = bc.(*UserBasicInfo)
			continue
		}
		loadKeys = append(loadKeys, uidHashKey(uid))
	}
	if len(loadKeys) <= 0 {
		return bcs, nil
	}
	bs, err := rdb.Default().MGet(context.Background(), loadKeys...).Result()
	if err != nil {
		return nil, err
	}
	for _, item := range bs {
		text, ok := item.(string)
		if !ok {
			continue
		}
		bc := &UserBasicInfo{}
		err = json.Unmarshal([]byte(text), bc)
		if err != nil {
			log.Error(err)
			continue
		}
		ucache.Set(bc.ID, bc)
		bcs[bc.ID] = bc
	}
	return bcs, nil
}

// 查询前先更新
func QueryUserBasicInfoWithUpdate(uid int64) (*UserBasicInfo, error) {
	key := uidHashKey(uid)
	data, err := rdb.Default().Get(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}
	bc := &UserBasicInfo{}
	err = json.Unmarshal([]byte(data), bc)
	if err != nil {
		return nil, err
	}
	ucache.Set(key, bc)
	return bc, nil
}

func SaveUserBasicInfo(bc *UserBasicInfo) error {
	key := uidHashKey(bc.ID)
	b, _ := json.Marshal(bc)
	err := rdb.Default().Set(context.Background(), key, b, 30*24*time.Hour).Err()
	return err
}
