package common

type GameStatistics struct {
	GameID       string
	Input        float64
	Output       float64
	Jackpot      float64
	JackpotCount int64
	Bonus        float64
	BonusCount   int64
}

type SeatStatistics struct {
	GameID       string
	SeatID       int32
	Input        float64
	Output       float64
	Jackpot      float64
	JackpotCount int64
	Bonus        float64
	BonusCount   int64
}
type StatsCache struct {
	GameStats map[string]*GameStatistics
	SeatStats map[string]map[int32]*SeatStatistics
}
