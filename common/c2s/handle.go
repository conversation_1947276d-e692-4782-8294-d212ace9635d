package c2s

import (
	"encoding/json"
	"reflect"
	"s2/common"
	"s2/pb"
	"time"

	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message/codec"
	"github.com/jfcwrlight/core/utils"
)

var (
	// msgHandle   = map[reflect.Type]func(msg any){}
	msgResponse = map[reflect.Type]func(msg any, response func(any, error)){}
	// registry for handlers that need IRequestContext
	msgResponseCtx = map[reflect.Type]func(msg any, ctx *pb.C2SPackageReq, response func(any, error)){}
)

func Enable(m iface.IModule) {
	codec.Register[pb.C2SPackageReq]()
	m.Response(reflect.TypeFor[*pb.C2SPackageReq](), onC2SPackageReq)
}

func Response[T1 any, T2 any](h func(req *T1, response func(*T2, error))) {
	mType := reflect.TypeFor[*T1]()
	if _, ok := msgResponse[mType]; ok {
		panic("msg handler already exists")
	}
	codec.Register[T1]()
	// 注册消息处理函数
	msgResponse[mType] = func(msg any, response func(any, error)) {
		since := time.Now()
		h(msg.(*T1), func(resp *T2, err error) {
			response(resp, err)
			log.Infof("Response C2S %s, %s, %s", utils.FormatMsg(msg), common.FormatMsgIgnoreByCode(resp), time.Since(since))
		})
	}
}

// ResponseCtx registers a handler that also receives the IRequestContext (to access metadata like IP/Device)
func ResponseCtx[T1 any, T2 any](h func(req *T1, ctx *pb.C2SPackageReq, response func(*T2, error))) {
	mType := reflect.TypeFor[*T1]()
	if _, ok := msgResponse[mType]; ok {
		panic("msg handler already exists; consider using Response instead")
	}
	if _, ok := msgResponseCtx[mType]; ok {
		panic("msg handler already exists in ctx registry")
	}
	codec.Register[T1]()
	msgResponseCtx[mType] = func(msg any, ctx *pb.C2SPackageReq, response func(any, error)) {
		since := time.Now()
		h(msg.(*T1), ctx, func(resp *T2, err error) {
			response(resp, err)
			log.Infof("Response C2S %s, %s, %s", utils.FormatMsg(msg), common.FormatMsgIgnoreByCode(resp), time.Since(since))
		})
	}
}

// func HandleC2S[T any](h func(req *T)) {
// 	mType := reflect.TypeFor[*T]()
// 	if _, ok := msgResponse[mType]; ok {
// 		panic("msg handler already exists")
// 	}
// 	codec.Register[T]()
// 	// 注册消息处理函数
// 	msgHandle[mType] = func(msg any) {
// 		since := time.Now()
// 		h(msg.(*T))
// 		log.Infof("Handle c2s %s, %s", utils.FormatMsg(msg), time.Since(since))
// 	}
// }

func onC2SPackageReq(ctx iface.IRequestContext) {
	body := ctx.Body().(*pb.C2SPackageReq)
	req, err := codec.Decode(body.Body)
	if err != nil {
		log.Errorf("onC2SPackage decode error %s", err)
		return
	}

	t := reflect.TypeOf(req)
	// if there's a context-aware handler, dispatch it
	if fCtx, ok := msgResponseCtx[t]; ok {
		fCtx(req, body, func(resp any, err error) {
			b, _ := json.Marshal(resp)
			ctx.Return(&pb.C2SPackageResp{Body: b}, nil)
		})
		return
	}
	// otherwise use the standard handler
	h := msgResponse[t]
	h(req, func(resp any, err error) {
		b, _ := json.Marshal(resp)
		ctx.Return(&pb.C2SPackageResp{Body: b}, nil)
	})
}
