<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Result Verifier</title>
  <style>
    :root {
      --primary: #007bff;
      --primary-dark: #0056b3;
      --bg-color: #f9f9f9;
      --text-color: #333;
      --border-color: #ccc;
      --accent: #17a2b8;
    }
    body {
      font-family: sans-serif;
      max-width: 800px;
      margin: 2em auto;
      background: var(--bg-color);
      color: var(--text-color);
    }

    fieldset {
      margin-bottom: 1.5em;
      padding: 1em;
      background: #fff;
      border: 1px solid var(--border-color);
      border-radius: 0.5em;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    legend {
      font-weight: bold;
      background: var(--accent);
      color: #fff;
      padding: 0.2em 0.5em;
      border-radius: 0.3em;
    }

    label {
      display: block;
      margin: 0.5em 0 0.2em;
      font-weight: 500;
    }

    input,
    textarea,
    button,
    select {
      width: 100%;
      box-sizing: border-box;
      border: 1px solid var(--border-color);
      border-radius: 0.3em;
      padding: 0.5em;
    }

    textarea {
      height: 4em;
      font-family: monospace;
    }

    select {
      background: #fff;
    }

    .inline {
      display: inline-block;
      width: auto;
      margin-left: 0.5em;
    }

    button {
      background: var(--primary);
      color: #fff;
      border: none;
      cursor: pointer;
      transition: background 0.2s;
    }
    button:hover {
      background: var(--primary-dark);
    }

    /* Mobile responsive adjustments */
    @media (max-width: 600px) {
      body {
        margin: 1em;
        padding: 0 1em;
      }
      fieldset {
        padding: 0.5em;
      }
      input, textarea, button {
        font-size: 1em;
      }
      textarea {
        height: auto;
      }
    }

    /* Main title styling */
    h1#title {
      color: var(--accent);
      text-align: center;
      font-size: 2em;
      margin-bottom: 0.5em;
    }
  </style>
</head>

<body>
  <p id="description" style="color: #555; font-size: 0.9em;">Results are generated from a pre-generated policy and
    future block hash, cannot be predicted or changed.</p>
  <h1 id="title">Result Verifier</h1>

  <!-- Include ethers.js for deriving address from private key -->
  <script src="https://cdn.jsdelivr.net/npm/ethers@5.7.2/dist/ethers.umd.min.js"></script>

  <!-- STEP 1: NewPolicy -->
  <fieldset>
    <legend id="legend1">1. Generate Policy &amp; PrivateKey</legend>
    <label id="labelChain">Chain name (chain):<input id="chain" value="OPBNB" /></label>
    <label id="labelHeight">Future block height (height):<input id="height" type="number" value="65713021" /></label>
    <label id="labelRandHex">Random number (8-byte HEX):<input id="randHex"
        placeholder="Enter HEX to use, otherwise generated randomly" /></label>
    <button id="btnPolicy">Generate Policy</button>
    <label id="labelOutPolicy">Policy address (policy):</label>
    <textarea id="outPolicy" readonly></textarea>
    <label id="labelOutPrivateKey">PrivateKey:</label>
    <textarea id="outPrivateKey" readonly></textarea>
    <label id="labelOutPolicyRaw">Calculation Process:</label>
    <textarea id="outPolicyRaw" readonly></textarea>
  </fieldset>

  <!-- STEP 2: NewOracleKey -->
  <fieldset>
    <legend id="legend2">2. Generate OracleKey</legend>
    <label id="labelInpPrivateKey">Policy privateKey (privateKey):<input id="inpPrivateKey" /></label>
    <label id="labelBlockHash">Future block hash (blockHash):<button id="btnBlockQuery" class="inline">Query
        Block</button><input id="blockHash" placeholder="Enter blockHash" style="width:100%; height:3em;" /></label>
    <button id="btnOracle">Compute OracleKey</button>
    <label id="labelOutOracleKey">OracleKey:</label>
    <textarea id="outOracleKey" readonly></textarea>
    <label id="labelOutOracleRaw">Calculation Process:</label>
    <textarea id="outOracleRaw" readonly></textarea>
  </fieldset>

  <!-- STEP 3: GetOracleSeedFromKey -->
  <fieldset>
    <legend id="legend3">3. Extract Seed</legend>
    <label id="labelInpOracleKey">OracleKey:<input id="inpOracleKey" /></label>
    <button id="btnSeed">Extract Seed</button>
    <label id="labelOutSeed">Seed (int64):</label>
    <textarea id="outSeed" readonly></textarea>
    <label id="labelOutSeedRaw">Calculation Process:</label>
    <textarea id="outSeedRaw" readonly></textarea>
  </fieldset>

  <!-- STEP 4: Spin -->
  <fieldset>
    <legend id="legend4">4. Simulate Spin</legend>
    <label id="labelGameID">GameID:<select id="gameID"></select></label>
    <label id="labelMode">Mode:<select id="mode"></select></label>
    <label id="labelSpinSeed">Seed:<input id="spinSeed" /></label>
    <button id="btnSpin">Compute Spin</button>
    <label id="labelPayout">Payouts:</label>
    <input id="outPayout" readonly></input>
    <!-- <label id="labelLine">Lines:</label>
    <input id="outLine" readonly></input> -->
  </fieldset>

  <script>
    // Multi-language support
    const urlParams = new URLSearchParams(window.location.search);
    const langParam = urlParams.get('lang');
    const lang = langParam === 'zh' ? 'zh' : 'en';
    const i18n = {
      en: {
        title: 'Result Verifier',
        description: 'Results are generated from a pre-generated policy and future block hash, cannot be predicted or changed.',
        legend1: '1. Generate Policy & PrivateKey',
        chainLabel: 'Chain name (chain):',
        heightLabel: 'Future block height (height):',
        randLabel: 'Random number (8-byte HEX):',
        randPlaceholder: 'Enter HEX to use, otherwise generated randomly',
        btnPolicy: 'Generate Policy',
        policyAddressLabel: 'Policy address (policy):',
        privateKeyLabel: 'Policy PrivateKey:',
        rawHexLabel: 'Calculation Process:',
        legend2: '2. Generate OracleKey',
        inpPrivateKeyLabel: 'Policy privateKey (privateKey):',
        blockHashLabel: 'Future block hash (blockHash):',
        btnBlockQuery: 'Query Block',
        btnOracle: 'Compute OracleKey',
        oracleKeyLabel: 'OracleKey:',
        rawConcatLabel: 'Calculation Process:',
        legend3: '3. Extract Seed',
        inpOracleKeyLabel: 'OracleKey:',
        btnSeed: 'Extract Seed',
        seedLabel: 'Seed (int64):',
        hexPartsLabel: 'Calculation Process:',
        legend4: '4. Simulate Spin',
        gameIDLabel: 'GameID:',
        modeLabel: 'Mode:',
        seedInputLabel: 'Seed:',
        btnSpin: 'Compute Spin',
        lineLabel: 'Lines:',
        payoutLabel: 'Payouts:',
        invalidBlockHash: 'Block hash cannot be empty. Click the Query button to fetch block hash.'
      },
      zh: {
        title: '结果验证器',
        description: '结果由预生成保单+未来区块hash生成，结果无法预测无法更改。',
        legend1: '1. 生成 Policy & PrivateKey',
        chainLabel: '链名 (chain)：',
        heightLabel: '未来区块高度 (height)：',
        randLabel: '随机数 (8 字节 HEX)：',
        randPlaceholder: '如填写则使用，否则随机生成',
        btnPolicy: '生成 Policy',
        policyAddressLabel: '保单地址 (policy)：',
        privateKeyLabel: '保单私钥 (privateKey)：',
        rawHexLabel: '计算过程：',
        legend2: '2. 生成 OracleKey',
        inpPrivateKeyLabel: '保单私钥 (privateKey)：',
        blockHashLabel: '未来区块哈希 (blockHash)：',
        btnBlockQuery: '查询区块',
        btnOracle: '计算 OracleKey',
        oracleKeyLabel: 'OracleKey：',
        rawConcatLabel: '计算过程：',
        legend3: '3. 提取 Seed',
        inpOracleKeyLabel: 'OracleKey：',
        btnSeed: '提取 Seed',
        seedLabel: 'Seed (int64)：',
        hexPartsLabel: '计算过程：',
        legend4: '4. 模拟 Spin',
        gameIDLabel: 'GameID：',
        modeLabel: 'Mode：',
        seedInputLabel: 'Seed：',
        btnSpin: '计算 Spin',
        lineLabel: '线数：',
        payoutLabel: '赔率：',
        invalidBlockHash: '区块Hash不能为空，点击查询按钮查询区块Hash'
      }
    };
    function translate() {
      const t = i18n[lang];
      document.title = t.title;
      document.getElementById('description').textContent = t.description;
      document.getElementById('title').textContent = t.title;
      document.getElementById('legend1').textContent = t.legend1;
      document.getElementById('labelChain').firstChild.textContent = t.chainLabel;
      document.getElementById('labelHeight').firstChild.textContent = t.heightLabel;
      document.getElementById('labelRandHex').firstChild.textContent = t.randLabel;
      document.getElementById('randHex').placeholder = t.randPlaceholder;
      document.getElementById('btnPolicy').textContent = t.btnPolicy;
      document.getElementById('labelOutPolicy').firstChild.textContent = t.policyAddressLabel;
      document.getElementById('labelOutPrivateKey').firstChild.textContent = t.privateKeyLabel;
      document.getElementById('labelOutPolicyRaw').firstChild.textContent = t.rawHexLabel;
      document.getElementById('legend2').textContent = t.legend2;
      document.getElementById('labelInpPrivateKey').firstChild.textContent = t.inpPrivateKeyLabel;
      document.getElementById('labelBlockHash').firstChild.textContent = t.blockHashLabel;
      document.getElementById('btnBlockQuery').textContent = t.btnBlockQuery;
      document.getElementById('btnOracle').textContent = t.btnOracle;
      document.getElementById('labelOutOracleKey').firstChild.textContent = t.oracleKeyLabel;
      document.getElementById('labelOutOracleRaw').firstChild.textContent = t.rawConcatLabel;
      document.getElementById('legend3').textContent = t.legend3;
      document.getElementById('labelInpOracleKey').firstChild.textContent = t.inpOracleKeyLabel;
      document.getElementById('btnSeed').textContent = t.btnSeed;
      document.getElementById('labelOutSeed').firstChild.textContent = t.seedLabel;
      document.getElementById('labelOutSeedRaw').firstChild.textContent = t.hexPartsLabel;
      document.getElementById('legend4').textContent = t.legend4;
      document.getElementById('labelGameID').firstChild.textContent = t.gameIDLabel;
      document.getElementById('labelMode').firstChild.textContent = t.modeLabel;
      document.getElementById('labelSpinSeed').firstChild.textContent = t.seedInputLabel;
      document.getElementById('btnSpin').textContent = t.btnSpin;
      document.getElementById('labelLine').firstChild.textContent = t.lineLabel;
      document.getElementById('labelPayout').firstChild.textContent = t.payoutLabel;
    }
    window.addEventListener('DOMContentLoaded', translate);

    // Utility: Uint8Array → HEX string
    function hexEncode(buf) {
      return Array.from(buf).map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // STEP1: NewPolicy
    document.getElementById('btnPolicy').onclick = () => {
      const chain = document.getElementById('chain').value;
      const height = BigInt(document.getElementById('height').value);
      // Get user input random HEX or generate new
      let randHex = document.getElementById('randHex').value.trim();
      const randBuf = new Uint8Array(8);
      if (/^[0-9a-fA-F]{16}$/.test(randHex)) {
        for (let i = 0; i < 8; i++) {
          randBuf[i] = parseInt(randHex.substr(i * 2, 2), 16);
        }
      } else {
        crypto.getRandomValues(randBuf);
        randHex = hexEncode(randBuf);
        document.getElementById('randHex').value = randHex;
      }
      // 1. 16 bytes chain name
      const encoder = new TextEncoder();
      const chainBytes = encoder.encode(chain);
      const buf = new Uint8Array(16 + 8 + 8);
      buf.fill(0);
      buf.set(chainBytes.slice(0, 16), 0);
      // 2. Height BE encoding
      new DataView(buf.buffer).setBigUint64(16, height, false);
      // 3. Random number
      buf.set(randBuf, 24);
      const rawHex = hexEncode(buf);
      const privateKey = '0x' + rawHex;
      // 4. Derive address
      const wallet = new ethers.Wallet(privateKey);
      // show pseudocode of concatenation of parts for clarity
      const partChain = rawHex.substring(0, 32);
      const partHeight = rawHex.substring(32, 48);
      const partRand = rawHex.substring(48, 64);
      document.getElementById('outPolicyRaw').value = `Policy PrivateKey = concat(chainBytes=${partChain}=encoder.encode(${chain}), heightBE=${partHeight}=hex(${height}), rand=${partRand})\n` + `Policy Address = (new ethers.Wallet(privateKey)).address`;
      document.getElementById('outPrivateKey').value = privateKey;
      document.getElementById('outPolicy').value = wallet.address;
      document.getElementById('inpPrivateKey').value = privateKey;
    };

    // STEP2: Add block hash query button
    document.getElementById('btnBlockQuery').onclick = () => {
      const blk = document.getElementById('height').value;
      window.open(`https://opbnbscan.com/block/${blk}`);  // Reference: https://opbnbscan.com/block/📑
    };

    // STEP2: NewOracleKey
    document.getElementById('btnOracle').onclick = () => {
      const privateKey = document.getElementById('inpPrivateKey').value;
      const blockHash = document.getElementById('blockHash').value;
      if (!blockHash || blockHash.length < 10) {
        alert(i18n[lang].invalidBlockHash);
        return;
      }
      const wallet = new ethers.Wallet(privateKey);
      const address = wallet.address;
      const part1 = address.slice(-32);
      const part2 = privateKey.slice(-16);
      const part3 = blockHash.slice(-16);
      const oracleKey = part1 + part2 + part3;
      document.getElementById('outOracleRaw').value = `OracleKey = concat(addressSuffix=${part1}, privateKeySuffix=${part2}, blockHashSuffix=${part3})`;
      document.getElementById('outOracleKey').value = oracleKey;
      document.getElementById('inpOracleKey').value = oracleKey;
    };

    // STEP3: GetOracleSeedFromKey
    document.getElementById('btnSeed').onclick = () => {
      const oracleKey = document.getElementById('inpOracleKey').value;
      const wallet = new ethers.Wallet(oracleKey);
      const addrHex = wallet.address.slice(2);
      const sLen = addrHex.length;
      const h1 = addrHex.slice(sLen - 16);
      const h2 = addrHex.slice(sLen - 32, sLen - 16);
      const h3 = addrHex.slice(0, 8); 
      const u1 = BigInt('0x' + h1);
      const u2 = BigInt('0x' + h2);
      const u3 = BigInt('0x' + h3);
      const seedBig = (u1 ^ u2 ^ u3) & BigInt('0x7fffffffffffffff');
      // show pseudocode of seed extraction for clarity
      document.getElementById('outSeedRaw').value =
        `ethers.Wallet(oracleKey) = ${addrHex}\n` +
        `u1 = BigInt('${h1}')\n` +
        `u2 = BigInt('${h2}')\n` +
        `u3 = BigInt('${h3}')\n` +
        `seed = (u1 ^ u2 ^ u3) & BigInt('0x7fffffffffffffff')`;
      document.getElementById('outSeed').value = seedBig.toString();
      document.getElementById('spinSeed').value = seedBig.toString();
    };

    // STEP4: Call backend to simulate Spin (keep original design)
    document.getElementById('btnSpin').onclick = async () => {
      const params = new URLSearchParams(window.location.search);
      const apiUrl = params.get('apiUrl');
      const gameid = +document.getElementById('gameID').value;
      const mode = +document.getElementById('mode').value;
      const seed = document.getElementById('spinSeed').value;
      const resp = await fetch(apiUrl + 'spin', {
        method: 'POST', headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ gameid, mode, seed })
      }).then(r => r.json());
      console.log(resp);
      // document.getElementById('outLine').value = resp.Line + "";
      document.getElementById('outPayout').value = (resp.Payout/resp.Line) + "";
    };

    // Auto-parse URL parameters
    (function () {
      const params = new URLSearchParams(window.location.search);
      const pk = params.get('privateKey');
      if (pk) {
        const rawHex = pk.startsWith('0x') ? pk.slice(2) : pk;
        // Convert to bytes
        const bytes = rawHex.match(/.{2}/g).map(b => parseInt(b, 16));
        const buf = new Uint8Array(bytes);
        // Parse chain name
        let chainStr = '';
        for (let i = 0; i < 16; i++) {
          if (buf[i] === 0) break;
          chainStr += String.fromCharCode(buf[i]);
        }
        document.getElementById('chain').value = chainStr;
        // Parse height
        const height = new DataView(buf.buffer).getBigUint64(16, false);
        document.getElementById('height').value = height.toString();
        // Parse random number
        const randArr = buf.slice(24, 32);
        const randHexParsed = hexEncode(randArr);
        document.getElementById('randHex').value = randHexParsed;
        // Fill private key and trigger Policy generation
        const fullPk = pk.startsWith('0x') ? pk : '0x' + pk;
        document.getElementById('inpPrivateKey').value = fullPk;
        document.getElementById('randHex').value = randHexParsed;
        document.getElementById('btnPolicy').click();
        const hash = params.get('hash');
        if (hash) {
          document.getElementById('blockHash').value = hash;
        }
      }
          })();

          // Populate game and mode dropdowns
          (async function () {
            const params = new URLSearchParams(window.location.search);
            const apiUrl = params.get('apiUrl');
            if (!apiUrl) return;
            try {
              const res = await fetch(apiUrl + 'games', {
                method: 'POST', headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({})
              }).then(r => r.json());
              const list = res.list || [];
              const gameSelect = document.getElementById('gameID');
              const modeSelect = document.getElementById('mode');
              list.forEach(game => {
                const opt = document.createElement('option');
                opt.value = game.gameid;
                opt.textContent = game.name;
                opt.dataset.modes = JSON.stringify(game.modes);
                gameSelect.appendChild(opt);
              });
              function updateModes() {
                modeSelect.innerHTML = '';
                const selectedOption = gameSelect.options[gameSelect.selectedIndex];
                if (!selectedOption) return;
                const modes = JSON.parse(selectedOption.dataset.modes);
                modes.forEach(m => {
                  const mo = document.createElement('option');
                  mo.value = m;
                  mo.textContent = m;
                  modeSelect.appendChild(mo);
                });
              }
              gameSelect.addEventListener('change', updateModes);
              if (gameSelect.options.length > 0) {
              const gameIdParam = params.get('gameId');
              if (gameIdParam) document.getElementById('gameID').value = gameIdParam;
              else gameSelect.selectedIndex = 0;
              updateModes();
              const modeParam = params.get('mode');
              if (modeParam) document.getElementById('mode').value = modeParam;
            }
          } catch (e) {
            console.error('Failed to fetch games list', e);
          }
        })();
  </script>
</body>
</html>