package web3

import (
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"math"
	"math/rand"
	"strconv"
)

func NewPolicy(chain string, height int64) (string, string, error) {
	buf := make([]byte, 16, 32)
	copy(buf, []byte(chain[:min(16, len(chain))]))
	buf = binary.BigEndian.AppendUint64(buf, uint64(height)) // 区块高度
	buf = binary.BigEndian.AppendUint64(buf, rand.Uint64())  // 随机数
	privateKey := hex.EncodeToString(buf)
	policy, err := GetAddressFromPrivateKey(privateKey)
	if err != nil {
		return "", "", err
	}
	return policy, privateKey, nil
}

func NewOracleKey(policyPrivateKey string, blockhash string) (string, error) {
	address, err := GetAddressFromPrivateKey(policyPrivateKey)
	if err != nil {
		return "", err
	}
	privateKey := fmt.Sprintf("%s%s%s", address[len(address)-32:], policyPrivate<PERSON>ey[48:64], blockhash[len(blockhash)-16:])
	return privateKey, nil
}

func GetOracleSeedFromKey(key string) (int64, error) {
	address, err := GetAddressFromPrivateKey(key)
	if err != nil {
		return 0, err
	}
	sLen := len(address)
	u1, err := strconv.ParseUint(address[sLen-16:], 16, 64)
	if err != nil {
		return 0, err
	}
	u2, err := strconv.ParseUint(address[sLen-32:sLen-16], 16, 64)
	if err != nil {
		return 0, err
	}
	u3, err := strconv.ParseUint(address[2:10], 16, 64)
	if err != nil {
		return 0, err
	}
	return int64((u1 ^ u2 ^ u3) & math.MaxInt64), nil // mongo不直接支持uint64, 处理成安全的int64
}
