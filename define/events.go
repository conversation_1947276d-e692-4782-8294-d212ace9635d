package define

import (
	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/utils"
)

var SystemEvent = utils.NewEnum[struct {
	ServerLaunchFinish events.EmptyEvent
}]()

type EventEnterLobby struct {
	UserID int64
}

func (e EventEnterLobby) Name() string {
	return "EnterLobby"
}

func (e EventEnterLobby) GetUserID() int64 {
	return e.UserID
}

type EventUserOffline struct {
	UserID int64
}

func (e EventUserOffline) Name() string {
	return "UserOffline"
}

func (e EventUserOffline) GetUserID() int64 {
	return e.UserID
}
