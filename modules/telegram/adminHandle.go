package telegram

import (
	"fmt"
	"s2/common/cache"
	"s2/define"
	"s2/pb"

	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (m *module) GMOrderOperation(update *tgbotapi.Update, params []string) error {
	id := update.CallbackQuery.Message.Chat.ID
	gameUid, err := GetGameUid(id, "", 0)
	if err != nil {
		log.Error(err)
		return err
	}
	bc, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return err
	}
	if len(params) < 2 {
		return fmt.Errorf("invalid params")
	}
	var Operation int32 = 1
	if params[1] == "1" {
		Operation = 1
	} else if params[1] == "2" {
		Operation = 2
	} else {
		return fmt.Errorf("invalid status")
	}
	resp, err := message.RequestAny[pb.WithdrawOrderOperationResp](define.ModuleName.Order, &pb.WithdrawOrderOperationReq{
		UserID:    bc.ID,
		ID:        params[0],
		Operation: Operation,
	})
	if err != nil {
		log.Error(err)
		return err
	}
	if resp.Code != pb.SUCCESS {
		alert := tgbotapi.NewCallbackWithAlert(update.CallbackQuery.ID, "操作失败")
		if _, err2 := bot.Send(alert); err2 != nil {
			log.Error(err2)
		}
		return fmt.Errorf("failed to update order status: %s", resp.Code)
	}
	alert := tgbotapi.NewCallbackWithAlert(update.CallbackQuery.ID, "操作成功")
	if _, err := bot.Send(alert); err != nil {
		log.Error(err)
	}
	m.HistoryPage(update, []string{"WithdrawalChecking", "1", "Admin"})
	return nil
}
