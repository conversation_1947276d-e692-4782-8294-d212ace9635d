package telegram

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"s2/common/cache"
	"s2/pb"
	"strconv"
	"sync"
	"time"

	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/infra/rdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

type RedpacketData struct {
	Id              int64   `gorm:"column:id;index;primary_key" json:"id"` //红包编号
	OwnerUid        int64   `gorm:"column:ownerUid;index" json:"ownerUid"`
	OwnerPlatUid    int64   `gorm:"column:ownerPlatUid" json:"ownerPlatUid"`
	OwnerName       string  `gorm:"column:ownerName" json:"ownerName"`
	ChatId          int64   `gorm:"column:chatId" json:"chatId"`
	InlineMessageID string  `gorm:"column:inlineMessageID" json:"inlineMessageID"`
	OwnerUserName   string  `gorm:"column:ownerUserName" json:"ownerUserName"`
	Amount          float64 `gorm:"column:amount" json:"amount"`
	Num             int32   `gorm:"column:num" json:"num"`
	Time            int64   `gorm:"column:time" json:"time"`
	Details         string  `gorm:"column:details" json:"Details"`             //领取详情
	ExchangeRate    float64 `gorm:"column:exchangeRate" json:"exchangeRate"`   //对u的汇率
	CurrencyDes     string  `gorm:"column:currencyDes" json:"currencyDes"`     //币种显示内容
	Multiple        float64 `gorm:"column:multiple" json:"multiple"`           //体现所需流水倍数
	RedpacketType   int32   `gorm:"column:redpacketType" json:"redpacketType"` //红包类型 1:平均红包 2:随机红包
	AssetID         int32   `gorm:"column:assetID" json:"assetID"`             //货币ID
}
type ReceiveUser struct {
	Uid      int64
	Name     string
	UserName string
	Amount   float64
}

func TelegramNameToHref(name, userName string) string {
	if userName != "" {
		return " <a href=\"https://t.me/" + userName + "\">" + name + "</a>"
	}
	return name
}

type RedpacketMgr struct {
	id                  int64
	ownerUid            int64
	OwnerName           string
	OwnerPlatUid        int64
	ChatId              int64
	InlineMessageID     string
	OwnerUserName       string
	finished            bool              //缓存是否结束
	uidFinished         map[int64]float64 //缓存领取的用户
	uidVerificationCode map[int64]string  //缓存的验证码
	mutex               sync.Mutex
	Mutexext            sync.Mutex
}

var (
	redpacketMap    map[int64]*RedpacketMgr = map[int64]*RedpacketMgr{}
	redpacket_mutex sync.Mutex
	cacheData       map[int64]*RedpacketMgr = map[int64]*RedpacketMgr{} //数据变化需要给telegram消息更新做一下缓存
)

func AddCacheRedpacket(mgr *RedpacketMgr) {
	redpacket_mutex.Lock()
	defer redpacket_mutex.Unlock()
	cacheData[mgr.id] = mgr
}
func ClearCacheRedpacket() []*RedpacketMgr {
	redpacket_mutex.Lock()
	defer redpacket_mutex.Unlock()
	list := []*RedpacketMgr{}
	for k := range cacheData {
		list = append(list, cacheData[k])
		delete(cacheData, k)
	}
	return list
}

func GetRedpacketMgr(id int64) *RedpacketMgr {
	redpacket_mutex.Lock()
	defer redpacket_mutex.Unlock()
	if redpacketMap[id] != nil {
		return redpacketMap[id]
	}
	redpacket := &RedpacketMgr{id: id, uidFinished: map[int64]float64{}, uidVerificationCode: map[int64]string{}}
	data := redpacket.GetData()
	if data == nil {
		return nil
	}
	redpacket.ownerUid = data.OwnerUid
	redpacket.OwnerName = data.OwnerName
	redpacket.OwnerUserName = data.OwnerUserName
	redpacket.OwnerPlatUid = data.OwnerPlatUid
	redpacket.ChatId = data.ChatId
	redpacket.InlineMessageID = data.InlineMessageID
	redpacket.updateCache()
	redpacketMap[redpacket.id] = redpacket
	return redpacket
}

func AddRedpacket(data RedpacketData) error {
	redpacket_mutex.Lock()
	defer redpacket_mutex.Unlock()
	if data.Id == 0 || data.OwnerUid == 0 {
		return errors.New("Id and OwnerUid is nill")
	}
	if redpacketMap[data.Id] != nil {
		return errors.New("Id Already exists")
	}
	redpacket := &RedpacketMgr{id: data.Id, uidFinished: map[int64]float64{}, uidVerificationCode: map[int64]string{}}
	err := redpacket.AddData(data)
	if err != nil {
		return err
	}
	redpacket.ownerUid = data.OwnerUid
	redpacket.OwnerName = data.OwnerName
	redpacket.OwnerUserName = data.OwnerUserName
	redpacket.OwnerPlatUid = data.OwnerPlatUid
	redpacket.ChatId = data.ChatId
	redpacket.InlineMessageID = data.InlineMessageID
	redpacket.updateCache()
	redpacketMap[data.Id] = redpacket
	return nil
}

func GetOwnerRedpacket(ownerUid int64) []RedpacketData {
	var list []RedpacketData
	a := RedpacketMgr{}
	if err := mdb.Default().Table(a.TableName()).Where("`ownerUid` = ?", ownerUid).Find(&list).Error; err != nil {
		return list
	}
	return list
}
func (a *RedpacketMgr) TableName() string {
	return "telegram_redpacket"
}

func (a *RedpacketMgr) AutoMigrate() {
	_ = mdb.Default().Table(a.TableName()).AutoMigrate(&RedpacketData{})
}

func (a *RedpacketMgr) GetData() *RedpacketData {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	return a.getData()
}

func (a *RedpacketMgr) GetOwnerUid() int64 {
	return a.ownerUid
}

func (a *RedpacketMgr) GetId() int64 {
	return a.id
}
func (a *RedpacketMgr) getData() *RedpacketData {
	var data RedpacketData
	if a.id != 0 {
		err := mdb.Default().Table(a.TableName()).Where("`id` = ?", a.id).Find(&data).Error
		if err != nil {
			return nil
		}
	} else {
		return nil
	}
	return &data
}

func (a *RedpacketMgr) AddData(data RedpacketData) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	data.Time = time.Now().UTC().Unix()
	if err := mdb.Default().Table(a.TableName()).Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (a *RedpacketMgr) GetReceiveUser(s string) []ReceiveUser {
	b := []ReceiveUser{}
	if s == "" {
		return b
	}
	json.Unmarshal([]byte(s), &b)
	return b
}

func (a *RedpacketMgr) ReceiveUserToString(b []ReceiveUser) string {
	s, _ := json.Marshal(b)
	return string(s)
}

var RedisKey_VerificationCode = "TelegramBot:Redpacket:VerificationCode:"

// callback *tgbotapi.CallbackQuery
// 获取验证码
func (a *RedpacketMgr) GetVerificationCode(uid int64) (string, error) {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	if a.finished {
		return "", fmt.Errorf("红包领完了")
	}
	if a.uidFinished[uid] > 0 {
		return "", fmt.Errorf("该红包您已经领取过了")
	}
	rand.Seed(time.Now().UnixNano())
	code := fmt.Sprintf("%06d", rand.Intn(900000)+100000)
	a.uidVerificationCode[uid] = code

	key := RedisKey_VerificationCode + strconv.Itoa(int(a.id))
	s, _ := json.Marshal(a.uidVerificationCode)
	rdb.Default().Set(context.Background(), key, s, 30*24*time.Hour)
	return code, nil
}
func (a *RedpacketMgr) VerificationCode(uid int64, code string) bool {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	return a.uidVerificationCode[uid] == code
}

/**
*领取
 */
func (a *RedpacketMgr) updateCache() {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	data := a.getData()
	list := a.GetReceiveUser(data.Details)
	if len(list) >= int(data.Num) {
		a.finished = true
	}
	var amountAll float64 = 0
	for _, item := range list {
		a.uidFinished[item.Uid] = item.Amount
		amountAll += item.Amount
	}
	if amountAll >= data.Amount {
		a.finished = true
	}
	key := RedisKey_VerificationCode + strconv.Itoa(int(a.id))
	s, err := rdb.Default().Get(context.Background(), key).Result()
	if err == nil {
		json.Unmarshal([]byte(s), &a.uidVerificationCode)
	}
}

func (a *RedpacketMgr) UpdateChaId(chatId int64, inlineMessageID string) error {
	s := map[string]interface{}{}
	s["chatId"] = chatId
	s["inlineMessageID"] = inlineMessageID
	if err_ := mdb.Default().Table(a.TableName()).Where("`id` = ?", a.id).Updates(s).Error; err_ != nil {
		log.Error("红包chatId更新失败记录 id:", a.id, " chatId:", chatId, " inlineMessageID:", inlineMessageID)
		return err_
	}
	a.ChatId = chatId
	a.InlineMessageID = inlineMessageID
	return nil
}

/**
*领取
 */
func (a *RedpacketMgr) Receive(uid int64, name, userName string, gmStop bool) (float64, error) {
	a.mutex.Lock()
	defer a.mutex.Unlock()
	if a.finished {
		return 0, fmt.Errorf("红包领完了")
	}
	if a.uidFinished[uid] > 0 {
		return 0, fmt.Errorf("已经领取过了")
	}
	data := a.getData()
	list := a.GetReceiveUser(data.Details)
	if len(list) >= int(data.Num) {
		a.finished = true
		return 0, fmt.Errorf("红包领完了")
	}
	var amountAll float64 = 0
	for _, item := range list {
		amountAll += item.Amount
		a.uidFinished[uid] = item.Amount
		if !gmStop {
			if item.Uid == uid {
				return 0, fmt.Errorf("已经领取过了")
			}
		}
	}
	if amountAll >= data.Amount {
		a.finished = true
		return 0, fmt.Errorf("红包额度没有")
	}
	gameId, err := GetGameUid(uid, name, data.OwnerPlatUid)
	if err != nil {
		log.Error(err)
		return 0, err
	}
	bc, err := cache.QueryUserBasicInfo(gameId)
	if err != nil {
		log.Error(err)
		return 0, err
	}

	leftAmount := data.Amount - amountAll
	leftNum := int(data.Num) - len(list)
	amount := leftAmount / float64(leftNum)
	if data.RedpacketType == 2 {
		if amount > data.Amount/float64(data.Num)/10 {
			max := amount + amount/10
			min := amount - amount/10
			rand.Seed(time.Now().UnixNano())              // 设置随机数种子
			randomValue := min + rand.Float64()*(max-min) // 生成范围内的随机数
			amount = float64(int(randomValue*1000)) / 1000
		}
		if leftNum == 1 {
			amount = leftAmount
		}
	}
	if gmStop {
		amount = data.Amount - amountAll
	}
	if len(list) > 40 {
		name = ""
		userName = ""
	}
	list = append(list, ReceiveUser{
		Uid:      uid,
		Name:     name,
		UserName: userName,
		Amount:   amount,
	})
	str := a.ReceiveUserToString(list)
	s := map[string]interface{}{}
	s["details"] = str
	if err_ := mdb.Default().Table(a.TableName()).Where("`id` = ?", a.id).Updates(s).Error; err_ != nil {
		log.Error("领取红包失败_增加记录 id:", a.id, " uid:", uid, " amount:", amount)
		return 0, err_
	}
	a.uidFinished[uid] = amount
	log.Info("领取红包成功_a id:", a.id, " uid:", uid, " amount:", amount)
	var changeAssets = []*pb.IDValFloat{
		&pb.IDValFloat{ID: int64(data.AssetID), Value: amount},
	}
	_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: bc.ID,
		Cause:  "AddRedPacket",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		return 0, err
	}
	log.Info("领取红包成功_b id:", a.id, " uid:", uid, " amount:", amount)
	return amount, nil
}
