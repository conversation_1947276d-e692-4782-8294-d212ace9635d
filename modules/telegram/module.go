package telegram

import (
	"context"
	"encoding/json"
	"s2/common"
	"s2/common/cache"
	"s2/define"
	"s2/pb"
	"sync"
	"time"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/infra/rdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

var cacheUids sync.Map = sync.Map{}

type EnumInputCmd int32

const (
	NONE             EnumInputCmd = 0
	RechargeAmount   EnumInputCmd = 1
	WithdrawalAmount EnumInputCmd = 2
)

type InputCmd struct {
	Cmd        EnumInputCmd
	ExpireTime int64 //过期时间
	Data       []string
}

type module struct {
	iface.IModule
	cacheInputCmd sync.Map
	adminIds      []int64
	operatorIds   []int64
}

func New() iface.IModule {
	m := &module{
		IModule:       basic.NewConcurrency(),
		cacheInputCmd: sync.Map{},
	}
	return m
}

func (m *module) Name() string {
	return define.ModuleName.Telegram
}

func (m *module) Init() error {
	{
		a := RedpacketMgr{}
		a.AutoMigrate()
	}
	m.loadInputCmd()
	m.adminIds = conf.List("telegram.AdminIds", []int64{})
	m.operatorIds = conf.List("telegram.OperatorIds", []int64{})
	return nil
}

func (m *module) Run() error {
	conc.Go(func() {
		m.runTelegram()
	})
	return nil
}
func (m *module) Exit() error {
	bot.StopReceivingUpdates()
	m.saveInputCmd()
	m.IModule.Exit()
	return nil
}

func (m *module) loadInputCmd() {
	data, err := rdb.Default().Get(context.Background(), "telegram_input_cmd").Result()
	if err != nil {
		log.Error("loadInputCmd error:", err)
		return
	}
	var inputCmds []InputCmd
	err = json.Unmarshal([]byte(data), &inputCmds)
	if err != nil {
		log.Error("loadInputCmd unmarshal error:", err)
		return
	}
	for id, inputCmd := range inputCmds {
		m.cacheInputCmd.Store(id, inputCmd)
	}
}
func (m *module) saveInputCmd() {
	var inputCmds []InputCmd
	m.cacheInputCmd.Range(func(key, value any) bool {
		if value.(InputCmd).ExpireTime < time.Now().Unix() {
			m.cacheInputCmd.Delete(key)
			return true
		}
		inputCmds = append(inputCmds, value.(InputCmd))
		return true
	})
	data, err := json.Marshal(inputCmds)
	if err != nil {
		log.Error("saveInputCmd marshal error:", err)
		return
	}
	rdb.Default().Set(context.Background(), "telegram_input_cmd", string(data), time.Hour*24)
}

func (m *module) SetInputCmd(telegramId int64, cmd EnumInputCmd, data []string) error {
	expireTime := time.Now().Add(time.Second * 60 * 10).Unix()
	inputCmd := InputCmd{
		Cmd:        cmd,
		ExpireTime: expireTime,
		Data:       data,
	}
	m.cacheInputCmd.Store(telegramId, inputCmd)
	return nil
}

func (m *module) UseInputCmd(telegramId int64) InputCmd {
	inputCmd, ok := m.cacheInputCmd.Load(telegramId)
	if !ok {
		return InputCmd{}
	}
	if inputCmd.(InputCmd).ExpireTime < time.Now().Unix() {
		m.cacheInputCmd.Delete(telegramId)
		return InputCmd{}
	}
	m.cacheInputCmd.Delete(telegramId)
	return inputCmd.(InputCmd)
}

func GetGameUid(telegramId int64, name string, parent int64) (int64, error) {
	uid, ok := cacheUids.Load(telegramId)
	if ok {
		return uid.(int64), nil
	}
	token := conf.Str("telegram.token", "")
	data, err := common.CreateTelegramLoginToken(telegramId, name, token)
	if err != nil {
		return 0, err
	}
	resp, err := message.RequestAny[pb.TelegramLoginResp](define.ModuleName.Account, &pb.TelegramLoginToAccountReq{
		TelegramData: data,
		ParentUid:    parent,
	})
	if err != nil {
		return 0, err
	}
	bc, err := cache.QueryUserBasicInfoByToken(resp.Token)
	if err != nil {
		return 0, err
	}
	cacheUids.Store(telegramId, bc.ID)
	return bc.ID, nil
}
