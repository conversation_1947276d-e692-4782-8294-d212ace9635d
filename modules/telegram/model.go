package telegram

import (
	"bytes"
	"encoding/base64"
	"encoding/binary"
	"errors"
	"fmt"
)

type Parameter struct {
	Parent    int64
	Redpacket int64
}

func PackParameter(p Parameter) string {
	var buf bytes.Buffer
	tagBuf := make([]byte, binary.MaxVarintLen64)
	// encode field 1: Parent, tag = (1<<3)|0
	n := binary.PutUvarint(tagBuf, 1<<3)
	buf.Write(tagBuf[:n])
	n = binary.PutUvarint(tagBuf, uint64(p.Parent))
	buf.Write(tagBuf[:n])
	// encode field 2: Redpacket, tag = (2<<3)|0
	n = binary.PutUvarint(tagBuf, 2<<3)
	buf.Write(tagBuf[:n])
	n = binary.PutUvarint(tagBuf, uint64(p.Redpacket))
	buf.Write(tagBuf[:n])
	return base64.RawURLEncoding.EncodeToString(buf.Bytes())
}

func UnPackParameter(s string) (Parameter, error) {
	data, err := base64.RawURLEncoding.DecodeString(s)
	if err != nil {
		return Parameter{}, err
	}
	var p Parameter
	for len(data) > 0 {
		tag, n := binary.Uvarint(data)
		if n <= 0 {
			return p, errors.New("invalid tag")
		}
		fieldNum := tag >> 3
		wireType := tag & 7
		if wireType != 0 {
			return p, fmt.Errorf("unsupported wire type %d", wireType)
		}
		data = data[n:]
		value, mLen := binary.Uvarint(data)
		if mLen <= 0 {
			return p, errors.New("invalid varint for field")
		}
		switch fieldNum {
		case 1:
			p.Parent = int64(value)
		case 2:
			p.Redpacket = int64(value)
		default:
			// ignore unknown field
		}
		data = data[mLen:]
	}
	return p, nil
}
