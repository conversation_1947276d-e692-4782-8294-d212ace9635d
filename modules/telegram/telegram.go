package telegram

import (
	"reflect"
	"s2/common/cache"
	"strconv"
	"strings"

	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
)

var (
	bot *tgbotapi.BotAPI
)

func (m *module) runTelegram() error {
	token := conf.Str("telegram.token", "")
	bot_, err := tgbotapi.NewBotAPI(token)
	if err != nil {
		panic(err)
	}
	bot = bot_
	log.Infof("Authorized on telegram %s", bot.Self.UserName)
	u := tgbotapi.NewUpdate(0)
	u.Timeout = 60
	updates := bot.GetUpdatesChan(u)

	// Handle updates with context cancellation support
	for {
		select {
		case <-system.RootCtx().Done():
			log.Info("Telegram service shutting down...")
			return nil
		case update, ok := <-updates:
			if !ok {
				log.Info("Telegram updates channel closed")
				return nil
			}
			go m.telegramHandle(update)
		}
	}
}

func (m *module) telegramHandle(update tgbotapi.Update) error {
	defer utils.RecoverPanic()
	if update.InlineQuery != nil { //输入查询
		m.onInlineQuery(update)
	}
	if update.Message != nil { // If we got a message
		text := update.Message.Text
		log.Info("Message_1 chatuid:", update.Message.Chat.ID, " FromID:", update.Message.From.ID, " UserName:", update.Message.From.UserName, " text:", text)
		if update.Message.Chat.ID < 0 {
			// onMessageFromOther(config, update)
			return nil
		}
		if strings.Index(text, "/start") == 0 {
			m.ShowConfigPage(&update, []string{PageName.StartPage})
		} else {
			cmd := m.UseInputCmd(update.Message.Chat.ID)
			if cmd.Cmd != NONE {
				m.onCmd(&update, cmd)
			}
		}
	}
	if update.CallbackQuery != nil {
		if err := m.onCallbackQuery(&update); err != nil {
			return err
		}
	}
	return nil
}

// onCallbackQuery dispatches callback to method named by callback.Data
func (m *module) onCallbackQuery(update *tgbotapi.Update) error {
	callback := update.CallbackQuery
	// data format: method_param1_param2_...
	parts := strings.Split(callback.Data, "_")
	methodName := parts[0]
	params := parts[1:]
	log.Infof("callbackName: %s, params: %v", methodName, params)
	method := reflect.ValueOf(m).MethodByName(methodName)
	if !method.IsValid() {
		log.Warnf("no handler for callback %s", methodName)
		return nil
	}
	// call handler: func(update *tgbotapi.Update, params []string) error
	in := []reflect.Value{reflect.ValueOf(update), reflect.ValueOf(params)}
	results := method.Call(in)
	if len(results) == 1 && !results[0].IsNil() {
		if err, ok := results[0].Interface().(error); ok {
			return err
		}
	}
	return nil
}

func (m *module) onInlineQuery(update tgbotapi.Update) {
	queryText := update.InlineQuery.Query
	log.Info("InlineQuery queryText:", queryText)
	if strings.Index(queryText, "redpacket_") == 0 {
		redpacketIdStr := queryText[10:]
		redpacketId_, err := strconv.ParseInt(redpacketIdStr, 10, 64)
		if err != nil {
			return
		}
		redpacketId := int64(redpacketId_)
		mgr := GetRedpacketMgr(redpacketId)
		if mgr == nil {
			return
		}
		data := mgr.GetData()
		if data == nil {
			return
		}
		if data.OwnerUid != update.InlineQuery.From.ID {
			return
		}
		ownerBc, err := cache.QueryUserBasicInfo(data.OwnerPlatUid)
		if err != nil {
			log.Error(err)
			return
		}
		list := mgr.GetReceiveUser(data.Details)
		left := data.Num - int32(len(list))
		var msgText = "🧧 " + TelegramNameToHref(ownerBc.Name, data.OwnerName) + " 发送了一个红包"
		logs := ""
		if len(list) > 20 {
			logs += "前20领取记录:"
		} else if len(list) > 0 {
			logs += "领取记录:"
		}
		i := 0
		var amountAll float64 = 0
		for _, item := range list {
			if i < 20 {
				logs += "\n" + strconv.FormatFloat(item.Amount, 'f', -1, 64) + data.CurrencyDes + " - " + item.Name
			}
			amountAll += item.Amount
			i++
		}
		msgText += "\n💵 金额:" + strconv.FormatFloat(amountAll, 'f', -1, 64) + "/" + strconv.FormatFloat(data.Amount, 'f', -1, 64) + " " + data.CurrencyDes
		if amountAll < data.Amount {
			msgText += "\n领取数量:" + strconv.Itoa(int(len(list))) + "/" + strconv.Itoa(int(data.Num))
		}
		msgText += "\n" + logs
		fileUrl := conf.Str("telegram.redpacketimg", "")
		var results = []interface{}{}
		queryTitle := "🧧剩余红包:" + strconv.Itoa(int(left)) + "\n" + data.CurrencyDes + " 总金额:" + strconv.FormatFloat(data.Amount, 'f', -1, 64)
		redpacket := tgbotapi.NewInlineKeyboardButtonData("管理员开启", "receive_redpacket_owner:"+redpacketIdStr)
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(redpacket),
		)
		if strings.HasSuffix(fileUrl, ".mp4") {
			article := tgbotapi.NewInlineQueryResultVoice(update.InlineQuery.ID, fileUrl, queryTitle)
			article.Caption = msgText
			article.ParseMode = "HTML"
			article.ReplyMarkup = &keyboard
			results = []interface{}{article}
		} else if strings.HasSuffix(fileUrl, ".gif") {
			article := tgbotapi.NewInlineQueryResultGIF(update.InlineQuery.ID, fileUrl)
			article.Title = queryTitle
			article.Caption = msgText
			article.ParseMode = "HTML"
			article.ReplyMarkup = &keyboard
			results = []interface{}{article}
		} else if fileUrl == "" {
			article := tgbotapi.NewInlineQueryResultArticle(update.InlineQuery.ID, queryTitle, msgText)
			article.ReplyMarkup = &keyboard
			results = []interface{}{article}
		} else {
			article := tgbotapi.NewInlineQueryResultPhoto(update.InlineQuery.ID, fileUrl)
			article.Title = queryTitle
			article.Caption = msgText
			article.ParseMode = "HTML"
			article.ReplyMarkup = &keyboard
			results = []interface{}{article}
		}
		inlineConf := tgbotapi.InlineConfig{
			InlineQueryID: update.InlineQuery.ID,
			IsPersonal:    true,
			CacheTime:     0,
			Results:       results,
		}
		if _, err := bot.Request(inlineConf); err != nil {
			log.Error(err)
		}
	} else {
		gameId, err := GetGameUid(update.InlineQuery.From.ID, "", 0)
		if err != nil {
			return
		}
		parameter := PackParameter(Parameter{Parent: gameId, Redpacket: 0})
		boturl := conf.Str("telegram.boturl", "")
		shareUrl := boturl + "?start=" + parameter
		shareText := conf.Str("telegram.shareText", "")
		article := tgbotapi.NewInlineQueryResultArticle(update.InlineQuery.ID, "🤝成为代理", shareText)
		article.Description = shareText + "\n" + shareUrl
		gobutton := tgbotapi.NewInlineKeyboardButtonURL("👉体验一下", shareUrl)
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(gobutton),
		)
		article.ReplyMarkup = &keyboard
		inlineConf := tgbotapi.InlineConfig{
			InlineQueryID: update.InlineQuery.ID,
			IsPersonal:    true,
			CacheTime:     0,
			Results:       []interface{}{article},
		}
		if _, err := bot.Request(inlineConf); err != nil {
			log.Error(err)
		}
	}
}
