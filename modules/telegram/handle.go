package telegram

import (
	"bytes"
	"fmt"
	"image/png"
	"math/rand"
	"reflect"
	"s2/common/cache"
	"s2/define"
	"s2/pb"
	"slices"
	"strconv"
	"strings"
	"text/template"
	"time"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/utils"
)

var (
	PageName = utils.NewEnum[struct {
		StartPage string
	}]()
)

func (m *module) DeletePage(update *tgbotapi.Update, params []string) error {
	uid := update.CallbackQuery.Message.Chat.ID
	msg := tgbotapi.NewDeleteMessage(uid, update.CallbackQuery.Message.MessageID)
	bot.Send(msg)

	if params != nil && params[0] != "" {
		methodName := params[0]
		params := params[1:]
		log.Infof("callbackName: %s, params: %v", methodName, params)
		method := reflect.ValueOf(m).MethodByName(methodName)
		if !method.IsValid() {
			log.Warnf("no handler for callback %s", methodName)
			return nil
		}
		update.Message = update.CallbackQuery.Message
		// call handler: func(update *tgbotapi.Update, params []string) error
		in := []reflect.Value{reflect.ValueOf(update), reflect.ValueOf(params)}
		results := method.Call(in)
		if len(results) == 1 && !results[0].IsNil() {
			if err, ok := results[0].Interface().(error); ok {
				return err
			}
		}
	}
	return nil
}

func (m *module) HistoryPage(update *tgbotapi.Update, params []string) error {
	id := update.CallbackQuery.Message.Chat.ID
	// 获取业务用户ID并查询基本信息
	gameUid, err := GetGameUid(id, "", 0)
	if err != nil {
		log.Error(err)
		return err
	}
	bc, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return err
	}
	// 解析页码
	pageNum, err := strconv.ParseInt(params[1], 10, 64) // 页码
	if err != nil {
		log.Error(err)
		return err
	}
	currentPage := int(pageNum)
	pageSize := 10
	var total int
	var textBuilder strings.Builder
	types := params[0] // Recharge, Withdrawal, Input
	userID := bc.ID
	userType := ""
	if len(params) > 2 {
		userType = params[2] // Admin 所有
		if userType == "Admin" {
			userID = 0
		}
	}
	var rows [][]tgbotapi.InlineKeyboardButton
	// 根据类型调用不同接口并格式化文本
	if types == "Recharge" {
		resp, err := message.RequestAny[pb.HistoryRechargeOrderResp](define.ModuleName.Order, &pb.HistoryRechargeOrderReq{
			UserID:     userID,
			PageSize:   int32(pageSize),
			PageNumber: int32(currentPage),
			StartTime:  uint64(time.Now().AddDate(0, 0, -30).Unix()),
			EndTime:    uint64(time.Now().Unix()),
			Type:       2,
		})
		if err != nil {
			log.Error(err)
			return err
		}
		total = int(resp.Total)
		textBuilder.WriteString(fmt.Sprintf("最近的充值信息（第%d页）\n\n", currentPage))
		for _, o := range resp.Orders {
			textBuilder.WriteString(fmt.Sprintf("订单号：%s\n", o.TxHash))
			// 状态与中文描述
			var icon, cn string
			switch o.Status {
			case pb.OrderStatus_CONFIRMED:
				icon, cn = "✅", "成功充值"
			case pb.OrderStatus_FAILED:
				icon, cn = "❌", "充值失败"
			case pb.OrderStatus_PENDING:
				icon, cn = "⌛", "待确认"
			case pb.OrderStatus_CHECKING:
				icon, cn = "⌛", "等待支付"
			case pb.OrderStatus_REJECTED_PENDING:
				icon, cn = "❌", "取消支付"
			}
			textBuilder.WriteString(fmt.Sprintf("类型： %s %s\n", icon, cn))
			chainName := strings.Title(strings.ToLower(o.Chain))
			textBuilder.WriteString(fmt.Sprintf("渠道：%s@(%s)\n", chainName, o.CoinType))
			textBuilder.WriteString(fmt.Sprintf("到账金额：%s\n", strconv.FormatFloat(o.Amount, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("游戏币 %s\n", strconv.FormatFloat(o.AssetAmount, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("日期：%s\n\n", time.Unix(int64(o.CreatedAt), 0).Format("2006-01-02 15:04:05")))
		}
	} else if types == "Withdrawal" {
		resp, err := message.RequestAny[pb.HistoryWithdrawOrderResp](define.ModuleName.Order, &pb.HistoryWithdrawOrderReq{
			UserID:     userID,
			PageSize:   int32(pageSize),
			PageNumber: int32(currentPage),
			StartTime:  uint64(time.Now().AddDate(0, 0, -30).Unix()),
			EndTime:    uint64(time.Now().Unix()),
			Type:       2,
		})
		if err != nil {
			log.Error(err)
			return err
		}
		total = int(resp.Total)
		textBuilder.WriteString(fmt.Sprintf("最近的提款信息（第%d页）\n\n", currentPage))
		for _, o := range resp.Orders {
			textBuilder.WriteString(fmt.Sprintf("订单号：%s\n", o.ID))
			var icon, cn string
			switch o.Status {
			case pb.OrderStatus_CONFIRMED:
				icon, cn = "✅", "成功取款"
			case pb.OrderStatus_FAILED:
				icon, cn = "❌", "转账失败"
			case pb.OrderStatus_PENDING:
				icon, cn = "⌛", "待转账"
			case pb.OrderStatus_CHECKING:
				icon, cn = "⌛", "审核中"
			case pb.OrderStatus_REJECTED_PENDING:
				icon, cn = "❌", "审核驳回"
			case pb.OrderStatus_REJECTED_CONFIRMED:
				icon, cn = "❌", "审核驳回已退款"
			}
			textBuilder.WriteString(fmt.Sprintf("类型： %s %s\n", icon, cn))
			chainName := strings.Title(strings.ToLower(o.Chain))
			textBuilder.WriteString(fmt.Sprintf("渠道：%s@(%s)\n", chainName, o.CoinType))
			textBuilder.WriteString(fmt.Sprintf("转账金额：%s\n", strconv.FormatFloat(o.Amount, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("扣除游戏币：%s\n", strconv.FormatFloat(o.AssetAmount, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("手续费: %s\n", strconv.FormatFloat(o.AssetFees, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("日期：%s\n\n", time.Unix(int64(o.CreatedAt), 0).Format("2006-01-02 15:04:05")))
		}
	} else if types == "WithdrawalChecking" {
		pageSize = 1
		resp, err := message.RequestAny[pb.HistoryWithdrawOrderResp](define.ModuleName.Order, &pb.HistoryWithdrawOrderReq{
			UserID:     userID,
			PageSize:   int32(pageSize),
			PageNumber: int32(currentPage),
			StartTime:  uint64(time.Now().AddDate(0, 0, -30).Unix()),
			EndTime:    uint64(time.Now().Unix()),
			Type:       3,
		})
		if err != nil {
			log.Error(err)
			return err
		}
		total = int(resp.Total)
		textBuilder.WriteString(fmt.Sprintf("待审核提款信息（第%d页）\n\n", currentPage))
		if len(resp.Orders) > 0 {
			order := resp.Orders[0]
			icon, cn := "⌛", "审核中"
			textBuilder.WriteString(fmt.Sprintf("订单号：%s\n", order.ID))
			textBuilder.WriteString(fmt.Sprintf("类型： %s %s\n", icon, cn))
			chainName := strings.Title(strings.ToLower(order.Chain))
			textBuilder.WriteString(fmt.Sprintf("渠道：%s@(%s)\n", chainName, order.CoinType))
			textBuilder.WriteString(fmt.Sprintf("转账金额：%s\n", strconv.FormatFloat(order.Amount, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("扣除游戏币：%s\n", strconv.FormatFloat(order.AssetAmount, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("手续费: %s\n", strconv.FormatFloat(order.AssetFees, 'f', -1, 64)))
			textBuilder.WriteString(fmt.Sprintf("日期：%s\n\n", time.Unix(int64(order.CreatedAt), 0).Format("2006-01-02 15:04:05")))
			var buttonRow []tgbotapi.InlineKeyboardButton
			buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonData("✅通过", "GMOrderOperation_"+order.ID+"_1"))
			buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonData("❌驳回", "GMOrderOperation_"+order.ID+"_2"))
			rows = append(rows, buttonRow)
		}
	} else {
		// 非法类型
		return nil
	}
	// 分页按钮
	totalPage := (total + pageSize - 1) / pageSize
	var btns []tgbotapi.InlineKeyboardButton
	if currentPage > 1 {
		prev := fmt.Sprintf("HistoryPage_%s_%d", params[0], currentPage-1)
		if userType == "Admin" {
			prev = fmt.Sprintf("HistoryPage_%s_%d_%s", params[0], currentPage-1, userType)
		}
		btns = append(btns, tgbotapi.NewInlineKeyboardButtonData("←上一页", prev))
	}
	btns = append(btns, tgbotapi.NewInlineKeyboardButtonData("返回主页", "ShowConfigPage_StartPage"))
	if currentPage < totalPage {
		next := fmt.Sprintf("HistoryPage_%s_%d", params[0], currentPage+1)
		btns = append(btns, tgbotapi.NewInlineKeyboardButtonData("下一页→", next))
	}
	rows = append(rows, btns)
	keyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	chatId := update.CallbackQuery.From.ID
	inlineMessageID := update.CallbackQuery.InlineMessageID
	messageID := update.CallbackQuery.Message.MessageID
	edit := tgbotapi.EditMessageTextConfig{
		BaseEdit: tgbotapi.BaseEdit{
			ChatID:          chatId,
			InlineMessageID: inlineMessageID,
			MessageID:       messageID,
			ReplyMarkup:     &keyboard,
		},
		ParseMode: "HTML",
		Text:      textBuilder.String(),
	}
	if _, err := bot.Send(edit); err != nil {
		log.Error(err)
	}
	return nil
}

func (m *module) CmdPage(update *tgbotapi.Update, params []string) error {
	id := update.CallbackQuery.Message.Chat.ID
	text := params[0]
	cmdStr := params[1]
	cmd, err := strconv.Atoi(cmdStr)
	if err != nil {
		log.Error(err)
		return err
	}
	cmdData := []string{}
	if len(params) > 2 {
		cmdData = params[2:]
	}
	m.SetInputCmd(id, EnumInputCmd(cmd), cmdData)
	msg := tgbotapi.NewMessage(id, text)
	msg.ParseMode = "HTML"
	if _, err := bot.Send(msg); err != nil {
		log.Error(err)
	}
	return nil
}

func (m *module) onCmd(update *tgbotapi.Update, cmd InputCmd) error {
	text := update.Message.Text
	if cmd.Cmd == RechargeAmount {
		amount, err := strconv.ParseFloat(text, 64)
		if err != nil {
			log.Error(err)
			return err
		}
		id := update.Message.Chat.ID
		gameUid, err := GetGameUid(id, "", 0)
		if err != nil {
			log.Error(err)
			return err
		}
		bc, err := cache.QueryUserBasicInfo(gameUid)
		if err != nil {
			log.Error(err)
			return err
		}
		if len(cmd.Data) < 2 {
			log.Errorf("cmd.Data is not enough: %v", cmd)
			return err
		}
		chain := pb.EnumChain(pb.EnumChain_value["CHAIN_"+cmd.Data[0]])
		coinID := pb.EnumCoin(pb.EnumCoin_value[cmd.Data[1]])
		resp, err := message.RequestAny[pb.CreateRechargeOrderResp](define.ModuleName.Order, &pb.CreateRechargeOrderReq{
			UserID: bc.ID,
			Chain:  chain,
			Value:  amount,
			CoinID: coinID,
		})
		if err != nil {
			log.Error(err)
			return err
		}
		if resp.Code != pb.SUCCESS {
			log.Errorf("create recharge order failed: %v", resp)
			return err
		}
		msg := tgbotapi.NewMessage(id, "充值订单创建成功，点击前往充值")
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(
				tgbotapi.NewInlineKeyboardButtonURL("前往充值", resp.PayUrl),
			),
		)
		if _, err := bot.Send(msg); err != nil {
			log.Error(err)
			return err
		}
	} else if cmd.Cmd == WithdrawalAmount {
		amount, err := strconv.ParseFloat(text, 64)
		if err != nil {
			log.Error(err)
			return err
		}
		id := update.Message.Chat.ID
		gameUid, err := GetGameUid(id, "", 0)
		if err != nil {
			log.Error(err)
			return err
		}
		bc, err := cache.QueryUserBasicInfo(gameUid)
		if err != nil {
			log.Error(err)
			return err
		}
		if len(cmd.Data) < 2 {
			log.Errorf("cmd.Data is not enough: %v", cmd)
			return err
		}
		chain := pb.EnumChain(pb.EnumChain_value["CHAIN_"+cmd.Data[0]])
		coinID := pb.EnumCoin(pb.EnumCoin_value[cmd.Data[1]])
		resp, err := message.Request[pb.WithdrawResp](bc.ServerID, &pb.WithdrawToLobbyReq{
			UserID: bc.ID,
			Value:  amount,
			Chain:  chain,
			Coin:   coinID,
		})
		text := "提款订单提交成功，请等待审核"
		if resp.Code != pb.SUCCESS {
			text = "提款失败"
			if resp.Code == pb.BALANCE_NOT_ENOUGH {
				text = "余额不足"
			} else if resp.Code == pb.INPUT_NOT_ENOUGH {
				text = "流水不足"
			}
		}
		msg := tgbotapi.NewMessage(id, text)
		msg.ParseMode = "HTML"
		if _, err := bot.Send(msg); err != nil {
			log.Error(err)
			return err
		}
	} else {
		log.Warnf("unknown cmd: %v", cmd)
	}
	return nil
}

func (m *module) RechargePage(update *tgbotapi.Update, params []string) error {
	id := update.CallbackQuery.Message.Chat.ID
	msg := tgbotapi.NewDeleteMessage(id, update.CallbackQuery.Message.MessageID)
	bot.Send(msg)

	gameUid, err := GetGameUid(id, "", 0)
	if err != nil {
		log.Error(err)
		return err
	}
	bc, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return err
	}
	chain := params[0] // e.g. TRON, TON, BSC, Okpay
	chainId := pb.EnumChain_value["CHAIN_"+chain]
	if chainId <= 0 {
		return fmt.Errorf("invalid chain: %s", chain)
	}
	resp, err := message.RequestAny[pb.GetOrSetUserRechargeAddressResp](define.ModuleName.Order, &pb.GetOrSetUserRechargeAddressReq{
		UserID: bc.ID,
		Chain:  pb.EnumChain(chainId),
	})
	if err != nil {
		log.Error(err)
		return err
	}
	address := resp.Address

	// generate QR code image
	code, err := qr.Encode(address, qr.L, qr.Auto)
	if err != nil {
		log.Error(err)
		return err
	}
	qrImg, err := barcode.Scale(code, 256, 256)
	if err != nil {
		log.Error(err)
		return err
	}
	var buf bytes.Buffer
	if err := png.Encode(&buf, qrImg); err != nil {
		log.Error(err)
		return err
	}
	data := buf.Bytes()

	// send QR code as photo
	caption := fmt.Sprintf("将 Tether USDT(%s) 发送到该地址。发送后，存款将自动记入账户。\n\n<code>%s</code>\n\n☝点击地址复制", chain, address)
	if pb.EnumChain(chainId) == pb.CHAIN_TON {
		caption = fmt.Sprintf("将 Tether USDT(%s) 发送到该地址。发送后，存款将自动记入账户。\n\n<code>%s</code>\n\nTONMEMO: <code>%s</code>\n用户必须输入备注/标签，才能成功充值\n\n☝点击地址复制", chain, address, resp.Extra)
	}
	photoConfig := tgbotapi.NewPhoto(id, tgbotapi.FileBytes{Name: "qrcode.png", Bytes: data})
	photoConfig.File.NeedsUpload()
	photoConfig.Caption = caption
	photoConfig.ParseMode = "HTML"
	// main menu button
	menuBtn := tgbotapi.NewInlineKeyboardButtonData("🏠 主菜单", "DeletePage_ShowConfigPage_StartPage")
	photoConfig.ReplyMarkup = tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(menuBtn),
	)
	if _, err := bot.Send(photoConfig); err != nil {
		log.Error(err)
	}
	return nil
}

func (m *module) RedPacketMul(update *tgbotapi.Update, params []string) error {
	// m.DeletePage(update, nil)
	uid := update.CallbackQuery.Message.Chat.ID
	multiple := params[0]
	log.Infof("uid: %d, multiple: %s", uid, multiple)
	m.ShowConfigPage(update, []string{"RedPacketPage", multiple})
	return nil
}

func (m *module) RedPacketNum(update *tgbotapi.Update, params []string) error {
	// m.DeletePage(update, nil)
	id := update.CallbackQuery.Message.Chat.ID
	numStr := params[0]
	amountStr := params[1]  //金额
	asset := params[2]      // USDT, CNY
	calculator := params[3] // R/A Random, Average
	// suppress unused vars
	// parse requested amount
	amount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil || amount <= 0 {
		alert := tgbotapi.NewCallbackWithAlert(update.CallbackQuery.ID, "金额格式不正确")
		if _, err2 := bot.Send(alert); err2 != nil {
			log.Error(err2)
		}
		return nil
	}
	num, err := strconv.ParseFloat(numStr, 64)
	if err != nil || num <= 0 {
		alert := tgbotapi.NewCallbackWithAlert(update.CallbackQuery.ID, "数量格式不正确")
		if _, err2 := bot.Send(alert); err2 != nil {
			log.Error(err2)
		}
		return nil
	}
	redpacketType := 1
	if calculator == "R" {
		redpacketType = 2
	}
	_ = num
	_ = calculator
	gameUid, err := GetGameUid(id, "", 0)
	if err != nil {
		log.Error(err)
		return err
	}
	bc, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return err
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: gameUid,
	})
	if err != nil {
		log.Error(err)
		return err
	}
	var balance float64
	assetID := define.AssetUSDID
	if asset == "USDT" {
		assetID = define.AssetUSDID
	} else {
		assetID = define.AssetCNYID
	}
	balance = resp.Balance[int32(assetID)]
	// popup if insufficient balance
	if amount > balance {
		alert := tgbotapi.NewCallbackWithAlert(update.CallbackQuery.ID, "余额不足")
		if _, err2 := bot.Send(alert); err2 != nil {
			log.Error(err2)
		}
		return nil
	}
	var changeAssets = []*pb.IDValFloat{
		&pb.IDValFloat{ID: int64(assetID), Value: -amount},
	}
	_, err = message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
		UserID: bc.ID,
		Cause:  "AddRedPacket",
		Assets: changeAssets,
	})
	if err != nil {
		log.Error(err)
		return err
	}
	redpacketId := int64(rand.Int())
	chat := update.CallbackQuery.Message.Chat
	err = AddRedpacket(RedpacketData{
		Id:            redpacketId,
		OwnerUid:      id,
		Amount:        amount,
		Num:           int32(num),
		CurrencyDes:   asset,
		AssetID:       int32(assetID),
		ExchangeRate:  1,
		Multiple:      5,
		RedpacketType: int32(redpacketType),
		OwnerName:     chat.FirstName + chat.LastName,
		OwnerUserName: chat.UserName,
		OwnerPlatUid:  bc.ID,
	})
	if err != nil {
		log.Warn("发红包失败 uid:", chat.ID, " PlatUid:", bc.ID, " amount:", amount, " num:", num, " redpacketId:", redpacketId)
		return err
	}
	SendRedpacketDetails(gameUid, redpacketId, true)
	return nil
}

func (m *module) RedPacketAmount(update *tgbotapi.Update, params []string) error {
	// m.DeletePage(update, nil)
	id := update.CallbackQuery.Message.Chat.ID
	amount := params[0]
	asset := params[1]      // USDT, CNY
	calculator := params[2] // R/A Random, Average
	log.Infof("RedPacketAmount id: %d, amount: %s, asset: %s, calculator: %s", id, amount, asset, calculator)
	m.InputNPage(update, []string{"请输入您要发送的红包数量:", "0", "RedPacketNum", "", params[0], params[1], params[2]})
	return nil
}

func (m *module) AddRedPacketPage(update *tgbotapi.Update, params []string) error {
	// uid := update.CallbackQuery.Message.Chat.ID
	// msg := tgbotapi.NewDeleteMessage(uid, update.CallbackQuery.Message.MessageID)
	// bot.Send(msg)
	m.InputNPage(update, []string{"请输入您要发送的总金额:", "0", "RedPacketAmount", "", params[0], params[1]})
	return nil
}

// 红包详情
func SendRedpacketDetails(gameUid int64, redpacketId int64, showOwnerButton bool) {
	mgr := GetRedpacketMgr(redpacketId)
	if mgr == nil {
		return
	}
	data := mgr.GetData()
	if data == nil {
		return
	}
	now := time.Now().UTC().Unix()
	user, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return
	}
	text := "🧧 " + user.Name + " 发送了一个红包"
	text += "\n🕦 时间:" + strconv.FormatFloat((float64(now-data.Time)/float64(3600)), 'f', 2, 64) + "小时前"
	list := mgr.GetReceiveUser(data.Details)
	logs := ""
	if len(list) > 20 {
		logs += "前20领取记录:"
	} else if len(list) > 0 {
		logs += "领取记录:"
	}
	var amountAll float64 = 0
	i := 0
	for _, item := range list {
		if i < 20 {
			logs += "\n" + strconv.FormatFloat(item.Amount, 'f', 2, 64) + data.CurrencyDes + " - " + item.Name
		}
		amountAll += item.Amount
		i++
	}
	text += "\n💵 金额:" + strconv.FormatFloat(amountAll, 'f', 2, 64) + "/" + strconv.FormatFloat(data.Amount, 'f', 2, 64) + " " + data.CurrencyDes
	if amountAll < data.Amount {
		text += "\n领取数量:" + strconv.Itoa(int(len(list))) + "/" + strconv.Itoa(int(data.Num))
	}
	text += "\n" + logs
	msg := tgbotapi.NewMessage(user.Telegram, text)
	if showOwnerButton {
		keyboard := tgbotapi.NewInlineKeyboardMarkup()
		if amountAll < data.Amount {
			shareText := "redpacket_" + strconv.Itoa(int(redpacketId))
			sendButton := tgbotapi.NewInlineKeyboardButtonSwitch("🧧发送红包", shareText)
			keyboard.InlineKeyboard = append(keyboard.InlineKeyboard, tgbotapi.NewInlineKeyboardRow(sendButton))

			stopText := "stopredpacket:" + strconv.Itoa(int(redpacketId))
			stopButton := tgbotapi.NewInlineKeyboardButtonData("🧧收回红包", stopText)
			keyboard.InlineKeyboard = append(keyboard.InlineKeyboard, tgbotapi.NewInlineKeyboardRow(stopButton))
		}
		returnButton := tgbotapi.NewInlineKeyboardButtonData("<<返回", "return")
		keyboard.InlineKeyboard = append(keyboard.InlineKeyboard, tgbotapi.NewInlineKeyboardRow(returnButton))
		msg.ReplyMarkup = keyboard
	}
	msg.ParseMode = "HTML"
	msg.DisableWebPagePreview = true
	if _, err := bot.Send(msg); err != nil {
		log.Error(err)
	}
}

func (m *module) ShowConfigPage(update *tgbotapi.Update, params []string) error {
	// unify message source: support direct /start or callback query
	var msg *tgbotapi.Message
	name := ""
	if update.Message != nil {
		msg = update.Message
		name = update.Message.From.FirstName + update.Message.From.LastName
	} else if update.CallbackQuery != nil {
		msg = update.CallbackQuery.Message
		name = update.CallbackQuery.From.FirstName + update.CallbackQuery.From.LastName
	} else {
		return fmt.Errorf("no message found in update")
	}
	id := msg.Chat.ID
	// build inline keyboard from config
	cfg := conf.Map[any]("telegram", nil)
	pageName := params[0]
	pageConfig, ok := cfg[pageName].(map[string]any)
	if !ok {
		return fmt.Errorf("telegram." + pageName + " config not found")
	}
	rawRows, ok := pageConfig["buttons"].([]any)
	if !ok {
		return fmt.Errorf("buttons config missing or invalid")
	}
	var rows [][]tgbotapi.InlineKeyboardButton
	for _, rawRow := range rawRows {
		rowSlice, ok := rawRow.([]any)
		if !ok {
			continue
		}
		var buttonRow []tgbotapi.InlineKeyboardButton
		for _, raw := range rowSlice {
			btnMap, ok := raw.(map[string]any)
			if !ok {
				continue
			}
			btnType, _ := btnMap["type"].(string)
			text, _ := btnMap["text"].(string)
			switch btnType {
			case "data":
				data, _ := btnMap["data"].(string)
				onlyAdmin, _ := btnMap["onlyAdmin"].(bool)
				if onlyAdmin {
					if !slices.Contains(m.adminIds, id) {
						continue
					}
				}
				buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonData(text, data))
			case "url":
				url, _ := btnMap["url"].(string)
				buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonURL(text, url))
			case "web_app":
				webAppURL, _ := btnMap["web_app_url"].(string)
				buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonWebApp(text, tgbotapi.WebAppInfo{URL: webAppURL}))
			case "switch":
				sw, _ := btnMap["sw"].(string)
				buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonSwitch(text, sw))
			}
		}
		if len(buttonRow) > 0 {
			rows = append(rows, buttonRow)
		}
	}
	inlineKeyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	// send message with inline keyboard
	rawText, ok := pageConfig["text"].(string)
	if !ok {
		rawText = ""
	}
	var parentUid int64
	if pageName == PageName.StartPage && update.Message != nil && len(update.Message.Text) > 6 {
		payload := update.Message.Text[7:]
		parameter, err := UnPackParameter(payload)
		if err != nil {
			return err
		}
		parentUid = parameter.Parent
		redpacketId := strconv.FormatInt(parameter.Redpacket, 10)
		log.Infof("id: %d, parentUid: %d, redpacketId: %s", id, parentUid, redpacketId)
	}
	gameUid, err := GetGameUid(id, name, parentUid)
	if err != nil {
		log.Error(err)
		return err
	}
	bc, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return err
	}
	resp, err := message.Request[pb.AssetResp](bc.ServerID, &pb.AssetReq{
		UserID: gameUid,
	})
	if err != nil {
		log.Error(err)
		return err
	}
	data := map[string]interface{}{
		"Name":          msg.From.FirstName + msg.From.LastName,
		"UserID":        msg.Chat.ID,
		"USDT":          resp.Assets[define.AssetUSDID].Value,
		"CNY":           resp.Assets[define.AssetCNYID].Value,
		"LeftInputUSDT": resp.Assets[define.AssetUSDID].LeftInput,
		"LeftInputCNY":  resp.Assets[define.AssetCNYID].LeftInput,
		"RedPacketMul":  5.0,
		"Now":           time.Now().Format("2006-01-02 15:04:05"),
	}
	for i, param := range params {
		argKey := fmt.Sprintf("Arg%d", i)
		data[argKey] = param
	}
	tmpl, err := template.New("page").Parse(rawText)
	if err != nil {
		log.Error(err)
		return err
	}
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		log.Error(err)
		return err
	}
	text := buf.String()
	fileurl, ok := pageConfig["file"].(string)
	if !ok {
		fileurl = ""
	}
	if update.Message != nil {
		if fileurl != "" {
			var file = tgbotapi.FileURL(fileurl)
			if strings.HasSuffix(fileurl, ".gif") {
				tgFile := tgbotapi.NewDocument(id, file)
				tgFile.File.NeedsUpload()
				tgFile.ReplyMarkup = inlineKeyboard
				tgFile.Caption = text
				tgFile.ParseMode = "HTML"
				_, err := bot.Send(tgFile)
				if err != nil {
					log.Error(err)
				}
			} else if strings.HasSuffix(fileurl, ".mp4") {
				tgFile := tgbotapi.NewVideo(id, file)
				tgFile.File.NeedsUpload()
				tgFile.Caption = text
				tgFile.ParseMode = "HTML"
				tgFile.ReplyMarkup = inlineKeyboard
				_, err := bot.Send(tgFile)
				if err != nil {
					log.Error(err)
				}
			} else {
				tgFile := tgbotapi.NewPhoto(id, file)
				tgFile.File.NeedsUpload()
				tgFile.ReplyMarkup = inlineKeyboard
				tgFile.Caption = text
				tgFile.ParseMode = "HTML"
				_, err := bot.Send(tgFile)
				if err != nil {
					log.Error(err)
				}
			}
		} else {
			msg := tgbotapi.NewMessage(id, text)
			msg.ReplyMarkup = inlineKeyboard
			msg.ParseMode = "HTML"
			msg.DisableWebPagePreview = true
			if _, err := bot.Send(msg); err != nil {
				log.Error(err)
			}
		}
	} else {
		chatId := update.CallbackQuery.From.ID
		inlineMessageID := update.CallbackQuery.InlineMessageID
		messageID := update.CallbackQuery.Message.MessageID
		edit := tgbotapi.EditMessageTextConfig{
			BaseEdit: tgbotapi.BaseEdit{
				ChatID:          chatId,
				InlineMessageID: inlineMessageID,
				MessageID:       messageID,
				ReplyMarkup:     &inlineKeyboard,
			},
			ParseMode: "HTML",
			Text:      text,
		}
		if _, err := bot.Send(edit); err != nil {
			log.Error(err)
		}
	}
	return nil
}

func (m *module) InputNPage(update *tgbotapi.Update, params []string) error {
	// unify message source: support direct /start or callback query
	var msg *tgbotapi.Message
	if update.Message != nil {
		msg = update.Message
	} else if update.CallbackQuery != nil {
		msg = update.CallbackQuery.Message
	} else {
		err := fmt.Errorf("no message found in update")
		log.Error(err)
		return err
	}
	id := msg.Chat.ID
	title := params[0]
	if title == "" {
		title = msg.Text
	}
	numStr := params[1]
	if numStr == "0" {
		numStr = ""
	}
	confirmPage := params[2]
	// confirmPageNumStr := params[3]
	confirmPageParams := params[4:]
	if numStr != "" {
		_, err := strconv.ParseFloat(numStr, 64)
		if err != nil {
			log.Error(err)
			return err
		}
	}
	var rows [][]tgbotapi.InlineKeyboardButton
	var buttonRow []tgbotapi.InlineKeyboardButton
	points := []string{"", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", ".", "确认", "清除", "🔙返回首页"}
	confirmData := confirmPage + "_" + numStr
	if len(confirmPageParams) > 0 {
		confirmData += "_" + strings.Join(confirmPageParams, "_")
	}
	for i, point := range points {
		t := point
		nextNum := ""
		if numStr != "0" {
			nextNum = numStr
		}
		nextNum += point
		d := "InputNPage_" + "" + "_" + nextNum + "_" + confirmData
		if point == "" {
			t = numStr
		} else if point == "确认" {
			d = confirmData
		} else if point == "清除" {
			d = "InputNPage_" + "" + "_" + "" + "_" + confirmData
		} else if point == "🔙返回首页" {
			d = "ShowConfigPage_StartPage"
		}
		buttonRow = append(buttonRow, tgbotapi.NewInlineKeyboardButtonData(t, d))
		if len(buttonRow) >= 3 || i == 0 || len(points)-1 == i {
			rows = append(rows, buttonRow)
			buttonRow = []tgbotapi.InlineKeyboardButton{}
		}
	}
	inlineKeyboard := tgbotapi.NewInlineKeyboardMarkup(rows...)
	// send message with inline keyboard
	gameUid, err := GetGameUid(id, "", 0)
	if err != nil {
		log.Error(err)
		return err
	}
	bc, err := cache.QueryUserBasicInfo(gameUid)
	if err != nil {
		log.Error(err)
		return err
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: gameUid,
	})
	if err != nil {
		log.Error(err)
		return err
	}
	data := map[string]interface{}{
		"Name":   msg.From.FirstName + msg.From.LastName,
		"UserID": msg.Chat.ID,
		"USDT":   resp.Balance[define.AssetUSDID],
		"CNY":    resp.Balance[define.AssetCNYID],
		"Now":    time.Now().Format("2006-01-02 15:04:05"),
	}
	rawText := title
	for i, param := range params {
		argKey := fmt.Sprintf("Arg%d", i)
		data[argKey] = param
	}
	tmpl, err := template.New("page").Parse(rawText)
	if err != nil {
		log.Error(err)
		return err
	}
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		log.Error(err)
		return err
	}
	text := buf.String()
	if update.Message != nil {
		msg := tgbotapi.NewMessage(id, text)
		msg.ReplyMarkup = inlineKeyboard
		msg.ParseMode = "HTML"
		msg.DisableWebPagePreview = true
		if _, err := bot.Send(msg); err != nil {
			log.Error(err)
		}
	} else {
		chatId := update.CallbackQuery.From.ID
		inlineMessageID := update.CallbackQuery.InlineMessageID
		messageID := update.CallbackQuery.Message.MessageID
		edit := tgbotapi.EditMessageTextConfig{
			BaseEdit: tgbotapi.BaseEdit{
				ChatID:          chatId,
				InlineMessageID: inlineMessageID,
				MessageID:       messageID,
				ReplyMarkup:     &inlineKeyboard,
			},
			ParseMode: "HTML",
			Text:      text,
		}
		if _, err := bot.Send(edit); err != nil {
			log.Error(err)
		}
	}
	return nil
}
