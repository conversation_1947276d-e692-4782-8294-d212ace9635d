package statistics

import (
	"context"
	"fmt"
	"s2/common"
	"s2/gsconf"
	"s2/mbi"
	"s2/pb"
	"strconv"
	"sync"
	"time"

	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/infra/rdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
	"gorm.io/gorm"
)

// Redis 键名前缀
const (
	RedisKeyStatsDedup    = "stats:dedup:"  // 去重集合前缀
	RedisKeyStatsOnline   = "stats:online:" // 在线统计前缀
	RedisKeyExpireSeconds = 86400           // 24小时过期
)

// 内存缓存结构
type CachedStatistics struct {
	Date         uint32
	GameID       int32
	AssetID      int32
	Channel      string
	BetCount     int32
	TotalInput   float64
	TotalOutput  float64
	MaxBet       float64
	MinBet       float64
	TotalJackpot float64
	JackpotCount int32
	BonusCount   int32
}

// 座位统计缓存结构
type CachedSeatStatistics struct {
	Date         uint32
	GameID       int32
	SeatID       int32
	AssetID      int32
	BetCount     int32
	TotalInput   float64
	TotalOutput  float64
	MaxBet       float64
	MinBet       float64
	TotalJackpot float64
	JackpotCount int32
	BonusCount   int32
}

// 用户事件类型
type UserEventType int32

const (
	USER_REGISTER          UserEventType = 1
	USER_LOGIN             UserEventType = 2
	USER_PAYMENT           UserEventType = 3
	USER_RECHARGE_CREATE   UserEventType = 4
	USER_RECHARGE_COMPLETE UserEventType = 5
	USER_WITHDRAW          UserEventType = 6
	USER_ONLINE            UserEventType = 7
)

// 用户事件请求结构
type UserEventReq struct {
	EventType   UserEventType `json:"event_type"`
	UserID      int64         `json:"user_id"`
	Channel     string        `json:"channel"`
	Timestamp   uint32        `json:"timestamp"`
	Amount      float64       `json:"amount,omitempty"`        // 金额（充值、提现、付费时使用）
	IsFirstTime bool          `json:"is_first_time,omitempty"` // 是否首次（首次充值、首次付费）
	OnlineCount int32         `json:"online_count,omitempty"`  // 在线人数
}

// 运营数据缓存结构
type CachedOperationalStatistics struct {
	Date                   uint32  // 日期
	Channel                string  // 渠道
	NewRegisterCount       int32   // 新注册用户数
	LoginUserCount         int32   //登录用户数
	ActiveUserCount        int32   // 活跃用户数
	RechargeUserCount      int32   // 充值用户数
	RechargeAmount         float64 // 充值金额
	NewUserRechargeAmount  float64 // 新用户充值金额
	NewUserRechargeCount   int32   // 新用户充值次数
	CreateRechargeOrders   int32   // 充值创建订单数
	CompleteRechargeOrders int32   // 充值完成订单数
	FirstRechargeUserCount int32   // 首次付费用户数
	FirstRechargeAmount    float64 // 首次付费金额
	WithdrawUserCount      int32   // 提现用户数
	WithdrawAmount         float64 // 提现金额
	ACU                    int32   // 平均同时在线人数
	PCU                    int32   // 每分钟同时在线人数

	// ACU/PCU 计算辅助字段
	OnlineReportCount int32 `json:"-"` // 上报次数
	TotalOnlineSum    int64 `json:"-"` // 在线人数总和

	// 用于去重的集合（使用map模拟set）
	RegisteredUsers map[int64]bool `json:"-"` // 当日注册用户
	LoginUsers      map[int64]bool `json:"-"` // 当日登录用户
	RechargeUsers   map[int64]bool `json:"-"` // 当日付费用户
	WithdrawUsers   map[int64]bool `json:"-"` // 当日提现用户
}

// Redis 持久化助手函数

// getRedisKey 生成 Redis 键名
func getRedisKey(prefix, keyType, channel string, date uint32) string {
	return fmt.Sprintf("%s%s:%s:%d", prefix, keyType, channel, date)
}

// isUserInRedisSet 检查用户是否在 Redis 集合中
func isUserInRedisSet(keyType, channel string, date uint32, userID int64) bool {
	key := getRedisKey(RedisKeyStatsDedup, keyType, channel, date)
	member := strconv.FormatInt(userID, 10)

	exists, err := rdb.Default().SIsMember(context.Background(), key, member).Result()
	if err != nil {
		log.Errorf("Failed to check Redis set membership: %v", err)
		return false
	}

	return exists
}

// addUserToRedisSet 将用户添加到 Redis 集合
func addUserToRedisSet(keyType, channel string, date uint32, userID int64) bool {
	key := getRedisKey(RedisKeyStatsDedup, keyType, channel, date)
	member := strconv.FormatInt(userID, 10)

	// 添加到集合并设置过期时间
	pipe := rdb.Default().Pipeline()
	pipe.SAdd(context.Background(), key, member)
	pipe.Expire(context.Background(), key, time.Duration(RedisKeyExpireSeconds)*time.Second)

	_, err := pipe.Exec(context.Background())
	if err != nil {
		log.Errorf("Failed to add user to Redis set: %v", err)
		return false
	}

	return true
}

// getOnlineStatsFromRedis 从 Redis 获取在线统计数据
func getOnlineStatsFromRedis(channel string, date uint32) (int32, int64) {
	countKey := getRedisKey(RedisKeyStatsOnline, "count", channel, date)
	sumKey := getRedisKey(RedisKeyStatsOnline, "sum", channel, date)

	countStr, err := rdb.Default().Get(context.Background(), countKey).Result()
	if err != nil {
		return 0, 0
	}

	sumStr, err := rdb.Default().Get(context.Background(), sumKey).Result()
	if err != nil {
		return 0, 0
	}

	count, _ := strconv.ParseInt(countStr, 10, 32)
	sum, _ := strconv.ParseInt(sumStr, 10, 64)

	return int32(count), sum
}

// saveOnlineStatsToRedis 保存在线统计数据到 Redis
func saveOnlineStatsToRedis(channel string, date uint32, count int32, sum int64) {
	countKey := getRedisKey(RedisKeyStatsOnline, "count", channel, date)
	sumKey := getRedisKey(RedisKeyStatsOnline, "sum", channel, date)

	pipe := rdb.Default().Pipeline()
	pipe.Set(context.Background(), countKey, count, time.Duration(RedisKeyExpireSeconds)*time.Second)
	pipe.Set(context.Background(), sumKey, sum, time.Duration(RedisKeyExpireSeconds)*time.Second)

	_, err := pipe.Exec(context.Background())
	if err != nil {
		log.Errorf("Failed to save online stats to Redis: %v", err)
	}
}

// 全局缓存变量
var (
	dailyStatsCache       = make(map[string]*CachedStatistics)
	seatDailyStatsCache   = make(map[string]*CachedSeatStatistics)
	operationalStatsCache = make(map[string]*CachedOperationalStatistics)
	dailyStatsMutex       sync.RWMutex
	seatDailyStatsMutex   sync.RWMutex
	operationalStatsMutex sync.RWMutex
)

// 初始化缓存刷新定时器
func InitStatisticsCache() {
	// 每秒刷新一次缓存到数据库
	conc.Go(func() {
		defer utils.RecoverPanic()

		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		log.Info("Statistics cache flush goroutine started")

		for {
			select {
			case <-system.RootCtx().Done():
				log.Info("Statistics cache flush goroutine received shutdown signal")
				// 最后一次刷新缓存
				func() {
					defer func() {
						if r := recover(); r != nil {
							log.Errorf("Panic during final cache flush: %v", r)
						}
					}()
					flushCacheToDatabase()
				}()
				log.Info("Statistics cache flush goroutine exiting")
				return
			case <-ticker.C:
				// 非阻塞方式刷新缓存
				func() {
					defer func() {
						if r := recover(); r != nil {
							log.Errorf("Panic during cache flush: %v", r)
						}
					}()
					flushCacheToDatabase()
				}()
			}
		}
	})
	log.Info("Statistics cache initialized")
}

// 查询游戏统计数据api
func onStatisticsGameDataReq(body *pb.StatisticsGameDataReq, response func(*pb.GMStatisticsGameDataResp, error)) {
	// 解析日期范围
	startDate, endDate, err := common.ParseStatisticsDateRange(body.StartDate, body.EndDate)
	if err != nil {
		log.Errorf("Failed to parse date range: %v", err)
		response(nil, err)
		return
	}

	// 构建查询条件
	assetID := body.AssetID

	// 查询统计数据
	statistics, err := GetGameDailyStatistics(startDate, endDate, 0, assetID, body.Channel)
	if err != nil {
		log.Errorf("Failed to get game statistics: %v", err)
		response(nil, err)
		return
	}

	// 转换为pb格式
	var gameDataList []*pb.StatisticsGameData
	for _, stat := range statistics {
		rtp := 0.0
		if stat.TotalInput > 0 {
			rtp = stat.TotalOutput / stat.TotalInput
		}
		name := ""
		conf := table.Get[gsconf.GameInfoConf](stat.GameID)
		if conf != nil {
			name = conf.Name
		}
		gameData := &pb.StatisticsGameData{
			Date:         strconv.Itoa(int(stat.Date)),
			GameID:       stat.GameID,
			AssetID:      stat.AssetID,
			Channel:      stat.Channel,
			TotalInput:   stat.TotalInput,
			TotalOutput:  stat.TotalOutput,
			BetCount:     stat.BetCount,
			Recover:      stat.TotalInput - stat.TotalOutput,
			RTP:          rtp,
			MaxBet:       stat.MaxBet,
			MinBet:       stat.MinBet,
			AvgBet:       stat.AvgBet,
			TotalJackpot: stat.TotalJackpot,
			JackpotCount: stat.JackpotCount,
			BonusCount:   stat.BonusCount,
			GameName:     name,
		}
		gameDataList = append(gameDataList, gameData)
	}

	// 返回响应
	resp := &pb.GMStatisticsGameDataResp{
		Code: pb.SUCCESS,
		List: gameDataList,
	}

	response(resp, nil)
}

func onStatisticsGameSeatDataReq(body *pb.StatisticsGameSeatDataReq, response func(*pb.StatisticsGameSeatDataResp, error)) {
	startDate, endDate, err := common.ParseStatisticsDateRange(body.StartDate, body.EndDate)
	if err != nil {
		log.Errorf("Failed to parse date range: %v", err)
		response(nil, err)
		return
	}

	statistics, err := GetGameSeatDailyStatistics(startDate, endDate, 0, -1, body.AssetID)
	if err != nil {
		log.Errorf("Failed to get game seat statistics: %v", err)
		response(nil, err)
		return
	}

	var gameSeatDataList []*pb.StatisticsGameSeatData
	for _, stat := range statistics {
		gameSeatData := &pb.StatisticsGameSeatData{
			Date:         strconv.Itoa(int(stat.Date)),
			GameID:       stat.GameID,
			SeatID:       stat.SeatID,
			AssetID:      stat.AssetID,
			BetCount:     stat.BetCount,
			TotalInput:   stat.TotalInput,
			TotalOutput:  stat.TotalOutput,
			Recover:      stat.TotalInput - stat.TotalOutput,
			RTP:          stat.TotalOutput / stat.TotalInput,
			MaxBet:       stat.MaxBet,
			MinBet:       stat.MinBet,
			AvgBet:       stat.AvgBet,
			TotalJackpot: stat.TotalJackpot,
			JackpotCount: stat.JackpotCount,
			BonusCount:   stat.BonusCount,
		}
		gameSeatDataList = append(gameSeatDataList, gameSeatData)
	}

	resp := &pb.StatisticsGameSeatDataResp{
		Code: pb.SUCCESS,
		List: gameSeatDataList,
	}
	response(resp, nil)
}

// 统计运营数据
func onOperationalStatisticsReq(body *pb.OperationalStatisticsReq, response func(*pb.GMOperationalStatisticsResp, error)) {
	startDate, endDate, err := common.ParseStatisticsDateRange(body.StartDate, body.EndDate)
	if err != nil {
		log.Errorf("Failed to parse date range: %v", err)
		response(nil, err)
		return
	}
	statistics, err := GetOperationalStatistics(startDate, endDate, body.Channel)
	if err != nil {
		log.Errorf("Failed to get operational statistics: %v", err)
		response(nil, err)
		return
	}
	var operationalStatisticsList []*pb.OperationalStatistics
	for _, stat := range statistics {
		operationalStatistics := &pb.OperationalStatistics{
			Date:                   strconv.Itoa(int(stat.Date)),
			Channel:                stat.Channel,
			NewRegisterCount:       stat.NewRegisterCount,
			LoginUserCount:         stat.LoginUserCount,
			ActiveUserCount:        stat.ActiveUserCount,
			NewUserRechargeCount:   stat.NewUserRechargeCount,
			RechargeAmount:         stat.RechargeAmount,
			RechargeUserCount:      stat.RechargeUserCount,
			CreateRechargeOrders:   stat.CreateRechargeOrders,
			CompleteRechargeOrders: stat.CompleteRechargeOrders,
			NewUserRechargeAmount:  stat.NewUserRechargeAmount,
			FirstRechargeUserCount: stat.FirstRechargeUserCount,
			FirstRechargeAmount:    stat.FirstRechargeAmount,
			WithdrawUserCount:      stat.WithdrawUserCount,
			WithdrawAmount:         stat.WithdrawAmount,
			ARPU:                   stat.ARPU,
			NewUserARPU:            stat.NewUserARPU,
			ARPPU:                  stat.ARPPU,
			ACU:                    stat.ACU,
			PCU:                    stat.PCU,
			NewUserPayRate:         stat.NewUserPayRate,
		}
		operationalStatisticsList = append(operationalStatisticsList, operationalStatistics)
	}
	resp := &pb.GMOperationalStatisticsResp{
		Code: pb.SUCCESS,
		List: operationalStatisticsList,
	}
	response(resp, nil)
}

// 上报游戏数据
func onStatisticsReq(body *pb.StatisticsReq) {
	playData := body.PlayData

	// 原有的BI日志记录
	blockHash := ""
	if len(playData.BlockHash) > 16 {
		blockHash = playData.BlockHash[len(playData.BlockHash)-16:]
	}
	mbi.SpinResult(playData.EventID,
		playData.UserID,
		playData.GameId,
		playData.Platfrom,
		playData.Private,
		playData.Mode,
		int64(playData.SN),
		playData.BlockNumber,
		blockHash,
		playData.AssetID,
		playData.Input,
		playData.Output,
		playData.ExtInfo,
		playData.Channel,
		playData.SeatId,
		playData.Balance,
		playData.ParentID,
		playData.Name)
	// 新增：更新内存缓存统计数据
	updateStatisticsCache(playData)
}

// updateStatisticsCache 更新内存缓存统计数据
func updateStatisticsCache(playData *pb.StatisticsPlayData) {
	if playData.Input <= 0 && playData.Output <= 0 {
		return // 只统计有效投注
	}
	// 将playData.Date转换为当天UTC零点的时间戳
	var data time.Time
	if playData.Date == 0 {
		data = time.Now().UTC()
	} else {
		data = time.Unix(int64(playData.Date), 0).UTC()
	}
	playData.Date = uint32(common.Days(data))

	gameID := playData.GameId
	assetID := playData.AssetID
	channel := playData.Channel

	// 更新日统计缓存
	updateDailyStatsCache(playData.Date, int32(gameID), assetID, channel, playData)
	updateDailyStatsCache(0, int32(gameID), assetID, channel, playData) //历史游戏数据

	// 更新座位统计缓存
	if playData.SeatId >= 0 {
		updateSeatDailyStatsCache(playData.Date, int32(gameID), playData.SeatId, assetID, playData)
		updateSeatDailyStatsCache(0, int32(gameID), playData.SeatId, assetID, playData) //历史座位数据
	}
}

// updateDailyStatsCache 更新日游戏统计缓存
func updateDailyStatsCache(date uint32, gameID int32, assetID int32, channel string, playData *pb.StatisticsPlayData) {
	key := fmt.Sprintf("%d-%d-%d-%s", date, gameID, assetID, channel)

	dailyStatsMutex.Lock()
	defer dailyStatsMutex.Unlock()

	cached, exists := dailyStatsCache[key]
	if !exists {
		cached = &CachedStatistics{
			Date:         date,
			GameID:       gameID,
			AssetID:      assetID,
			Channel:      channel,
			BetCount:     0,
			TotalInput:   0,
			TotalOutput:  0,
			MaxBet:       0,
			MinBet:       0,
			TotalJackpot: 0,
			JackpotCount: 0,
			BonusCount:   0,
		}
		dailyStatsCache[key] = cached
	}

	// 累加统计数据
	cached.BetCount++
	cached.TotalInput += playData.Input
	cached.TotalOutput += playData.Output
	cached.TotalJackpot += playData.Jackpot
	if playData.Jackpot > 0 {
		cached.JackpotCount++
	}
	if playData.Input > 0 && playData.Output > 0 {
		if playData.Output/playData.Input > 10 {
			cached.BonusCount++
		}
	}
	// 更新最大最小单注
	if cached.MaxBet == 0 || playData.Input > cached.MaxBet {
		cached.MaxBet = playData.Input
	}
	if cached.MinBet == 0 || playData.Input < cached.MinBet {
		cached.MinBet = playData.Input
	}
}

// updateSeatDailyStatsCache 更新座位日统计缓存
func updateSeatDailyStatsCache(date uint32, gameID int32, seatID int32, assetID int32, playData *pb.StatisticsPlayData) {
	key := fmt.Sprintf("%d-%d-%d-%d", date, gameID, seatID, assetID)

	seatDailyStatsMutex.Lock()
	defer seatDailyStatsMutex.Unlock()

	cached, exists := seatDailyStatsCache[key]
	if !exists {
		cached = &CachedSeatStatistics{
			Date:         date,
			GameID:       gameID,
			SeatID:       seatID,
			AssetID:      assetID,
			BetCount:     0,
			TotalInput:   0,
			TotalOutput:  0,
			MaxBet:       0,
			MinBet:       0,
			TotalJackpot: 0,
			JackpotCount: 0,
			BonusCount:   0,
		}
		seatDailyStatsCache[key] = cached
	}

	// 累加统计数据
	cached.BetCount++
	cached.TotalInput += playData.Input
	cached.TotalOutput += playData.Output
	cached.TotalJackpot += playData.Jackpot
	if playData.Jackpot > 0 {
		cached.JackpotCount++
	}
	if playData.Input > 0 && playData.Output > 0 {
		if playData.Output/playData.Input > 10 {
			cached.BonusCount++
		}
	}

	// 更新最大最小单注
	if cached.MaxBet == 0 || playData.Input > cached.MaxBet {
		cached.MaxBet = playData.Input
	}
	if cached.MinBet == 0 || playData.Input < cached.MinBet {
		cached.MinBet = playData.Input
	}
}

// flushCacheToDatabase 将缓存数据刷新到数据库
func flushCacheToDatabase() {
	defer utils.RecoverPanic()

	// 检查系统是否正在关闭
	select {
	case <-system.RootCtx().Done():
		log.Debug("System shutting down, performing final cache flush")
	default:
	}

	startTime := time.Now()

	// 刷新日统计缓存
	flushDailyStatsCache()

	// 刷新座位统计缓存
	flushSeatDailyStatsCache()

	// 刷新运营统计缓存
	flushOperationalStatsCache()

	duration := time.Since(startTime)
	if duration > 100*time.Millisecond {
		log.Debugf("Cache flush took longer than expected: %v", duration)
	}
}

// flushDailyStatsCache 刷新日统计缓存到数据库
func flushDailyStatsCache() {
	dailyStatsMutex.Lock()
	// 复制当前缓存并清空，避免长时间持锁
	cacheToFlush := make(map[string]*CachedStatistics)
	for k, v := range dailyStatsCache {
		cacheToFlush[k] = v
	}
	dailyStatsCache = make(map[string]*CachedStatistics)
	dailyStatsMutex.Unlock()

	if len(cacheToFlush) == 0 {
		return
	}

	log.Debugf("Flushing %d daily statistics records to database", len(cacheToFlush))

	// 批量处理数据库操作
	batchUpdateDailyStatisticsDB(cacheToFlush)
}

// flushSeatDailyStatsCache 刷新座位统计缓存到数据库
func flushSeatDailyStatsCache() {
	seatDailyStatsMutex.Lock()
	// 复制当前缓存并清空，避免长时间持锁
	cacheToFlush := make(map[string]*CachedSeatStatistics)
	for k, v := range seatDailyStatsCache {
		cacheToFlush[k] = v
	}
	seatDailyStatsCache = make(map[string]*CachedSeatStatistics)
	seatDailyStatsMutex.Unlock()

	if len(cacheToFlush) == 0 {
		return
	}

	log.Debugf("Flushing %d seat daily statistics records to database", len(cacheToFlush))

	// 批量处理数据库操作
	batchUpdateSeatDailyStatisticsDB(cacheToFlush)
}

// batchUpdateDailyStatisticsDB 批量更新日统计数据到数据库
func batchUpdateDailyStatisticsDB(cacheData map[string]*CachedStatistics) {
	if len(cacheData) == 0 {
		return
	}

	startTime := time.Now()
	batchSize := 100 // 每批处理100条记录
	recordCount := len(cacheData)

	// 将缓存数据转换为切片以便分批处理
	var records []*CachedStatistics
	for _, cached := range cacheData {
		records = append(records, cached)
	}

	// 分批处理，减少单次事务大小
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}
		batch := records[i:end]

		err := processDailyStatisticsBatch(batch)
		if err != nil {
			log.Errorf("Failed to process daily statistics batch %d-%d: %v", i, end-1, err)
		}
	}

	duration := time.Since(startTime)
	log.Debugf("Upserted %d daily statistics records in %v (%.2f records/ms)",
		recordCount, duration, float64(recordCount)/float64(duration.Milliseconds()))
}

// processDailyStatisticsBatch 处理单批次的日统计数据
func processDailyStatisticsBatch(batch []*CachedStatistics) error {
	if len(batch) == 0 {
		return nil
	}

	// 构建批量UPSERT SQL
	valueStrings := make([]string, 0, len(batch))
	valueArgs := make([]interface{}, 0, len(batch)*13)

	for _, cached := range batch {
		avgBet := 0.0
		if cached.BetCount > 0 {
			avgBet = cached.TotalInput / float64(cached.BetCount)
		}

		valueStrings = append(valueStrings, "(?, ?, '', ?, '', '', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())")
		valueArgs = append(valueArgs,
			cached.Date, cached.GameID, cached.AssetID, cached.Channel,
			cached.BetCount, cached.TotalInput, cached.TotalOutput,
			cached.MaxBet, cached.MinBet, avgBet,
			cached.TotalJackpot, cached.JackpotCount, cached.BonusCount,
		)
	}

	sql := `
		INSERT INTO game_daily_statistics (
			date, game_id, game_name, asset_id, asset_name, platform, channel,
			bet_count, total_input, total_output, max_bet, min_bet, avg_bet,
			total_jackpot, jackpot_count, bonus_count, created_at, updated_at
		) VALUES ` + fmt.Sprintf("%s", valueStrings[0])

	for i := 1; i < len(valueStrings); i++ {
		sql += ", " + valueStrings[i]
	}

	sql += `
		ON DUPLICATE KEY UPDATE
			bet_count = bet_count + VALUES(bet_count),
			total_input = total_input + VALUES(total_input),
			total_output = total_output + VALUES(total_output),
			max_bet = GREATEST(max_bet, VALUES(max_bet)),
			min_bet = CASE WHEN min_bet = 0 THEN VALUES(min_bet) ELSE LEAST(min_bet, VALUES(min_bet)) END,
			avg_bet = total_input / bet_count,
			total_jackpot = total_jackpot + VALUES(total_jackpot),
			jackpot_count = jackpot_count + VALUES(jackpot_count),
			bonus_count = bonus_count + VALUES(bonus_count),
			updated_at = NOW()
	`

	// 在事务中执行批量操作，添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx := mdb.Default().WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := tx.Exec(sql, valueArgs...).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("batch upsert failed: %v", err)
	}

	return tx.Commit().Error
}

// flushOperationalStatsCache 刷新运营统计缓存到数据库
func flushOperationalStatsCache() {
	operationalStatsMutex.Lock()
	// 复制当前缓存并清空，避免长时间持锁
	cacheToFlush := make(map[string]*CachedOperationalStatistics)
	for k, v := range operationalStatsCache {
		// 深拷贝缓存数据，保留用户去重信息
		cachedCopy := &CachedOperationalStatistics{
			Date:                   v.Date,
			Channel:                v.Channel,
			NewRegisterCount:       v.NewRegisterCount,
			LoginUserCount:         v.LoginUserCount,
			ActiveUserCount:        v.ActiveUserCount,
			RechargeUserCount:      v.RechargeUserCount,
			RechargeAmount:         v.RechargeAmount,
			NewUserRechargeAmount:  v.NewUserRechargeAmount,
			NewUserRechargeCount:   v.NewUserRechargeCount,
			CreateRechargeOrders:   v.CreateRechargeOrders,
			CompleteRechargeOrders: v.CompleteRechargeOrders,
			FirstRechargeUserCount: v.FirstRechargeUserCount,
			FirstRechargeAmount:    v.FirstRechargeAmount,
			WithdrawUserCount:      v.WithdrawUserCount,
			WithdrawAmount:         v.WithdrawAmount,
			ACU:                    v.ACU,
			PCU:                    v.PCU,
			OnlineReportCount:      v.OnlineReportCount,
			TotalOnlineSum:         v.TotalOnlineSum,
			// 注意：去重map不需要拷贝到数据库，但需要在缓存中保留
		}
		cacheToFlush[k] = cachedCopy

		// 重置缓存中的计数器，但保留去重集合
		if v.Date != 0 { // 只重置日统计，历史汇总保持累积
			v.NewRegisterCount = 0
			v.LoginUserCount = 0
			v.ActiveUserCount = 0
			v.RechargeUserCount = 0
			v.RechargeAmount = 0
			v.NewUserRechargeAmount = 0
			v.NewUserRechargeCount = 0
			v.CreateRechargeOrders = 0
			v.CompleteRechargeOrders = 0
			v.FirstRechargeUserCount = 0
			v.FirstRechargeAmount = 0
			v.WithdrawUserCount = 0
			v.WithdrawAmount = 0
			v.ACU = 0
			v.PCU = 0
			// 保留 RegisteredUsers, LoginUsers 等去重集合不清空
		}
	}
	operationalStatsMutex.Unlock()

	if len(cacheToFlush) == 0 {
		return
	}

	log.Debugf("Flushing %d operational statistics records to database", len(cacheToFlush))

	// 批量处理数据库操作，增加错误处理
	err := batchUpdateOperationalStatisticsDB(cacheToFlush)
	if err != nil {
		log.Errorf("Failed to flush operational statistics cache: %v", err)
		// 可以考虑将失败的数据重新加入缓存，或者记录到错误队列
	} else {
		log.Debugf("Successfully flushed %d operational statistics records", len(cacheToFlush))
	}
}

// batchUpdateOperationalStatisticsDB 批量更新运营统计数据到数据库
func batchUpdateOperationalStatisticsDB(cacheData map[string]*CachedOperationalStatistics) error {
	if len(cacheData) == 0 {
		return nil
	}

	startTime := time.Now()
	batchSize := 100 // 每批处理100条记录
	recordCount := len(cacheData)

	// 将缓存数据转换为切片以便分批处理
	var records []*CachedOperationalStatistics
	for _, cached := range cacheData {
		records = append(records, cached)
	}

	// 分批处理，减少单次事务大小
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}
		batch := records[i:end]

		err := processOperationalStatisticsBatch(batch)
		if err != nil {
			log.Errorf("Failed to process operational statistics batch %d-%d: %v", i, end-1, err)
			return fmt.Errorf("batch processing failed at %d-%d: %w", i, end-1, err)
		} else {
			log.Debugf("Successfully processed operational statistics batch %d-%d (%d records)",
				i, end-1, len(batch))
		}
	}

	duration := time.Since(startTime)
	log.Debugf("Upserted %d operational statistics records in %v (%.2f records/ms)",
		recordCount, duration, float64(recordCount)/float64(duration.Milliseconds()))

	return nil
}

// processOperationalStatisticsBatch 处理单批次的运营统计数据
func processOperationalStatisticsBatch(batch []*CachedOperationalStatistics) error {
	if len(batch) == 0 {
		return nil
	}

	// 在事务中执行批量操作
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx := mdb.Default().WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 处理每一条记录
	for _, cached := range batch {
		// 计算 ARPU、ARPPU 等字段
		arpu := 0.0
		if cached.ActiveUserCount > 0 {
			arpu = cached.RechargeAmount / float64(cached.ActiveUserCount)
		}

		newUserARPU := 0.0
		if cached.NewRegisterCount > 0 {
			newUserARPU = cached.NewUserRechargeAmount / float64(cached.NewRegisterCount)
		}

		arppu := 0.0
		if cached.RechargeUserCount > 0 {
			arppu = cached.RechargeAmount / float64(cached.RechargeUserCount)
		}

		newUserPayRate := 0.0
		if cached.NewRegisterCount > 0 {
			newUserPayRate = float64(cached.NewUserRechargeCount) / float64(cached.NewRegisterCount)
		}

		// 创建数据模型
		record := &OperationalStatistics{
			Date:                   cached.Date,
			Channel:                cached.Channel,
			NewRegisterCount:       cached.NewRegisterCount,
			LoginUserCount:         cached.LoginUserCount,
			ActiveUserCount:        cached.ActiveUserCount,
			NewUserRechargeCount:   cached.NewUserRechargeCount,
			RechargeAmount:         cached.RechargeAmount,
			RechargeUserCount:      cached.RechargeUserCount,
			CreateRechargeOrders:   cached.CreateRechargeOrders,
			CompleteRechargeOrders: cached.CompleteRechargeOrders,
			NewUserRechargeAmount:  cached.NewUserRechargeAmount,
			FirstRechargeUserCount: cached.FirstRechargeUserCount,
			FirstRechargeAmount:    cached.FirstRechargeAmount,
			WithdrawUserCount:      cached.WithdrawUserCount,
			WithdrawAmount:         cached.WithdrawAmount,
			ARPU:                   arpu,
			NewUserARPU:            newUserARPU,
			ARPPU:                  arppu,
			ACU:                    cached.ACU,
			PCU:                    cached.PCU,
			NewUserPayRate:         newUserPayRate,
		}

		// 使用 GORM 的 ON CONFLICT 功能进行 UPSERT
		result := tx.Table(OperationalStatisticsTableName).
			Where("date = ? AND channel = ?", cached.Date, cached.Channel).
			First(&OperationalStatistics{})

		if result.Error != nil {
			// 记录不存在，创建新记录
			if err := tx.Create(record).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to create operational statistics: %v", err)
			}
		} else {
			// 记录存在，更新现有记录
			if err := tx.Table(OperationalStatisticsTableName).
				Where("date = ? AND channel = ?", cached.Date, cached.Channel).
				Updates(map[string]interface{}{
					"new_register_count":        gorm.Expr("new_register_count + ?", cached.NewRegisterCount),
					"login_user_count":          gorm.Expr("login_user_count + ?", cached.LoginUserCount),
					"active_user_count":         gorm.Expr("active_user_count + ?", cached.ActiveUserCount),
					"new_user_recharge_count":   gorm.Expr("new_user_recharge_count + ?", cached.NewUserRechargeCount),
					"recharge_amount":           gorm.Expr("recharge_amount + ?", cached.RechargeAmount),
					"recharge_user_count":       gorm.Expr("recharge_user_count + ?", cached.RechargeUserCount),
					"create_recharge_orders":    gorm.Expr("create_recharge_orders + ?", cached.CreateRechargeOrders),
					"complete_recharge_orders":  gorm.Expr("complete_recharge_orders + ?", cached.CompleteRechargeOrders),
					"new_user_recharge_amount":  gorm.Expr("new_user_recharge_amount + ?", cached.NewUserRechargeAmount),
					"first_recharge_user_count": gorm.Expr("first_recharge_user_count + ?", cached.FirstRechargeUserCount),
					"first_recharge_amount":     gorm.Expr("first_recharge_amount + ?", cached.FirstRechargeAmount),
					"withdraw_user_count":       gorm.Expr("withdraw_user_count + ?", cached.WithdrawUserCount),
					"withdraw_amount":           gorm.Expr("withdraw_amount + ?", cached.WithdrawAmount),
					"acu":                       gorm.Expr("GREATEST(acu, ?)", cached.ACU),
					"pcu":                       gorm.Expr("GREATEST(pcu, ?)", cached.PCU),
					"arpu":                      arpu,
					"new_user_arpu":             newUserARPU,
					"arppu":                     arppu,
					"new_user_pay_rate":         newUserPayRate,
				}).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update operational statistics: %v", err)
			}
		}
	}

	return tx.Commit().Error
}

// CheckOperationalStatisticsArchive 检查运营统计存档状态
func CheckOperationalStatisticsArchive(channel string, date uint32) (*OperationalStatistics, error) {
	var stats OperationalStatistics

	err := mdb.Default().Table(OperationalStatisticsTableName).
		Where("date = ? AND channel = ?", date, channel).
		First(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("failed to query operational statistics: %w", err)
	}

	log.Debugf("Archive check - Channel: %s, Date: %d, NewRegister: %d, Login: %d, Recharge: %.2f",
		stats.Channel, stats.Date, stats.NewRegisterCount, stats.LoginUserCount, stats.RechargeAmount)

	return &stats, nil
}

// GetOperationalStatisticsCacheStatus 获取当前缓存状态（用于调试）
func GetOperationalStatisticsCacheStatus() map[string]*CachedOperationalStatistics {
	operationalStatsMutex.RLock()
	defer operationalStatsMutex.RUnlock()

	// 创建副本避免并发问题
	status := make(map[string]*CachedOperationalStatistics)
	for k, v := range operationalStatsCache {
		// 简化拷贝，只拷贝主要字段
		status[k] = &CachedOperationalStatistics{
			Date:              v.Date,
			Channel:           v.Channel,
			NewRegisterCount:  v.NewRegisterCount,
			LoginUserCount:    v.LoginUserCount,
			RechargeUserCount: v.RechargeUserCount,
			RechargeAmount:    v.RechargeAmount,
			RegisteredUsers:   nil, // 不暴露内部map
			LoginUsers:        nil,
			RechargeUsers:     nil,
		}
	}

	log.Debugf("Current operational cache has %d entries", len(status))
	return status
}

// ForceFlushOperationalStats 强制刷新运营统计到数据库（用于调试和紧急情况）
func ForceFlushOperationalStats() error {
	log.Info("Force flushing operational statistics cache to database")

	// 记录刷新前的缓存状态
	operationalStatsMutex.RLock()
	cacheCount := len(operationalStatsCache)
	operationalStatsMutex.RUnlock()

	if cacheCount == 0 {
		log.Info("No operational statistics cache data to flush")
		return nil
	}

	// 执行刷新
	flushOperationalStatsCache()

	log.Infof("Force flush completed, processed %d cache entries", cacheCount)
	return nil
}

// onStatisticsUserEventReq 处理用户事件上报（原子事件）
func onStatisticsUserEventReq(body *pb.StatisticsUserEventReq) {
	// 将事件时间转换为当天UTC零点的时间戳
	var eventTime time.Time
	if body.Timestamp == 0 {
		eventTime = time.Now().UTC()
	} else {
		eventTime = time.Unix(int64(body.Timestamp), 0).UTC()
	}
	dateKey := uint32(common.Days(eventTime))
	// 根据事件类型处理不同的统计逻辑
	switch body.EventType {
	case pb.USER_REGISTER:
		handleUserRegisterEvent(dateKey, body)
	case pb.USER_LOGIN:
		handleUserLoginEvent(dateKey, body)
	case pb.USER_RECHARGE_CREATE:
		handleRechargeCreateEvent(dateKey, body)
	case pb.USER_RECHARGE_COMPLETE:
		handleRechargeCompleteEvent(dateKey, body)
	case pb.USER_WITHDRAW:
		handleUserWithdrawEvent(dateKey, body)
	case pb.USER_ONLINE:
		handleUserOnlineEvent(dateKey, body)
	default:
		log.Warnf("Unknown user event type: %v", body.EventType)
	}
}

// handleUserRegisterEvent 处理用户注册事件
func handleUserRegisterEvent(dateKey uint32, event *pb.StatisticsUserEventReq) {
	log.Debugf("Processing user register event: UserID=%d, Channel=%s", event.UserID, event.Channel)

	// 使用 Redis 检查是否已经统计过该用户
	if !isUserInRedisSet("register", event.Channel, dateKey, event.UserID) {
		// 用户未被统计过，添加到 Redis 并更新统计
		if addUserToRedisSet("register", event.Channel, dateKey, event.UserID) {
			incrementOperationalStats(dateKey, event.Channel, func(stats *CachedOperationalStatistics) {
				stats.NewRegisterCount++
			})
			mbi.UserRegister(event.UserID, event.Channel, event.ParentID)
		}
	}
}

// handleUserLoginEvent 处理用户登录事件
func handleUserLoginEvent(dateKey uint32, event *pb.StatisticsUserEventReq) {
	log.Debugf("Processing user login event: UserID=%d, Channel=%s", event.UserID, event.Channel)

	// 使用 Redis 检查是否已经统计过该用户的登录
	if !isUserInRedisSet("login", event.Channel, dateKey, event.UserID) {
		// 用户未被统计过，添加到 Redis 并更新统计
		if addUserToRedisSet("login", event.Channel, dateKey, event.UserID) {
			incrementOperationalStats(dateKey, event.Channel, func(stats *CachedOperationalStatistics) {
				stats.LoginUserCount++
				stats.ActiveUserCount++ // 假设登录用户就是活跃用户
			})
			mbi.UserLogin(event.UserID, event.Channel, event.ParentID)
		}
	}
}

// handleRechargeCreateEvent 处理充值订单创建事件
func handleRechargeCreateEvent(dateKey uint32, event *pb.StatisticsUserEventReq) {
	log.Debugf("Processing recharge create event: UserID=%d, Amount=%.2f", event.UserID, event.Amount)

	incrementOperationalStats(dateKey, event.Channel, func(stats *CachedOperationalStatistics) {
		stats.CreateRechargeOrders++
	})
}

// handleRechargeCompleteEvent 处理充值订单完成事件
func handleRechargeCompleteEvent(dateKey uint32, event *pb.StatisticsUserEventReq) {
	log.Debugf("Processing recharge complete event: UserID=%d, Amount=%.2f, IsFirst=%v", event.UserID, event.Amount, event.IsFirstTime)

	incrementOperationalStats(dateKey, event.Channel, func(stats *CachedOperationalStatistics) {
		stats.RechargeAmount += event.Amount
		stats.CompleteRechargeOrders++

		// 使用 Redis 检查是否为新付费用户
		if !isUserInRedisSet("recharge", event.Channel, dateKey, event.UserID) {
			if addUserToRedisSet("recharge", event.Channel, dateKey, event.UserID) {
				stats.RechargeUserCount++
			}
		}

		// 处理首次付费
		if event.IsFirstTime {
			stats.FirstRechargeUserCount++
			stats.FirstRechargeAmount += event.Amount
		}

		// 简化处理：所有充值都计入新用户充值（可根据业务需求调整）
		stats.NewUserRechargeCount++
		stats.NewUserRechargeAmount += event.Amount
	})
}

// handleUserWithdrawEvent 处理用户提现事件
func handleUserWithdrawEvent(dateKey uint32, event *pb.StatisticsUserEventReq) {
	log.Debugf("Processing user withdraw event: UserID=%d, Amount=%.2f", event.UserID, event.Amount)

	incrementOperationalStats(dateKey, event.Channel, func(stats *CachedOperationalStatistics) {
		stats.WithdrawAmount += event.Amount

		// 使用 Redis 检查是否为当日新提现用户
		if !isUserInRedisSet("withdraw", event.Channel, dateKey, event.UserID) {
			if addUserToRedisSet("withdraw", event.Channel, dateKey, event.UserID) {
				stats.WithdrawUserCount++
			}
		}
	})
}

// handleUserOnlineEvent 处理用户在线事件（通常是定时上报的在线人数）
func handleUserOnlineEvent(dateKey uint32, event *pb.StatisticsUserEventReq) {
	log.Debugf("Processing user online event: Channel=%s, OnlineCount=%d", event.Channel, event.OnlineCount)

	incrementOperationalStats(dateKey, event.Channel, func(stats *CachedOperationalStatistics) {
		// 从 Redis 恢复在线统计状态（防止重启丢失）
		if stats.OnlineReportCount == 0 && stats.TotalOnlineSum == 0 {
			redisCount, redisSum := getOnlineStatsFromRedis(event.Channel, dateKey)
			if redisCount > 0 {
				stats.OnlineReportCount = redisCount
				stats.TotalOnlineSum = redisSum
				log.Debugf("Restored online stats from Redis: Count=%d, Sum=%d", redisCount, redisSum)
			}
		}

		// 累加在线人数和上报次数
		stats.OnlineReportCount++
		stats.TotalOnlineSum += int64(event.OnlineCount)

		// 计算平均在线人数 (ACU = 总在线人数 / 上报次数)
		if stats.OnlineReportCount > 0 {
			stats.ACU = int32(stats.TotalOnlineSum / int64(stats.OnlineReportCount))
		}

		// 更新峰值在线人数 (PCU = 历史最高值)
		if event.OnlineCount > stats.PCU {
			stats.PCU = event.OnlineCount
		}

		// 保存最新的在线统计到 Redis
		saveOnlineStatsToRedis(event.Channel, dateKey, stats.OnlineReportCount, stats.TotalOnlineSum)

		log.Debugf("Online stats updated - Channel: %s, Current: %d, ACU: %d, PCU: %d, Reports: %d",
			event.Channel, event.OnlineCount, stats.ACU, stats.PCU, stats.OnlineReportCount)
	})
}

// incrementOperationalStats 安全地更新运营统计数据
func incrementOperationalStats(dateKey uint32, channel string, updateFunc func(*CachedOperationalStatistics)) {
	key := fmt.Sprintf("%d-%s", dateKey, channel)

	operationalStatsMutex.Lock()
	defer operationalStatsMutex.Unlock()

	stats, exists := operationalStatsCache[key]
	if !exists {
		stats = &CachedOperationalStatistics{
			Date:    dateKey,
			Channel: channel,
		}
		operationalStatsCache[key] = stats
	}

	updateFunc(stats)
}

// GetOperationalStatistics 获取运营统计数据
func GetOperationalStatistics(startDate, endDate int32, channel string) ([]*OperationalStatistics, error) {
	var statistics []*OperationalStatistics

	query := mdb.Default().Table(OperationalStatisticsTableName).Where("date >= ? AND date <= ?", startDate, endDate)

	if channel != "" {
		query = query.Where("channel = ?", channel)
	}

	err := query.Order("date DESC, channel ASC").Find(&statistics).Error
	return statistics, err
}

// GetGameDailyStatistics 获取游戏日统计数据
func GetGameDailyStatistics(startDate, endDate int32, gameID int32, assetID int32, channel string) ([]*GameDailyStatistics, error) {
	var statistics []*GameDailyStatistics
	// var total int64

	query := mdb.Default().Table(GameDailyStatisticsTableName).Where("date >= ? AND date <= ?", startDate, endDate)

	if gameID > 0 {
		query = query.Where("game_id = ?", gameID)
	}
	if assetID > 0 {
		query = query.Where("asset_id = ?", assetID)
	}
	if channel != "" {
		query = query.Where("channel = ?", channel)
	}
	// 计算总数
	// err := query.Count(&total).Error
	// if err != nil {
	// 	return nil, 0, err
	// }
	// 获取分页数据
	// offset := (page - 1) * pageSize
	err := query.Order("date DESC, game_id ASC, asset_id ASC").Find(&statistics).Error
	return statistics, err
}

// GetGameSeatDailyStatistics 获取游戏座位日统计数据
func GetGameSeatDailyStatistics(startDate, endDate int32, gameID int32, seatID int32, assetID int32) ([]*GameSeatDailyStatistics, error) {
	var statistics []*GameSeatDailyStatistics
	// var total int64

	query := mdb.Default().Table(GameSeatDailyStatisticsTableName).Where("date >= ? AND date <= ?", startDate, endDate)

	if gameID > 0 {
		query = query.Where("game_id = ?", gameID)
	}
	if seatID >= 0 {
		query = query.Where("seat_id = ?", seatID)
	}
	if assetID > 0 {
		query = query.Where("asset_id = ?", assetID)
	}

	// 计算总数
	// err := query.Count(&total).Error
	// if err != nil {
	// 	return nil, 0, err
	// }

	// 获取分页数据
	// offset := (page - 1) * pageSize
	err := query.Order("date DESC, game_id ASC, seat_id ASC, asset_id ASC").Find(&statistics).Error

	return statistics, err
}

// batchUpdateSeatDailyStatisticsDB 批量更新座位日统计数据到数据库
func batchUpdateSeatDailyStatisticsDB(cacheData map[string]*CachedSeatStatistics) {
	if len(cacheData) == 0 {
		return
	}

	startTime := time.Now()
	batchSize := 100 // 每批处理100条记录
	recordCount := len(cacheData)

	// 将缓存数据转换为切片以便分批处理
	var records []*CachedSeatStatistics
	for _, cached := range cacheData {
		records = append(records, cached)
	}

	// 分批处理，减少单次事务大小
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}
		batch := records[i:end]

		err := processSeatDailyStatisticsBatch(batch)
		if err != nil {
			log.Errorf("Failed to process seat daily statistics batch %d-%d: %v", i, end-1, err)
		}
	}

	duration := time.Since(startTime)
	log.Debugf("Upserted %d seat daily statistics records in %v (%.2f records/ms)",
		recordCount, duration, float64(recordCount)/float64(duration.Milliseconds()))
}

// processSeatDailyStatisticsBatch 处理单批次的座位日统计数据
func processSeatDailyStatisticsBatch(batch []*CachedSeatStatistics) error {
	if len(batch) == 0 {
		return nil
	}

	// 构建批量UPSERT SQL
	valueStrings := make([]string, 0, len(batch))
	valueArgs := make([]interface{}, 0, len(batch)*13)

	for _, cached := range batch {
		avgBet := 0.0
		if cached.BetCount > 0 {
			avgBet = cached.TotalInput / float64(cached.BetCount)
		}

		valueStrings = append(valueStrings, "(?, ?, '', ?, ?, '', '', ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())")
		valueArgs = append(valueArgs,
			cached.Date, cached.GameID, cached.SeatID, cached.AssetID,
			cached.BetCount, cached.TotalInput, cached.TotalOutput,
			cached.MaxBet, cached.MinBet, avgBet,
			cached.TotalJackpot, cached.JackpotCount, cached.BonusCount,
		)
	}

	sql := `
		INSERT INTO game_seat_daily_statistics (
			date, game_id, game_name, seat_id, asset_id, asset_name, platform,
			bet_count, total_input, total_output, max_bet, min_bet, avg_bet,
			total_jackpot, jackpot_count, bonus_count, created_at, updated_at
		) VALUES ` + fmt.Sprintf("%s", valueStrings[0])

	for i := 1; i < len(valueStrings); i++ {
		sql += ", " + valueStrings[i]
	}

	sql += `
		ON DUPLICATE KEY UPDATE
			bet_count = bet_count + VALUES(bet_count),
			total_input = total_input + VALUES(total_input),
			total_output = total_output + VALUES(total_output),
			max_bet = GREATEST(max_bet, VALUES(max_bet)),
			min_bet = CASE WHEN min_bet = 0 THEN VALUES(min_bet) ELSE LEAST(min_bet, VALUES(min_bet)) END,
			avg_bet = total_input / bet_count,
			total_jackpot = total_jackpot + VALUES(total_jackpot),
			jackpot_count = jackpot_count + VALUES(jackpot_count),
			bonus_count = bonus_count + VALUES(bonus_count),
			updated_at = NOW()
	`

	// 在事务中执行批量操作，添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx := mdb.Default().WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := tx.Exec(sql, valueArgs...).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("batch upsert failed: %v", err)
	}

	return tx.Commit().Error
}
