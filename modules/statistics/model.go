package statistics

import "time"

const GameDailyStatisticsTableName = "game_daily_statistics"
const GameSeatDailyStatisticsTableName = "game_seat_daily_statistics"
const OperationalStatisticsTableName = "operational_statistics"

// GameDailyStatistics 游戏日统计数据模型
type GameDailyStatistics struct {
	ID        uint   `gorm:"column:id;primary_key;autoIncrement"`
	Date      uint32 `gorm:"column:date;not null;index:idx_date_game_asset_channel"`            // 日期 YYYYMMDD ""为汇总所有
	GameID    int32  `gorm:"column:game_id;not null;index:idx_date_game_asset_channel"`         // 游戏ID
	GameName  string `gorm:"column:game_name;type:varchar(100)"`                                // 游戏名称
	AssetID   int32  `gorm:"column:asset_id;not null;index:idx_date_game_asset_channel"`        // 币种ID
	AssetName string `gorm:"column:asset_name;type:varchar(50)"`                                // 币种名称
	Platform  string `gorm:"column:platform;type:varchar(50)"`                                  // 平台
	Channel   string `gorm:"column:channel;type:varchar(50);index:idx_date_game_asset_channel"` // 渠道

	// 统计数据
	BetCount    int32   `gorm:"column:bet_count;default:0"`                       // 下注次数
	TotalInput  float64 `gorm:"column:total_input;type:decimal(20,8);default:0"`  // 下注金额
	TotalOutput float64 `gorm:"column:total_output;type:decimal(20,8);default:0"` // 返奖金额

	// 额外统计
	MaxBet       float64 `gorm:"column:max_bet;type:decimal(20,8);default:0"`       // 最大单注
	MinBet       float64 `gorm:"column:min_bet;type:decimal(20,8);default:0"`       // 最小单注
	AvgBet       float64 `gorm:"column:avg_bet;type:decimal(20,8);default:0"`       // 平均单注
	TotalJackpot float64 `gorm:"column:total_jackpot;type:decimal(20,8);default:0"` // 总奖池贡献
	JackpotCount int32   `gorm:"column:jackpot_count;default:0"`                    // 奖池中奖次数
	BonusCount   int32   `gorm:"column:bonus_count;default:0"`                      // 奖励触发次数

	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (GameDailyStatistics) TableName() string {
	return GameDailyStatisticsTableName
}

// GameSeatDailyStatistics 游戏座位日统计数据模型
type GameSeatDailyStatistics struct {
	ID        uint   `gorm:"column:id;primary_key;autoIncrement"`
	Date      uint32 `gorm:"column:date;not null;index:idx_date_game_seat_asset"`     // 日期时间戳
	GameID    int32  `gorm:"column:game_id;not null;index:idx_date_game_seat_asset"`  // 游戏ID
	GameName  string `gorm:"column:game_name;type:varchar(100)"`                      // 游戏名称
	SeatID    int32  `gorm:"column:seat_id;not null;index:idx_date_game_seat_asset"`  // 座位ID
	AssetID   int32  `gorm:"column:asset_id;not null;index:idx_date_game_seat_asset"` // 币种ID
	AssetName string `gorm:"column:asset_name;type:varchar(50)"`                      // 币种名称
	Platform  string `gorm:"column:platform;type:varchar(50)"`                        // 平台

	// 统计数据
	BetCount    int32   `gorm:"column:bet_count;default:0"`                       // 下注次数
	TotalInput  float64 `gorm:"column:total_input;type:decimal(20,8);default:0"`  // 下注金额
	TotalOutput float64 `gorm:"column:total_output;type:decimal(20,8);default:0"` // 返奖金额

	// 额外统计
	MaxBet       float64 `gorm:"column:max_bet;type:decimal(20,8);default:0"`       // 最大单注
	MinBet       float64 `gorm:"column:min_bet;type:decimal(20,8);default:0"`       // 最小单注
	AvgBet       float64 `gorm:"column:avg_bet;type:decimal(20,8);default:0"`       // 平均单注
	TotalJackpot float64 `gorm:"column:total_jackpot;type:decimal(20,8);default:0"` // 总奖池贡献
	JackpotCount int32   `gorm:"column:jackpot_count;default:0"`                    // 奖池中奖次数
	BonusCount   int32   `gorm:"column:bonus_count;default:0"`                      // 奖励触发次数

	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (GameSeatDailyStatistics) TableName() string {
	return GameSeatDailyStatisticsTableName
}

// OperationalStatistics 运营数据统计模型
type OperationalStatistics struct {
	ID      uint   `gorm:"column:id;primary_key;autoIncrement"`
	Date    uint32 `gorm:"column:date;not null;index:idx_date_channel"`            // 日期 YYYYMMDD
	Channel string `gorm:"column:channel;type:varchar(50);index:idx_date_channel"` // 渠道

	// 用户相关统计
	NewRegisterCount int32 `gorm:"column:new_register_count;default:0"` // 新增注册
	LoginUserCount   int32 `gorm:"column:login_user_count;default:0"`   // 登录人数
	ActiveUserCount  int32 `gorm:"column:active_user_count;default:0"`  // 日活用户(DAU)

	// 付费相关统计
	NewUserRechargeCount   int32   `gorm:"column:new_user_recharge_count;default:0"`                     // 新增付费人数
	RechargeAmount         float64 `gorm:"column:recharge_amount;type:decimal(20,8);default:0"`          // 付费金额
	RechargeUserCount      int32   `gorm:"column:recharge_user_count;default:0"`                         // 充值用户数
	CreateRechargeOrders   int32   `gorm:"column:create_recharge_orders;default:0"`                      // 创建充值订单数
	CompleteRechargeOrders int32   `gorm:"column:complete_recharge_orders;default:0"`                    // 完成充值订单数
	NewUserRechargeAmount  float64 `gorm:"column:new_user_recharge_amount;type:decimal(20,8);default:0"` // 新增付费金额/新用户充值金额
	FirstRechargeUserCount int32   `gorm:"column:first_recharge_user_count;default:0"`                   // 首次付费人数
	FirstRechargeAmount    float64 `gorm:"column:first_recharge_amount;type:decimal(20,8);default:0"`    // 首次付费金额

	// 提现相关统计
	WithdrawUserCount int32   `gorm:"column:withdraw_user_count;default:0"`                // 提现人数
	WithdrawAmount    float64 `gorm:"column:withdraw_amount;type:decimal(20,8);default:0"` // 提现金额

	// 计算字段（可由其他字段计算得出）
	ARPU           float64 `gorm:"column:arpu;type:decimal(20,8);default:0"`              // 平均每用户收入
	NewUserARPU    float64 `gorm:"column:new_user_arpu;type:decimal(20,8);default:0"`     // 新增用户ARPU
	ARPPU          float64 `gorm:"column:arppu;type:decimal(20,8);default:0"`             // 平均每付费用户收入
	ACU            int32   `gorm:"column:acu;default:0"`                                  // 平均在线用户数
	PCU            int32   `gorm:"column:pcu;default:0"`                                  // 最高在线用户数
	NewUserPayRate float64 `gorm:"column:new_user_pay_rate;type:decimal(10,4);default:0"` // 每日新增充值比例

	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (OperationalStatistics) TableName() string {
	return OperationalStatisticsTableName
}
