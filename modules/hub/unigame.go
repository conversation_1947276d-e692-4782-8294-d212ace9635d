package hub

import (
	"s2/common/cache"
	"s2/pb"
	"strconv"

	"github.com/bluele/gcache"
	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

var (
	unigameCache = gcache.New(5000).LRU().Build()
)

func (m *module) InitUnigame() {
	sub := m.Group("/unigame")
	// sub.POST("/asset_change_sync", handleUnigameChangeUserAsset)
	// sub.POST("/asset_change", handleUnigameChangeUserAsset)
	// sub.POST("/asset_balance", handleUnigameUserAssetBalance)
	// sub.POST("/result", handleUnigameResultSync)

	sub.POST("/result_async", handleUnigameSyncAssetChange)
}

func handleUnigameSyncAssetChange(ctx *gin.Context) {
	body := []*pb.UnigameGameResult{}
	err := ctx.BindJSON(&body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	ctx.JSON(200, pb.Error{Code: pb.SUCCESS})
	m := map[int64][]*pb.UnigameGameResult{}
	for _, item := range body {
		uid, err := strconv.Atoi(item.DataxUid)
		if err != nil {
			log.Error("item.DataxUid:", item.DataxUid, "err:", err)
			continue
		}
		m[int64(uid)] = append(m[int64(uid)], item)
	}
	for uid, list := range m {
		bc, err := cache.QueryUserBasicInfo(uid)
		if err != nil {
			log.Error(err)
			continue
		}
		message.Stream.Cast(bc.ServerID, &pb.UnigameResultSyncMsg{
			UserID: uid,
			Result: list,
		})
	}
}

/*
func handleUnigameChangeUserAsset(ctx *gin.Context) {
	body := &pb.UnigameAssetChangeReq{}
	err := ctx.BindJSON(body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	uid, assetID, err := getUserInfoByUnigameID(body.UnigameID)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	body.UserID = uid
	body.AssetID = assetID
	resp, err := message.Request[pb.UnigameAssetChangeResp](bc.ServerID, body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	ctx.JSON(200, resp)
}

func handleUnigameUserAssetBalance(ctx *gin.Context) {
	body := &pb.UnigameAssetBalanceReq{}
	err := ctx.BindJSON(body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	uid, assetID, err := getUserInfoByUnigameID(body.UnigameID)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	body.UserID = uid
	body.AssetID = assetID
	resp, err := message.Request[pb.UnigameAssetBalanceResp](bc.ServerID, body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	ctx.JSON(200, resp)
}

func handleUnigameResultSync(ctx *gin.Context) {
	body := &pb.UnigameResultSyncReq{}
	err := ctx.BindJSON(body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	uid, assetID, err := getUserInfoByUnigameID(body.UnigameID)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	body.UserID = uid
	body.AssetID = assetID
	resp, err := message.Request[pb.UnigameResultSyncResp](bc.ServerID, body)
	if err != nil {
		log.Error(err)
		ctx.JSON(200, pb.Error{Code: pb.SERVER_ERROR, Msg: err.Error()})
		return
	}
	ctx.JSON(200, resp)
}
*/
