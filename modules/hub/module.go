package hub

import (
	"fmt"
	"s2/define"
	"time"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/utils/hs"
)

type module struct {
	iface.IModule
	*hs.HttpService
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	m.HttpService = hs.NewHttpService()
	return m
}

func (m module) Name() string {
	return define.ModuleName.Hub
}

func (m *module) Init() error {
	m.InitUnigame()
	return nil
}

func (m *module) Run() error {
	return m.ListenAndServe(fmt.Sprintf(":%d", conf.Num[int]("hub.port")))
}

func (m *module) Exit() error {
	return m.Stop(time.Second * 10)
}
