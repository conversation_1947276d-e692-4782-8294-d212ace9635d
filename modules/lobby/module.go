package lobby

import (
	"s2/define"
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/impl"
	"s2/modules/lobby/userops"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/iface"
)

type module struct {
	iface.IModule
	domain *domain.Domain
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewEventLoop(basic.DefaultMQLen),
	}
	m.domain = domain.New(m)
	userops.Init(m)
	impl.Init(m.domain)
	return m
}

func (m module) Name() string {
	return define.ModuleName.Lobby
}

func (m *module) Exit() error {
	m.IModule.Exit()
	userops.SaveALL()
	impl.Exit(m.domain)
	return nil
}
