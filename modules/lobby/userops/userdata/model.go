package userdata

import (
	"github.com/jfcwrlight/core/infra/mgdb/orm"
	"github.com/jfcwrlight/core/utils"
	"github.com/jfcwrlight/core/utils/idgen"
)

type M struct {
	ID  int64 `bson:"_id"`
	Tag struct {
		SN           uint64 // play次数
		EventID      uint64
		LoginTime    int64
		LoginIP      string
		LoginDevice  string
		SeatIDs      map[int32]int32
		GateID       uint32
		orm.DirtyTag `bson:"-"`
	}
	Basic struct { //改动频率低
		Name         string
		Channel      string
		RegTime      int64
		Address      string
		Language     string
		ParentID     int64
		orm.DirtyTag `bson:"-"`
	}
	Assets map[int32]*struct {
		Balance      float64
		LeftInput    float64
		orm.DirtyTag `bson:"-"`
	}
	Third struct {
		UniGameIn struct {
			GameIn     int32
			GatewayURL string
			PlatKey    string
			UnigameID  int32
		}
		TCGameIn struct {
			GameCode    string
			ProductType string
		}
		orm.DirtyTag `bson:"-"`
	}
	Statistics map[int32]*struct {
		SN            uint64  // play次数
		Input         float64 // 输入
		Output        float64 // 输出
		Recharge      float64 // 充值
		Withdraw      float64 // 提款
		RechargeCount int32   // 充值次数
		WithdrawCount int32   // 提款次数
		orm.DirtyTag  `bson:"-"`
	}
	orm.DirtyTag `bson:"-"`
}

func New() *M {
	m := utils.DeepNew[*M]()
	return m
}

func (m M) MongoID() any {
	return m.ID
}

func (m *M) ResetEventID() {
	m.Tag.EventID = idgen.NewUUID()
}

func (m *M) EventID() uint64 {
	return m.Tag.EventID
}

func (m *M) GetStatistics(assetID int32) *struct {
	SN            uint64  // play次数
	Input         float64 // 输入
	Output        float64 // 输出
	Recharge      float64 // 充值
	Withdraw      float64 // 提款
	RechargeCount int32   // 充值次数
	WithdrawCount int32   // 提款次数
	orm.DirtyTag  `bson:"-"`
} {
	if m.Statistics[assetID] == nil {
		m.Statistics[assetID] = &struct {
			SN            uint64  // play次数
			Input         float64 // 输入
			Output        float64 // 输出
			Recharge      float64 // 充值
			Withdraw      float64 // 提款
			RechargeCount int32   // 充值次数
			WithdrawCount int32   // 提款次数
			orm.DirtyTag  `bson:"-"`
		}{}
	}
	return m.Statistics[assetID]
}
