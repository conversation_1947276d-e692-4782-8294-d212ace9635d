package userops

import (
	"context"
	"s2/modules/lobby/userops/userdata"
	"time"

	"github.com/jfcwrlight/core/infra/mgdb"
	"github.com/jfcwrlight/core/infra/mgdb/orm"
	"github.com/jfcwrlight/core/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getOrLoadFromDB(uid int64) (*userdata.M, error) {
	if uid <= 0 {
		user := userdata.New()
		user.ID = uid
		return user, nil // 未登陆的请求使用
	}
	if user, err := manager.cache.Get(uid); err == nil {
		return user.(*userdata.M), nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	result := mgdb.Default().Collection("userdata").FindOne(ctx, bson.M{"_id": uid})
	user := userdata.New()
	err := result.Decode(user)
	if err != nil {
		return nil, err
	}
	manager.cache.Set(uid, user)
	return user, nil
}

func saveDB(user *userdata.M) {
	if user == nil || user.ID <= 0 {
		return
	}
	updates := orm.ParseUpsert(user)
	if len(updates) == 0 {
		return
	}
	_, err := mgdb.Default().Collection("userdata").BulkWrite(context.Background(), updates)
	if err != nil {
		log.Errorf("userops saveDB error: %v", err)
	}
}

func SaveALL() {
	users := manager.cache.GetALL(false)
	writes := make([]mongo.WriteModel, 0, len(users))
	var retries int
	for _, u := range users {
		user, ok := u.(*userdata.M)
		if !ok {
			continue
		}
		m := mongo.NewUpdateOneModel()
		m.SetFilter(bson.M{"_id": user.ID})
		m.SetUpdate(bson.M{
			"$set": user,
		})
		writes = append(writes, m)
		retries = 0
		for len(writes) >= 100 {
			_, err := mgdb.Default().Collection("userdata").BulkWrite(context.Background(), writes)
			if err != nil {
				log.Errorf("userops save all error: %v", err)
				time.Sleep(time.Second)
				retries++
				if retries < 10 {
					continue
				}
			}
			writes = writes[:0]
		}
	}
	retries = 0
	for len(writes) > 0 {
		_, err := mgdb.Default().Collection("userdata").BulkWrite(context.Background(), writes)
		if err != nil {
			log.Errorf("userops save all error: %v", err)
			time.Sleep(time.Second)
			retries++
			if retries < 10 {
				continue
			}
		}
		writes = writes[:0]
	}
	log.Info("userops save all success")
}
