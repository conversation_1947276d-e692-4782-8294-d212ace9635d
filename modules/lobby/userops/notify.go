package userops

import (
	"s2/define"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"

	"github.com/gogo/protobuf/proto"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/message/codec"
	"github.com/jfcwrlight/core/utils"
)

func Notify(user *userdata.M, msg proto.Message) {
	if user.Tag.GateID == 0 {
		return // 不在线
	}
	log.Infof("Notify User %d, %s", user.ID, utils.FormatMsg(msg))
	b := codec.Encode(msg)
	message.Cast(user.Tag.GateID, &pb.S2CPackageMsg{
		UserID: user.ID,
		Body:   b,
	})
}

// 广播
func Broadcast(msg proto.Message) {
	b := codec.Encode(msg)
	log.Infof("Broadcast %s", utils.FormatMsg(msg))
	message.Broadcast(define.ModuleName.Door, &pb.S2CBroadcastMsg{
		Body: b,
	})
}

// 多播
func Multicast(msg proto.Message, userIDs ...int64) {
	if len(userIDs) == 0 {
		return
	}
	b := codec.Encode(msg)
	log.Infof("Multicast %s", utils.FormatMsg(msg))
	message.Broadcast(define.ModuleName.Door, &pb.S2CMulticastMsg{
		UserIDs: userIDs,
		Body:    b,
	})
}
