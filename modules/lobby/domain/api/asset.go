package api

import (
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
)

type IAsset interface {
	Enough(user *userdata.M, assetID int32, num float64) pb.ErrCode
	Add(user *userdata.M, assetID int32, num float64, leftInput float64, cause string) (float64, pb.ErrCode)
	Sub(user *userdata.M, assetID int32, num float64, cause string) (float64, pb.ErrCode)
	Adds(user *userdata.M, cause string, assets ...*pb.IDValFloat) (map[int32]float64, pb.ErrCode)
	Subs(user *userdata.M, cause string, assets ...*pb.IDValFloat) (map[int32]float64, pb.ErrCode)
	Changes(user *userdata.M, cause string, assets ...*pb.IDValFloat) (map[int32]float64, pb.ErrCode)
	Balance(user *userdata.M, assetID int32) float64
	BalanceALL(user *userdata.M) map[int32]float64
}
