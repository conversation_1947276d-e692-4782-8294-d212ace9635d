package api

import (
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
)

type Istatistics interface {
	Statistics(user *userdata.M, data *pb.StatisticsReq) pb.ErrCode
	GetHotGameList(limit int) []*pb.StatisticsGameData
	GetHotGameSeatList(limit int) []*pb.StatisticsGameSeatData
	GetGameSeatRTP(gameID int32, assetID int32, seatID int32) (*pb.StatisticsGameSeatData, error)
	GetGameTotalRTP(gameID int32, assetID int32) (float64, error)
}
