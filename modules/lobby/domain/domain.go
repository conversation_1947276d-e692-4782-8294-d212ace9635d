package domain

import (
	"s2/modules/lobby/domain/api"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/iface"
)

type Domain struct {
	iface.IModule
	domainops.IRoot
}

func New(m iface.IModule) *Domain {
	d := &Domain{
		IRoot:   domainops.New(m, caseMaxIndex),
		IModule: m,
	}
	return d
}

const (
	caseMinIndex = iota
	AssetIndex
	DemoIndex
	LobbyIndex
	WalletIndex
	JackpotIndex
	SeatIndex
	ThirdIndex
	CmdIndex
	StatisticsIndex
	caseMaxIndex
)

func (d *Domain) AssetCase() api.IAsset {
	return d.GetCase(AssetIndex).(api.IAsset)
}

func (d *Domain) DemoCase() api.IDemo {
	return d.GetCase(DemoIndex).(api.IDemo)
}

func (d *Domain) LobbyCase() api.ILobby {
	return d.GetCase(LobbyIndex).(api.ILobby)
}

func (d *Domain) WalletCase() api.IWallet {
	return d.GetCase(WalletIndex).(api.IWallet)
}

func (d *Domain) SeatCase() api.ISeat {
	return d.GetCase(SeatIndex).(api.ISeat)
}

func (d *Domain) ThirdCase() api.IThird {
	return d.GetCase(ThirdIndex).(api.IThird)
}

func (d *Domain) CmdCase() api.ICmd {
	return d.GetCase(CmdIndex).(api.ICmd)
}

func (d *Domain) StatisticsCase() api.Istatistics {
	return d.GetCase(StatisticsIndex).(api.Istatistics)
}
