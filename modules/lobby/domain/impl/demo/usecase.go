package demo

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"time"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/basic/timer"
	"github.com/jfcwrlight/core/log"
)

var uc *useCase

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IDemo](d, domain.DemoIndex, uc) // 注册用例
	userops.Response(uc, uc.onSayHelloReq)                 // 注册响应, 这里是一个客户端请求
	userops.Handle(uc, uc.onDemoTimer)                     // 注册处理, 这里是一个延迟消息
	userops.Handle(uc, uc.onSayHelloMsg)
}

func (uc *useCase) onSayHelloReq(user *userdata.M, body *pb.SayHelloReq, response func(*pb.SayHelloResp, error)) {
	uc.AssetCase().BalanceALL(user) // 调用其他用例方法
	response(&pb.SayHelloResp{Text: "hello"}, nil)
}

func (uc *useCase) onDemoTimer(user *userdata.M, body *pb.DemoTimer) {
	log.Info("timer trigger")
	timer.After(uc, &pb.DemoTimer{UserID: body.UserID, Text: "test"}, time.Second*5) // 延迟5秒收到消息
}

func (uc *useCase) onSayHelloMsg(user *userdata.M, body *pb.SayHelloMsg) {
	log.Info(body.Text)
	userops.Notify(user, &pb.SayHelloAck{Text: "S2C By Notify"})
	userops.Broadcast(&pb.SayHelloAck{Text: "S2C By Broadcast"})
	userops.Multicast(&pb.SayHelloAck{Text: "S2C By Multicast"}, user.ID)
}
