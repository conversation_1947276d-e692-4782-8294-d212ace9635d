package asset

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"

	"github.com/jfcwrlight/core/basic/domainops"
)

var uc *useCase

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IAsset](d, domain.AssetIndex, uc)
	userops.Response(uc, uc.onAddAssetReq)
	userops.Response(uc, uc.onSubAssetReq)
	userops.Response(uc, uc.onChangeAssetReq)
	userops.Response(uc, uc.onAssetBalanceReq)
	userops.Response(uc, uc.onAssetReq)
}
