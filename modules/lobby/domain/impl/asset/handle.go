package asset

import (
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
)

func (uc *useCase) onAddAssetReq(user *userdata.M, body *pb.AddAssetReq, response func(*pb.AddAssetResp, error)) {
	balance, errCode := uc.Adds(user, body.Cause, body.Assets...)
	if errCode != pb.SUCCESS {
		response(&pb.AddAssetResp{Code: errCode}, nil)
		return
	}
	response(&pb.AddAssetResp{Balance: balance}, nil)
}

func (uc *useCase) onSubAssetReq(user *userdata.M, body *pb.SubAssetReq, response func(*pb.SubAssetResp, error)) {
	balance, errCode := uc.Subs(user, body.Cause, body.Assets...)
	if errCode != pb.SUCCESS {
		response(&pb.SubAssetResp{Code: errCode}, nil)
	}
	response(&pb.SubAssetResp{Balance: balance}, nil)
}

func (uc *useCase) onChangeAssetReq(user *userdata.M, body *pb.ChangeAssetReq, response func(*pb.ChangeAssetResp, error)) {
	balance, errCode := uc.Changes(user, body.Cause, body.Assets...)
	if errCode != pb.SUCCESS {
		response(&pb.ChangeAssetResp{Code: errCode}, nil)
	}
	response(&pb.ChangeAssetResp{Balance: balance}, nil)
}

func (uc *useCase) onAssetBalanceReq(user *userdata.M, body *pb.AssetBalanceReq, response func(*pb.AssetBalanceResp, error)) {
	balances := uc.AssetCase().BalanceALL(user)
	resp := &pb.AssetBalanceResp{Balance: balances}
	response(resp, nil)
}

func (uc *useCase) onAssetReq(user *userdata.M, body *pb.AssetReq, response func(*pb.AssetResp, error)) {
	assets := make(map[int32]*pb.IDValFloat)
	for id, asset := range user.Assets {
		assets[id] = &pb.IDValFloat{
			ID:        int64(id),
			Value:     asset.Balance,
			LeftInput: asset.LeftInput,
		}
	}
	resp := &pb.AssetResp{Assets: assets}
	response(resp, nil)
}
