package asset

import (
	"s2/mbi"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"

	"github.com/jfcwrlight/core/bi"
	"github.com/jfcwrlight/core/infra/mgdb/orm"
	"github.com/jfcwrlight/core/log"
)

// 检查资产是否足够
func (uc *useCase) Enough(user *userdata.M, id int32, num float64) pb.ErrCode {
	if num <= 0 {
		return pb.PARAM_ERROR
	}
	asset := user.Assets[id]
	if asset == nil {
		return pb.BALANCE_NOT_ENOUGH
	}
	if asset.Balance < num {
		return pb.BALANCE_NOT_ENOUGH
	}
	return pb.SUCCESS
}

func (uc *useCase) Add(user *userdata.M, id int32, num float64, leftInput float64, cause string) (float64, pb.ErrCode) {
	if num < 0 {
		log.Warnf("add asset num < 0")
		return 0, pb.PARAM_ERROR
	}
	asset := user.Assets[id]
	if asset == nil {
		asset = &struct {
			Balance      float64
			LeftInput    float64
			orm.DirtyTag "bson:\"-\""
		}{}
		user.Assets[id] = asset
	}
	asset.Balance += num
	asset.LeftInput += leftInput
	asset.MarkDirty()
	bi.Log(
		mbi.TableUserAssetChange,
		user.EventID(),
		user.ID,
		id,
		num,
		leftInput,
		asset.Balance,
		asset.LeftInput,
		cause,
		user.Basic.Channel,
		user.Basic.ParentID,
	)
	return asset.Balance, pb.SUCCESS
}

func (uc *useCase) Sub(user *userdata.M, id int32, num float64, cause string) (float64, pb.ErrCode) {
	asset := user.Assets[id]
	if asset == nil {
		return 0, pb.BALANCE_NOT_ENOUGH
	}
	if asset.Balance < num {
		return asset.Balance, pb.BALANCE_NOT_ENOUGH
	}
	asset.Balance -= num
	asset.MarkDirty()
	bi.Log(
		mbi.TableUserAssetChange,
		user.EventID(),
		user.ID,
		id,
		-num,
		0,
		asset.Balance,
		asset.LeftInput,
		cause,
		user.Basic.Channel,
		user.Basic.ParentID,
	)
	return asset.Balance, pb.SUCCESS
}

func (uc *useCase) Adds(user *userdata.M, cause string, assets ...*pb.IDValFloat) (map[int32]float64, pb.ErrCode) {
	if len(assets) == 0 {
		return nil, pb.SUCCESS
	}
	for _, item := range assets {
		if item.Value < 0 {
			log.Warnf("add asset num < 0")
			return nil, pb.BALANCE_NOT_ENOUGH
		}
	}
	balance := make(map[int32]float64, len(assets))
	for _, asset := range assets {
		balance[int32(asset.ID)], _ = uc.Add(user, int32(asset.ID), asset.Value, asset.LeftInput, cause)
	}
	return balance, pb.SUCCESS
}

func (uc *useCase) Subs(user *userdata.M, cause string, assets ...*pb.IDValFloat) (map[int32]float64, pb.ErrCode) {
	if len(assets) == 0 {
		return nil, pb.SUCCESS
	}
	for _, asset := range assets {
		if errCode := uc.Enough(user, int32(asset.ID), asset.Value); errCode != pb.SUCCESS {
			return nil, errCode
		}
	}
	balance := make(map[int32]float64, len(assets))
	for _, asset := range assets {
		balance[int32(asset.ID)], _ = uc.Sub(user, int32(asset.ID), asset.Value, cause)
	}
	return balance, pb.SUCCESS
}

func (uc *useCase) Changes(user *userdata.M, cause string, assets ...*pb.IDValFloat) (map[int32]float64, pb.ErrCode) {
	if len(assets) == 0 {
		return nil, pb.SUCCESS
	}
	for _, asset := range assets {
		if asset.Value < 0 && uc.Enough(user, int32(asset.ID), -asset.Value) != pb.SUCCESS {
			return nil, pb.BALANCE_NOT_ENOUGH
		}
	}
	balance := make(map[int32]float64, len(assets))
	for _, asset := range assets {
		// if asset.Value == 0 {
		// 	continue
		// }
		if asset.Value >= 0 {
			balance[int32(asset.ID)], _ = uc.Add(user, int32(asset.ID), asset.Value, asset.LeftInput, cause)
		} else {
			balance[int32(asset.ID)], _ = uc.Sub(user, int32(asset.ID), -asset.Value, cause)
		}
	}
	return balance, pb.SUCCESS
}

func (uc *useCase) Balance(user *userdata.M, id int32) float64 {
	asset := user.Assets[id]
	if asset == nil {
		return 0
	}
	return asset.Balance
}

func (uc *useCase) BalanceALL(user *userdata.M) map[int32]float64 {
	balance := map[int32]float64{}
	for id, asset := range user.Assets {
		balance[id] = asset.Balance
	}
	return balance
}
