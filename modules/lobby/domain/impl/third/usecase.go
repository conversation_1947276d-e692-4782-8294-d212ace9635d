package third

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/domain/impl/third/tc"
	"s2/modules/lobby/domain/impl/third/unigame"

	"github.com/jfcwrlight/core/basic/domainops"
)

var uc *useCase

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IThird](d, domain.ThirdIndex, uc)
	uc.initFws()
}

func (uc *useCase) initFws() {
	reg(tc.New(uc.Domain))
	reg(unigame.New(uc.Domain))
}
