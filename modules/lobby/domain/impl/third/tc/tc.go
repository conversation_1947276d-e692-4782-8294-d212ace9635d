package tc

import (
	"bytes"
	"crypto/des"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"s2/common"
	"s2/define"
	"s2/gsconf"
	"s2/modules/lobby/domain"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
	"github.com/jfcwrlight/core/utils/hs"
)

var h *handle

type handle struct {
	*domain.Domain
	// Platform => ProductType
	toProductTypeMap map[string]string
}

func New(d *domain.Domain) *handle {
	userops.Response(d, onTCGameListReq)
	userops.Response(d, onTCGameLaunchReq)
	h = &handle{Domain: d}
	h.toProductTypeMap = map[string]string{
		"PG":           "98",
		"PP":           "39",
		"AG":           "4",
		"TCG":          "2",
		"PT":           "3",
		"MGS":          "6",
		"TTG":          "9",
		"PNG":          "12",
		"HB":           "13",
		"CQ9":          "16",
		"AE":           "17",
		"DG":           "27",
		"AB":           "28",
		"HC":           "34",
		"WD":           "37",
		"KY":           "38",
		"BG":           "41",
		"CG":           "42",
		"MG":           "43",
		"RTG":          "44",
		"TH":           "45",
		"BTI":          "47",
		"NE":           "48",
		"VT":           "49",
		"XY":           "52",
		"SBO":          "54",
		"JDB":          "55",
		"SKY":          "59",
		"IMES":         "61",
		"VG":           "62",
		"SW":           "63",
		"LB":           "64",
		"CR":           "65",
		"IMSB":         "68",
		"IA":           "71",
		"VR":           "73",
		"GPI":          "76",
		"BB":           "79",
		"HLG":          "83",
		"BOM":          "90",
		"PIN":          "91",
		"SAS":          "92",
		"SA":           "93",
		"TF":           "99",
		"V8":           "102",
		"CMD":          "104",
		"NE2":          "109",
		"SEX":          "112",
		"DTQ":          "115",
		"MA":           "116",
		"PRE":          "117",
		"WM":           "118",
		"AMB":          "119",
		"KM":           "121",
		"U9W":          "122",
		"KP":           "123",
		"CRG":          "124",
		"YL":           "125",
		"OB":           "129",
		"BOS":          "130",
		"OBSB":         "131",
		"SV3":          "132",
		"365":          "134",
		"DIG":          "135",
		"JK":           "136",
		"RSG":          "138",
		"RCG":          "139",
		"JL":           "140",
		"FC":           "141",
		"FB":           "142",
		"LY":           "143",
		"SG":           "144",
		"AMBS":         "145",
		"MP":           "146",
		"FTG":          "147",
		"RCB":          "148",
		"OBS":          "150",
		"UG2":          "151",
		"WL":           "152",
		"OBC":          "153",
		"CF":           "155",
		"KA":           "157",
		"BP":           "159",
		"BNG":          "161",
		"R88":          "162",
		"POLY":         "167",
		"MW":           "168",
		"BOTA":         "169",
		"TA":           "171",
		"EG4":          "172",
		"PS":           "173",
		"SB":           "174",
		"EZ":           "177",
		"YB":           "178",
		"WE":           "179",
		"DRS":          "182",
		"FP":           "183",
		"BTG":          "184",
		"NLC":          "185",
		"RT":           "186",
		"MNC":          "189",
		"MAS":          "190",
		"EG5":          "191",
		"PGA":          "192",
		"SPB":          "193",
		"PGE":          "194",
		"BT":           "195",
		"L365":         "196",
		"GM":           "200",
		"WF":           "201",
		"WS168":        "202",
		"AUX":          "203",
		"RLX":          "204",
		"HS":           "205",
		"EKOR":         "206",
		"XGS":          "207",
		"LUC":          "209",
		"ES":           "211",
		"SWG":          "212",
		"NG":           "213",
		"VIA":          "214",
		"YGD":          "215",
		"FS":           "216",
		"VP":           "217",
		"WOW":          "218",
		"759":          "219",
		"BOLE":         "221",
		"WMT":          "220",
		"JFF":          "223",
		"3S2":          "224",
		"NS":           "227",
		"L22":          "228",
		"GFG":          "229",
		"VL":           "230",
		"OG2":          "231",
		"OKE":          "232",
		"OCT":          "233",
		"S38":          "234",
		"MNP":          "237",
		"KS":           "238",
		"VA":           "241",
		"EZG":          "242",
		"TP":           "243",
		"7X":           "244",
		"RG":           "247",
		"GB9":          "248",
		"5G":           "249",
		"EGT":          "251",
		"MAHA":         "252",
		"TGC":          "253",
		"BSP":          "254",
		"FBL":          "256",
		"AW":           "257",
		"WJ":           "258",
		"EP":           "259",
		"TPG":          "260",
		"AIL":          "261",
		"ATG":          "262",
		"XG2":          "265",
		"QQ":           "266",
		"MT":           "272",
		"TCG_SEA":      "384",
		"TCG_LOTTO_VN": "420",
		"TCG_LIVE":     "460",
	}
	return h
}

func (handle) Name() string {
	return "tc"
}

func (h *handle) getProductType(gameID int32) (string, pb.ErrCode) {
	conf := table.Get[gsconf.GameInfoConf](gameID)
	productType := h.toProductTypeMap[conf.Platform]
	if productType == "" {
		return "", pb.PARAM_ERROR
	}
	return productType, pb.SUCCESS
}
func (h *handle) Enter(user *userdata.M, data *pb.GamePlayReq) (string, pb.ErrCode) {
	// return "", pb.SUCCESS
	if user.Third.UniGameIn.GameIn != 0 {
		return "", pb.PARAM_ERROR
	}
	if user.Third.TCGameIn.ProductType != "" {
		return "", pb.PARAM_ERROR
	}
	err := createAccount(user)
	if err != nil {
		log.Error(err)
		return "", pb.SERVER_ERROR
	}
	conf := table.Get[gsconf.GameInfoConf](data.GameID)
	productType, errCode := h.getProductType(data.GameID)
	if errCode != pb.SUCCESS {
		return "", errCode
	}
	cause := fmt.Sprintf("tc %s %s launch", productType, conf.ThirdGameCode)
	balance := h.AssetCase().Balance(user, define.AssetCoinID)
	sub := 0.0
	if balance/define.USDExchangeRate >= 1 {
		sub = balance
		_, errCode := h.AssetCase().Sub(user, define.AssetCoinID, sub, cause)
		if errCode != pb.SUCCESS {
			return "", errCode
		}
	}
	err = fundTransferIn(user, productType, sub)
	if err != nil {
		if sub > 0 {
			h.AssetCase().Add(user, define.AssetCoinID, sub, 0, cause+"_error")
		}
		log.Error(fmt.Sprintf("tc TransferIn Id: %d cause: %s error: %v", user.ID, cause, err))
		return "", pb.SERVER_ERROR
	}
	url, errCode := launchGame(user, productType, conf.ThirdGameCode)
	if errCode != pb.SUCCESS {
		return "", errCode
	}
	user.Third.TCGameIn.GameCode = conf.ThirdGameCode
	user.Third.TCGameIn.ProductType = productType
	user.Third.MarkDirty()
	return url, pb.SUCCESS
}

func (h *handle) Leave(user *userdata.M, gameID int32) pb.ErrCode {
	conf := table.Get[gsconf.GameInfoConf](gameID)
	errCode := LeaveByArgs(user, conf.Third[0])
	return errCode
}

var (
	desKey       = []byte(conf.Str("thirdTC.desKey", ""))
	signKey      = []byte(conf.Str("thirdTC.signKey", ""))
	merchantCode = conf.Str("thirdTC.merchantCode", "")
	apiURL       = conf.Str("thirdTC.apiURL", "")
)

func LeaveByArgs(user *userdata.M, productType any) pb.ErrCode {
	amount, errCode := fundTransferOut(user, productType)
	if errCode != pb.SUCCESS {
		return errCode
	}
	_, errCode = h.AssetCase().Add(user, define.AssetCoinID, amount, 0, fmt.Sprintf("tc %s leave", productType))
	return errCode
}

func username(userID int64) string {
	return fmt.Sprintf("dasino%d", userID)
}

// PKCS5填充
func pkcs5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

// DES-ECB加密
func desEncryptECB(origData, key []byte) (string, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return "", err
	}

	bs := block.BlockSize()
	origData = pkcs5Padding(origData, bs)

	encrypted := make([]byte, len(origData))

	// ECB模式就是每bs长度分块独立加密
	for start := 0; start < len(origData); start += bs {
		block.Encrypt(encrypted[start:start+bs], origData[start:start+bs])
	}
	bodyTxt := base64.StdEncoding.EncodeToString(encrypted)

	return bodyTxt, nil
}

func sendEncryptedRequest[T any](body any) (*T, error) {
	// 1. 确保密钥长度为 8 字节
	if len(desKey) != 8 {
		return nil, fmt.Errorf("DES key must be 8 bytes long")
	}

	// 2. 创建 DES 加密块
	postdata, _ := json.Marshal(body)
	// postdata := []byte("{\"username\":\"dasino1\",\"currency\":\"CNY\",\"method\":\"cm\",\"password\":\"abc123456\"}")
	// 3. 填充数据使其长度为 8 的倍数
	padding := 8 - len(postdata)%8
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	postdata = append(postdata, padText...)

	// 5. 执行加密
	ciphertext, _ := desEncryptECB(postdata, desKey)
	signdata := append([]byte(ciphertext), signKey...)
	sign := sha256.Sum256(signdata)
	// bodyTxt = "ZZLvmAkcLjZ5AkFsIU3iEAe%2BbvJMcABgN0X4CHoZQPlw9MS%2BqAof4B%2BmSNXFze%2Bj9vaBf9uYTz5VonWb2jDc9mDJUMcSVSjTYikoNCxyk5k%3D"
	// 6. 生成签名（加密后的字节流与签名密钥拼接后进行 SHA-256 计算）
	signature := hex.EncodeToString(sign[:])
	data := url.Values{}
	data.Set("merchant_code", merchantCode)
	data.Set("params", ciphertext)
	data.Set("sign", signature)

	// 8. 创建 HTTP POST 请求
	dataTxt := data.Encode()
	b, err := hs.HttpRequest("POST", apiURL, []byte(dataTxt), map[string]string{
		"Content-type": "application/x-www-form-urlencoded",
	})
	if err != nil {
		log.Error(err)
		return nil, err
	}
	result := new(T)
	err = json.Unmarshal(b, result)
	if err != nil {
		return nil, err
	}
	// 11. 返回响应内容
	return result, nil
}

func createAccount(user *userdata.M) error {
	// pwd := username(user.ID)
	body := map[string]any{
		"method":   "cm",
		"username": username(user.ID),
		"password": "abc123456",
		"currency": "CNY",
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
	}](body)
	if err != nil {
		return err
	}
	if result.Status != 0 {
		return errors.New(result.ErrMsg)
	}
	return nil
}

func fundTransferIn(user *userdata.M, productType any, amount float64) error {
	body := map[string]any{
		"method":       "ft",
		"username":     username(user.ID),
		"product_type": productType,
		"fund_type":    1,
		"amount":       amount / define.USDExchangeRate,
		"reference_no": common.GenToken()[:40],
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
	}](body)
	if err != nil {
		return err
	}
	if result.Status != 0 {
		return errors.New(result.ErrMsg)
	}
	return nil
}

func fundTransferOut(user *userdata.M, productType any) (float64, pb.ErrCode) {
	body := map[string]any{
		"method":       "ftoa",
		"username":     username(user.ID),
		"product_type": productType,
		"fund_type":    1,
		"reference_no": common.GenToken()[:40],
	}
	result, err := sendEncryptedRequest[struct {
		Status int32   `json:"status"`
		ErrMsg string  `json:"error_desc"`
		Amount float64 `json:"amount"`
	}](body)
	if err != nil {
		log.Error(err)
		return 0, pb.SERVER_ERROR
	}
	if result.Status != 0 {
		log.Error(result.ErrMsg)
		return 0, pb.SERVER_ERROR
	}
	return result.Amount * define.USDExchangeRate, pb.SUCCESS
}

func launchGame(user *userdata.M, productType any, gameCode any) (string, pb.ErrCode) {
	body := map[string]any{
		"method":       "lg",
		"username":     username(user.ID),
		"nickname":     user.Basic.Name,
		"vip_level":    0,
		"product_type": productType,
		"platform":     "html5",
		"game_mode":    1, //utils.IfElse(conf.IsDev(), 0, 1),
		"game_code":    gameCode,
		"language":     utils.IfElse(user.Basic.Language == "", "EN", user.Basic.Language),
		"back_url":     "https://statics.panlaxy.io/dasinodev/",
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
		URL    string `json:"game_url"`
	}](body)
	if err != nil {
		log.Error(err)
		return result.URL, pb.SERVER_ERROR
	}
	if result.Status != 0 {
		log.Error(result.ErrMsg)
		return result.URL, pb.SERVER_ERROR
	}
	return result.URL, pb.SUCCESS
}
