package tc

import (
	"fmt"
	"s2/define"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"

	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
)

func onTCGameListReq(user *userdata.M, body *pb.TCGameListReq, response func(*pb.TCGameListResp, error)) {
	req := map[string]any{
		"method":       "tgl",
		"product_type": body.ProductType,
		"platform":     body.Platform,
		"client_type":  body.ClientType, //utils.IfElse(conf.IsDev(), 0, 1),
		"game_type":    body.GameType,
		"language":     utils.IfElse(body.Language == "", "EN", body.Language),
		"page":         body.Page,
		"page_size":    body.PageSize,
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
		Games  []struct {
			DisplayStatus int    `json:"displayStatus"`
			GameType      string `json:"gameType"`
			GameName      string `json:"gameName"`
			TCGGameCode   string `json:"tcgGameCode"`
			ProductCode   string `json:"productCode"`
			ProductType   string `json:"productType"`
			Platform      string `json:"platform"`
			GameSubType   string `json:"gameSubType"`
			TrialSupport  bool   `json:"trialSupport"`
		} `json:"games"`
		PageInfo struct {
			TotalCount  int32 `json:"totalCount"`
			TotalPage   int32 `json:"totalPage"`
			CurrentPage int32 `json:"currentPage"`
		} `json:"page_info"`
	}](req)
	if err != nil {
		log.Error(err)
		response(&pb.TCGameListResp{
			Code: pb.SERVER_ERROR,
		}, nil)
		return
	}
	if result.Status != 0 {
		log.Error(result.ErrMsg)
		response(&pb.TCGameListResp{
			Code: pb.SERVER_ERROR,
		}, nil)
		return
	}
	games := make([]*pb.TCGameInfo, 0, len(result.Games))
	for _, item := range result.Games {
		if item.DisplayStatus != 0 {
			continue
		}
		games = append(games, &pb.TCGameInfo{
			GameCode:    item.TCGGameCode,
			ProductType: item.ProductType,
			GameType:    item.GameType,
			GameName:    item.GameName,
			Platform:    item.Platform,
			GameSubType: item.GameSubType,
			ProductCode: item.ProductCode,
		})
	}
	response(&pb.TCGameListResp{
		Code:        pb.SUCCESS,
		List:        games,
		TotalPage:   result.PageInfo.TotalPage,
		TotalCount:  result.PageInfo.TotalCount,
		CurrentPage: result.PageInfo.CurrentPage,
	}, nil)
}

func onTCGameLaunchReq(user *userdata.M, body *pb.TCGameLaunchReq, response func(*pb.TCGameLaunchResp, error)) {
	if user.Third.UniGameIn.GameIn != 0 {
		response(&pb.TCGameLaunchResp{
			Code: pb.PARAM_ERROR,
		}, nil)
		return
	}
	if user.Third.TCGameIn.ProductType != "" {
		response(&pb.TCGameLaunchResp{
			Code: pb.PARAM_ERROR,
		}, nil)
		return
	}
	err := createAccount(user)
	if err != nil {
		log.Error(err)
		response(&pb.TCGameLaunchResp{
			Code: pb.SERVER_ERROR,
		}, nil)
		return
	}
	cause := fmt.Sprintf("tc %s %s launch", body.ProductType, body.GameCode)
	balance := h.AssetCase().Balance(user, define.AssetCoinID)
	sub := 0.0
	if balance/define.USDExchangeRate >= 1 {
		sub = balance
		_, errCode := h.AssetCase().Sub(user, define.AssetCoinID, sub, cause)
		if errCode != pb.SUCCESS {
			response(&pb.TCGameLaunchResp{
				Code: errCode,
			}, nil)
			return
		}
	}
	err = fundTransferIn(user, body.ProductType, sub)
	if err != nil {
		if sub > 0 {
			h.AssetCase().Add(user, define.AssetCoinID, sub, 0, cause+"_error")
		}
		log.Error(fmt.Sprintf("tc TransferIn Id: %d cause: %s error: %v", user.ID, cause, err))
		response(&pb.TCGameLaunchResp{
			Code: pb.SERVER_ERROR,
		}, nil)
		return
	}
	url, errCode := launchGame(user, body.ProductType, body.GameCode)
	if errCode != pb.SUCCESS {
		response(&pb.TCGameLaunchResp{
			Code: errCode,
		}, nil)
		return
	}
	user.Third.TCGameIn.GameCode = body.GameCode
	user.Third.TCGameIn.ProductType = body.ProductType
	user.Third.MarkDirty()
	response(&pb.TCGameLaunchResp{
		Code: pb.SUCCESS,
		URL:  url,
	}, nil)
}
