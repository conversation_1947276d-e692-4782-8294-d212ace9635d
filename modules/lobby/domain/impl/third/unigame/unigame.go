package unigame

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"s2/modules/lobby/domain"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"strconv"
	"time"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils/hs"
)

var (
	loginUlr = conf.Str("thirdUnigame.loginUlr", "")
	apiUrl   = conf.Str("thirdUnigame.apiUrl", "")
)

type handle struct {
	*domain.Domain
}

func (handle) Name() string {
	return "x"
}

func New(d *domain.Domain) *handle {
	h := &handle{Domain: d}
	userops.Handle(h, h.onUnigameResultSyncMsg)
	userops.Response(h, h.onUnigameAssetBalanceReq)
	userops.Response(h, h.onUnigameAssetChangeReq)
	userops.Response(h, h.onUnigameResultSyncReq)
	return h
}

func (h *handle) Enter(user *userdata.M, data *pb.GamePlayReq) (string, pb.ErrCode) {
	uniPlatData := &unigamePlatData{}
	if user.Third.UniGameIn.UnigameID != 0 {
		uniPlatData.GatewayURL = user.Third.UniGameIn.GatewayURL
		uniPlatData.PlatKey = user.Third.UniGameIn.PlatKey
		uniPlatData.UserID = user.Third.UniGameIn.UnigameID
	} else {
		uniPlatData_, err := platTokenLogin(user.ID)
		if err != nil {
			return "", pb.SERVER_ERROR
		}
		uniPlatData = uniPlatData_
	}
	amount := h.AssetCase().Balance(user, int32(data.Currency))
	gameUrl, err := unigamePlay(uniPlatData.UserID, user.ID, amount, data.GameID, int32(data.Currency))
	if err != nil {
		return "", pb.SERVER_ERROR
	}
	if gameUrl != "" {
		gameUrl, _ = url.QueryUnescape(gameUrl)
		gameUrl += "&platkey=" + uniPlatData.PlatKey + "&wsurl=" + uniPlatData.GatewayURL
	}
	log.Warn("unigameUrl:", gameUrl)
	user.Third.UniGameIn.GatewayURL = uniPlatData.GatewayURL
	user.Third.UniGameIn.PlatKey = uniPlatData.PlatKey
	user.Third.UniGameIn.GameIn = data.GameID
	user.Third.UniGameIn.UnigameID = uniPlatData.UserID
	user.Third.MarkDirty()
	return gameUrl, pb.SUCCESS
}

func (h *handle) Leave(user *userdata.M, gameID int32) pb.ErrCode {
	return pb.SUCCESS
	// uniPlatData, err := platTokenLogin(user.ID)
	// if err != nil {
	// 	return pb.SERVER_ERROR
	// }
	// result, errcode := unigameFinish(uniPlatData.UserID)
	// if errcode != 0 {
	// 	log.Error("unigameFinish error uid:", user.ID, " errcode:", errcode)
	// 	if errcode == 2 { //在游戏中重新下分一次
	// 		time.Sleep(time.Second)
	// 		result, errcode = unigameFinish(uniPlatData.UserID)
	// 		if errcode != 0 {
	// 			return pb.SERVER_ERROR
	// 		}
	// 	}
	// }
	// return pb.SERVER_ERROR
}

func username(userID int64) string {
	return fmt.Sprintf("dasino_%d", userID)
}

func unigamePlay(uid int32, dataxUid int64, number float64, gameId int32, currency int32) (string, error) {
	data, _ := json.Marshal(map[string]any{
		"uid":      uid,
		"dataxUid": strconv.Itoa(int(dataxUid)),
		"number":   number,
		"gameId":   gameId,
		"currency": currency,
	})
	b, err := hs.HttpRequest("POST", apiUrl+"/play", data, map[string]string{
		"Content-Type": "application/json",
	})
	if err != nil {
		log.Errorf("unigame error %v", err)
		return "", err
	}
	resp := &struct {
		ResultCode int    `json:"resultCode"`
		Msg        string `json:"msg"`
		Data       any    `json:"data"`
		Time       int    `json:"time"`
	}{
		ResultCode: -1,
	}
	err = json.Unmarshal(b, resp)
	if err != nil {
		log.Errorf("unigame resp unmarshal error %v, %s", err, string(b))
		return "", err
	}
	if resp.ResultCode != 0 {
		log.Errorf("unigame error %v %v", resp.ResultCode, resp.Msg)
		return "", errors.New(resp.Msg)
	}
	respData := resp.Data.(map[string]interface{})
	return respData["url"].(string), nil
}

type unigamePlatData struct {
	UserID     int32  `json:"uid"`
	GatewayURL string `json:"gatewayUrl"`
	PlatKey    string `json:"platKey"`
}

// platid 2 telegram
func platTokenLogin(uid int64) (*unigamePlatData, error) {
	req := map[string]any{}
	req["do"] = "plat-token-login"
	req["data"] = map[string]any{
		"platinfo": map[string]any{
			"language":   "cn",
			"cacheToken": "false",
			"uid":        username(uid),
			"platid":     nil,
			"osname":     "_",
		},
		"gameid": 8001,
		"zoneid": 1001,
	}
	data, _ := json.Marshal(req)
	b, err := hs.HttpRequest("POST", loginUlr, data, map[string]string{
		"Content-Type": "application/json",
	})
	if err != nil {
		log.Error("Plat_token_login error %v", err)
		return nil, err
	}
	resp := map[string]interface{}{}
	err = json.Unmarshal(b, &resp)
	if err != nil {
		log.Error("Plat_token_login resp unmarshal error %v, %s", err, string(b))
		return nil, err
	}
	respData := resp["data"].(map[string]interface{})
	u := respData["uid"].(string)
	accountid, _ := strconv.Atoi(u)
	platToken := respData["unigame_plat_login"].(string)
	platKey := respData["unigame_plat_key"].(string)
	return requestSelectZone(int32(accountid), platToken, platKey)
}

// md5Hash 接收一个字符串，并返回其MD5哈希的32位小写十六进制表示
func Md5Hash(text string) string {
	hash := md5.New()                // 创建一个新的MD5哈希器实例
	hash.Write([]byte(text))         // 添加要哈希的文本
	bytes := hash.Sum(nil)           // 计算最终的哈希值
	return hex.EncodeToString(bytes) // 将哈希值的字节转换为十六进制字符串
}

func requestSelectZone(uid int32, platToken, platKey string) (*unigamePlatData, error) {
	req := map[string]any{}
	req["do"] = "request-select-zone"
	req["data"] = map[string]any{}
	req["uid"] = strconv.Itoa(int(uid))
	req["gameid"] = 8001
	req["zoneid"] = 1001
	req["unigame_plat_login"] = platToken
	now := time.Now().UTC().Unix()
	req["unigame_plat_timestamp"] = now
	nows := strconv.Itoa(int(now))
	str, _ := json.Marshal(req)
	unigame_plat_sign := Md5Hash(string(str) + nows + platKey)
	url := loginUlr + "?unigame_plat_sign=" + unigame_plat_sign + "&do=request-select-zone"
	data, _ := json.Marshal(req)
	b, err := hs.HttpRequest("POST", url, data, map[string]string{
		"Content-Type": "application/json",
	})
	if err != nil {
		log.Errorf("request_select_zone resp unmarshal error %v, %s", err, string(b))
		return nil, err
	}
	resp := map[string]interface{}{}
	err = json.Unmarshal(b, &resp)
	if err != nil {
		log.Errorf("request_select_zone resp unmarshal error %v, %s", err, string(b))
		return nil, err
	}
	respDataData := resp["data"].(map[string]interface{})
	Gatewayurlws := respDataData["gatewayurlws"].(string)
	Gatewayurlws += "/json"
	Gatewayurl := respDataData["gatewayurl"].(string)
	log.Info("Gatewayurl:", Gatewayurl)
	log.Info("Gatewayurlws:", Gatewayurlws)
	platData := &unigamePlatData{}
	platData.GatewayURL = Gatewayurlws
	platData.UserID = uid
	platData.PlatKey = platKey
	return platData, nil
}

// type finishData struct {
// 	Chips      float64 `json:"chips"`
// 	Power      float64 `json:"power"`
// 	SpinCount  int32   `json:"spinCount"`
// 	TotalInput float64 `json:"totalInput"`
// 	GameId     int32   `json:"gameId"`
// 	Currency   int32   `json:"currency"`
// }
//
// func unigameFinish(uid int32) (*finishData, int) {
// 	data, _ := json.Marshal(map[string]any{
// 		"uid": uid,
// 	})
// 	b, err := hs.HttpRequest("POST", apiUrl+"/finish", data, map[string]string{
// 		"Content-Type": "application/json",
// 	})
// 	var code int = -1
// 	if err != nil {
// 		log.Errorf("UniGameFinish error %v", err)
// 		return nil, code
// 	}
// 	type Http_S struct {
// 		ResultCode int        `json:"resultCode"`
// 		Msg        string     `json:"msg"`
// 		Data       finishData `json:"data"`
// 	}
// 	resp := &Http_S{ResultCode: code}
// 	err = json.Unmarshal(b, resp)
// 	if err != nil {
// 		log.Errorf("UniGameFinish resp unmarshal error %v, %s", err, string(b))
// 		return nil, code
// 	}
// 	if resp.ResultCode != 0 {
// 		log.Errorf("UniGameFinish error %v %v", resp.ResultCode, resp.Msg)
// 		return nil, resp.ResultCode
// 	}
// 	return &resp.Data, resp.ResultCode
// }
