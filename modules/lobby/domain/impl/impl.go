package impl

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/impl/asset"
	"s2/modules/lobby/domain/impl/cmd"
	"s2/modules/lobby/domain/impl/demo"
	"s2/modules/lobby/domain/impl/lobby"
	"s2/modules/lobby/domain/impl/seat"
	"s2/modules/lobby/domain/impl/statistics"
	"s2/modules/lobby/domain/impl/third"
	"s2/modules/lobby/domain/impl/wallet"
)

func Init(d *domain.Domain) {
	asset.Init(d)
	demo.Init(d)
	lobby.Init(d)
	wallet.Init(d)
	seat.Init(d)
	third.Init(d)
	cmd.Init(d)
	statistics.Init(d)
}

func Exit(d *domain.Domain) {
	statistics.Exit(d)
}
