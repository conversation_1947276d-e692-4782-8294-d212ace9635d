package seat

import (
	"fmt"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"slices"

	"github.com/jfcwrlight/core/log"
)

// func (uc *useCase) SeatPopularRankKey(t time.Time) string {
// 	return fmt.Sprintf("seat.popular:%d", common.Days(t))
// }

// 座位占座字段
func (uc *useCase) SeatTakeField(gameID int32, seatID int32) string {
	return fmt.Sprintf("%d:%d", gameID, seatID)
}

func (uc *useCase) LeaveSeat(user *userdata.M) {
	for gameID, seatID := range user.Tag.SeatIDs {
		delete(user.Tag.SeatIDs, gameID)
		if tryLeaveSeat(gameID, seatID, user.ID) {
			IDs := getSubscriber(gameID)
			userops.Multicast(&pb.SeatChangeNtf{
				SeatID: seatID,
				Using:  false,
			}, IDs...)

			rank := uc.StatisticsCase().GetHotGameSeatList(100)
			if rank != nil && len(rank) > 0 {
				if slices.ContainsFunc(rank, func(item *pb.StatisticsGameSeatData) bool {
					return item.GameID == gameID && item.SeatID == seatID
				}) {
					seatData, err := uc.StatisticsCase().GetGameSeatRTP(gameID, int32(pb.ASSET_GOLD), seatID)
					if err != nil {
						log.Error(err)
						return
					}
					change := &pb.PopularSeat{
						GameID: gameID,
						SeatID: seatID,
						RTP:    int32(seatData.RTP*10000 + 0.5),
						Using:  false,
					}
					// 广播热门座位变化
					userops.Broadcast(&pb.PopularSeatChangeNtf{
						Seats: []*pb.PopularSeat{change},
					})
				}
			}
		}
	}
}

// 游戏的RTP和座位的RTP
func (uc *useCase) GetSeatRTP(gameID int32) (rtp [MaxSeatID]int32, totalRTP int32, err error) {
	for i := range MaxSeatID {
		seatData, err := uc.StatisticsCase().GetGameSeatRTP(gameID, int32(pb.ASSET_GOLD), int32(i))
		if err != nil || seatData == nil {
			rtp[i] = 0
		} else {
			rtp[i] = int32(seatData.RTP*10000 + 0.5) // 四舍五入保留两位小数点百分比
		}
	}
	totalRTPFloat, err := uc.StatisticsCase().GetGameTotalRTP(gameID, int32(pb.ASSET_GOLD))
	if err != nil {
		totalRTP = 0
	} else {
		totalRTP = int32(totalRTPFloat*10000 + 0.5)
	}
	return rtp, totalRTP, nil
}

// 昨日热门座位排行榜
// func yesterdaySeatPopularRankKey() string {
// 	return uc.SeatPopularRankKey(common.UTC0().AddDate(0, 0, -1))
// }
