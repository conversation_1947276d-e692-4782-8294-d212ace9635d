package seat

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"

	"github.com/jfcwrlight/core/basic/domainops"
)

var uc *useCase

type useCase struct {
	*domain.Domain
	// cache atomic.Pointer[struct {
	// 	rtp      map[int32][MaxSeatID]int32
	// 	totalRTP map[int32]int32
	// }]
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.ISeat](d, domain.SeatIndex, uc)
	userops.Handle(uc, uc.onSubscribeSeatChangeMsg)
	userops.Handle(uc, uc.onSubscribeSeatChangeRenewMsg)
	userops.Handle(uc, uc.onSeatHeartbeatMsgMsg)
	userops.Handle(uc, uc.onTakeSeatMsg)
	userops.Handle(uc, uc.onLeaveSeatMsg)
	userops.Response(uc, uc.onPopularSeatReq)
	//事件订阅
	userops.SubscribeEvent(uc, uc.onEventUserEnterLabby)
	userops.SubscribeEvent(uc, uc.onEventUserOffline)
	// 更新RTP 并广播给订阅者
	// uc.cache.Store(&struct {
	// 	rtp      map[int32][MaxSeatID]int32
	// 	totalRTP map[int32]int32
	// }{
	// 	rtp:      make(map[int32][MaxSeatID]int32),
	// 	totalRTP: make(map[int32]int32),
	// })
	// go uc.updateRTP()
}

/*
func (uc *useCase) updateRTP() {
	uc.doUpdateRTP()
	ticker := time.NewTicker(time.Second)
	for {
		select {
		case <-system.RootCtx().Done():
			return
		case <-ticker.C:
			// start := time.Now()
			uc.doUpdateRTP()
			// log.Infof("update RTP cost %s", time.Since(start))
		}
	}
}


func (uc *useCase) doUpdateRTP() {
	defer utils.RecoverPanic()
	confs := table.GetALL[gsconf.GameInfoConf]()
	cmd := rdb.Default().ZRevRange(context.Background(), yesterdaySeatPopularRankKey(), 0, 99)
	populars := cmd.Val()
	popularMsg := &pb.PopularSeatChangeNtf{}
	newCache := &struct {
		rtp      map[int32][MaxSeatID]int32
		totalRTP map[int32]int32
	}{
		rtp:      make(map[int32][MaxSeatID]int32),
		totalRTP: make(map[int32]int32),
	}
	for _, conf := range confs {
		subs := getSubscriber(conf.ID)
		// if len(subs) == 0 { // 没人订阅就不更新了
		// 	continue
		// }
		rtp, totalRTP, err := uc.GetSeatRTP(conf.ID)
		newCache.rtp[conf.ID] = rtp
		newCache.totalRTP[conf.ID] = totalRTP
		msg := &pb.UpdateRTPNtf{
			Total: totalRTP,
		}
		if err != nil {
			log.Error(err)
			return
		}
		push := totalRTP != uc.cache.Load().totalRTP[conf.ID]
		lastRTP := uc.cache.Load().rtp[conf.ID]
		for i := range MaxSeatID {
			if lastRTP[i] == rtp[i] {
				continue
			}
			msg.Changes = append(msg.Changes, &pb.SeatRTP{
				SeatID: int32(i),
				RTP:    rtp[i],
			})
			push = true
			if slices.Contains(populars, fmt.Sprintf("%d:%d", conf.ID, i)) {
				using, err := rdb.Default().HExists(context.Background(), seatTakeKey, uc.SeatTakeField(conf.ID, int32(i))).Result()
				if err != nil {
					log.Error(err)
					continue
				}
				popularMsg.Seats = append(popularMsg.Seats, &pb.PopularSeat{
					GameID: conf.ID,
					SeatID: int32(i),
					RTP:    rtp[i],
					Using:  using,
				})
			}
		}
		if push {
			userops.Multicast(msg, subs...)
		}
	}
	if len(popularMsg.Seats) > 0 {
		userops.Broadcast(popularMsg)
	}
	uc.cache.Store(newCache)
}
*/
