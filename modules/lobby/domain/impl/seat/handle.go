package seat

import (
	"s2/gsconf"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"slices"
	"strconv"
	"strings"

	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
)

func (uc *useCase) onSubscribeSeatChangeMsg(user *userdata.M, body *pb.SubscribeSeatChangeMsg) {
	gameinfo := table.Get[gsconf.GameInfoConf](body.GameID)
	if gameinfo == nil {
		userops.Notify(user, &pb.SubscribeSeatChangeAck{
			Code: pb.PARAM_ERROR,
		})
		return
	}
	err := addSubscribe(body.GameID, user.ID)
	if err != nil {
		userops.Notify(user, &pb.SubscribeSeatChangeAck{
			Code: pb.SERVER_ERROR,
		})
		return
	}
	usings, err := getSeatUsing()
	if err != nil {
		userops.Notify(user, &pb.SubscribeSeatChangeAck{
			Code: pb.SERVER_ERROR,
		})
		return
	}
	var using []int32
	for item := range usings {
		parts := strings.Split(item, ":")
		if len(parts) != 2 {
			continue
		}
		gameID, err := strconv.Atoi(parts[0])
		if err != nil {
			continue
		}
		seatID, err := strconv.Atoi(parts[1])
		if err != nil {
			continue
		}
		if gameID != int(body.GameID) {
			continue
		}
		using = append(using, int32(seatID))
	}
	rtp, totalRTP, err := uc.GetSeatRTP(body.GameID)
	if err != nil {
		userops.Notify(user, &pb.SubscribeSeatChangeAck{
			Code: pb.SERVER_ERROR,
		})
		return
	}

	userops.Notify(user, &pb.SubscribeSeatChangeAck{
		Code:     pb.SUCCESS,
		Using:    using,
		RTP:      rtp[:],
		TotalRTP: totalRTP,
	})
}

func (uc *useCase) onSubscribeSeatChangeRenewMsg(user *userdata.M, body *pb.SubscribeSeatChangeRenewMsg) {
	err := addSubscribe(body.GameID, user.ID)
	if err != nil {
		userops.Notify(user, &pb.SubscribeSeatChangeRenewAck{
			Code: pb.SERVER_ERROR,
		})
		return
	}
	userops.Notify(user, &pb.SubscribeSeatChangeRenewAck{
		Code: pb.SUCCESS,
	})
}

// 心跳
func (uc *useCase) onSeatHeartbeatMsgMsg(user *userdata.M, body *pb.SeatHeartbeatMsg) {
	if body.SeatID > MaxSeatID {
		userops.Notify(user, &pb.SeatHeartbeatAck{Code: pb.PARAM_ERROR})
		return
	}
	code := tryOccupySeat(body.GameID, body.SeatID, user.ID)
	userops.Notify(user, &pb.SeatHeartbeatAck{Code: code})
}

// 占座
func (uc *useCase) onTakeSeatMsg(user *userdata.M, body *pb.TakeSeatMsg) {
	if len(user.Tag.SeatIDs) > 0 {
		userops.Notify(user, &pb.TakeSeatAck{Code: pb.PARAM_ERROR})
		return
	}
	if body.SeatID > MaxSeatID {
		userops.Notify(user, &pb.TakeSeatAck{Code: pb.PARAM_ERROR})
		return
	}
	code := tryOccupySeat(body.GameID, body.SeatID, user.ID)
	userops.Notify(user, &pb.TakeSeatAck{
		Code:   code,
		GameID: body.GameID,
		SeatID: body.SeatID,
	})
	if code != pb.SUCCESS {
		return
	}
	IDs := getSubscriber(body.GameID)
	remSubscriber(body.GameID, user.ID)
	user.Tag.SeatIDs[body.GameID] = body.SeatID
	// 通知订阅者
	userops.Multicast(&pb.SeatChangeNtf{
		SeatID: body.SeatID,
		Using:  true,
	}, IDs...)
	rank := uc.StatisticsCase().GetHotGameSeatList(100)
	if rank != nil && len(rank) > 0 {
		if slices.ContainsFunc(rank, func(item *pb.StatisticsGameSeatData) bool {
			return item.GameID == body.GameID && item.SeatID == body.SeatID
		}) {
			seatData, err := uc.StatisticsCase().GetGameSeatRTP(body.GameID, int32(pb.ASSET_GOLD), body.SeatID)
			if err != nil {
				log.Error(err)
				return
			}
			change := &pb.PopularSeat{
				GameID: body.GameID,
				SeatID: body.SeatID,
				RTP:    int32(seatData.RTP*10000 + 0.5),
				Using:  true,
			}
			// 广播热门座位变化
			userops.Broadcast(&pb.PopularSeatChangeNtf{
				Seats: []*pb.PopularSeat{change},
			})
		}
	}
}

func (uc *useCase) onLeaveSeatMsg(user *userdata.M, body *pb.LeaveSeatMsg) {
	for gameID, seatID := range user.Tag.SeatIDs {
		delete(user.Tag.SeatIDs, gameID)
		if tryLeaveSeat(gameID, seatID, user.ID) {
			IDs := getSubscriber(gameID)
			userops.Multicast(&pb.SeatChangeNtf{
				SeatID: seatID,
				Using:  false,
			}, IDs...)
		}
	}
	userops.Notify(user, &pb.LeaveSeatAck{Code: pb.SUCCESS})
}

// 热门座位请求
func (uc *useCase) onPopularSeatReq(user *userdata.M, body *pb.PopularSeatReq, response func(*pb.PopularSeatResp, error)) {
	rank := uc.StatisticsCase().GetHotGameSeatList(100)
	if rank == nil || len(rank) == 0 {
		response(&pb.PopularSeatResp{
			Code: pb.SUCCESS,
		}, nil)
		return
	}
	resp := &pb.PopularSeatResp{
		Code:  pb.SUCCESS,
		Seats: make([]*pb.PopularSeat, 0, 100),
	}
	usings, err := getSeatUsing()
	if err != nil {
		resp.Code = pb.SERVER_ERROR
		response(resp, err)
		return
	}
	for _, seat := range rank {
		seatField := uc.SeatTakeField(seat.GameID, seat.SeatID)
		_, using := usings[seatField]
		resp.Seats = append(resp.Seats, &pb.PopularSeat{
			GameID: seat.GameID,
			SeatID: seat.SeatID,
			RTP:    int32(seat.RTP*10000 + 0.5),
			Using:  using,
		})
	}
	response(resp, nil)
}
