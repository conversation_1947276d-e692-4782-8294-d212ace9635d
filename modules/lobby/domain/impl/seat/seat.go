package seat

import (
	"context"
	"errors"
	"fmt"
	"s2/pb"
	"strconv"
	"time"

	"github.com/jfcwrlight/core/infra/rdb"
	"github.com/jfcwrlight/core/log"
	"github.com/redis/go-redis/v9"
)

const (
	MaxSeatID   = 777
	seatTakeKey = "seat.take"
)

// 订阅者key
func seatSubKey(gameID int32) string {
	return fmt.Sprintf("seat.sub:%d", gameID)
}

// 获取占用座位
func getSeatUsing() (map[string]string, error) {
	return rdb.Default().HGetAll(context.Background(), seatTakeKey).Result()
}

// 添加订阅者
func addSubscribe(gameID int32, userID int64) error {
	err := rdb.Default().HSet(context.Background(), seatSubKey(gameID), userID, 1).Err()
	if err != nil {
		log.Error(err)
		return err
	}
	return rdb.Default().HExpire(context.Background(), seatSubKey(gameID), time.Second*20, strconv.Itoa(int(userID))).Err()
}

// 获取订阅者
func getSubscriber(gameID int32) []int64 {
	list, err := rdb.Default().HGetAll(context.Background(), seatSubKey(gameID)).Result()
	if err != nil {
		log.Error(err)
		return nil
	}
	IDs := make([]int64, 0, len(list))
	for key := range list {
		n, err := strconv.Atoi(key)
		if err != nil {
			continue
		}
		IDs = append(IDs, int64(n))
	}
	return IDs
}

// 移除订阅者
func remSubscriber(gameID int32, userID int64) error {
	return rdb.Default().HDel(context.Background(), seatSubKey(gameID), strconv.Itoa(int(userID))).Err()
}

// 尝试占用座位
func tryOccupySeat(gameID int32, seatID int32, userID int64) (code pb.ErrCode) {
	ctx := context.Background()
	seatKey := seatTakeKey
	seatField := uc.SeatTakeField(gameID, seatID)
	state := 0
	rdb.Default().Watch(ctx, func(tx *redis.Tx) error {
		n, err := tx.HGet(ctx, seatKey, seatField).Int64()
		if err == redis.Nil {
			tx.HSet(ctx, seatKey, seatField, userID)
			tx.HExpire(ctx, seatKey, time.Second*40, seatField)
			state = 1
			return nil
		}
		if err == redis.TxFailedErr {
			return err
		}
		if err != nil {
			log.Error(err)
			state = 3
			return err
		}
		if n != userID {
			state = 2
			return errors.New("userID change")
		}
		tx.HExpire(ctx, seatKey, time.Second*40, seatField)
		state = 1
		return nil
	}, seatKey)
	if state == 2 {
		return pb.SEAT_USING
	}
	if state == 1 {
		return pb.SUCCESS
	}
	return pb.SEAT_USING
}

// 尝试离开座位
func tryLeaveSeat(gameID int32, seatID int32, userID int64) bool {
	ctx := context.Background()
	state := 0
	seatField := uc.SeatTakeField(gameID, seatID)
	for i := 0; i < 10 && state == 0; i++ { // 乐观锁
		err := rdb.Default().Watch(ctx, func(tx *redis.Tx) error {
			n, err := tx.HGet(ctx, seatTakeKey, seatField).Int64()
			if err == redis.Nil {
				state = 1
				return err
			}
			if err == redis.TxFailedErr {
				return err
			}
			if err != nil {
				log.Error(err)
				return err
			}
			if n != userID {
				state = 2
				return nil
			}
			tx.HDel(ctx, seatTakeKey, seatField)
			state = 1
			return nil
		}, seatTakeKey)
		if err == nil {
			break
		}
		if err == redis.TxFailedErr {
			continue
		}
		log.Error(err)
		return false
	}
	log.Infof("tryLeaveSeat %d %d %d %d", gameID, seatID, state, userID)
	return state == 1
}
