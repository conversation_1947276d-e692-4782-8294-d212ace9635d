package wallet

import (
	"s2/define"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"time"

	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (uc *useCase) onUserWithdrawRejectedMsg(user *userdata.M, body *pb.UserWithdrawRejectedMsg) {
	resp, err := message.RequestAny[pb.CheckAndCommitOrderResp](define.ModuleName.Order, &pb.CheckAndCommitOrderReq{
		TxHash:    body.ID,
		OrderType: 2,
	})
	if err != nil {
		log.Error(err)
		return
	}
	assetID := int32(resp.Asset.ID)
	amount := resp.Asset.Value
	_, errCode := uc.AssetCase().Add(user, assetID, amount, resp.Asset.LeftInput, "withdrawrejected")
	if errCode != pb.SUCCESS {
		log.Errorf("user %d recharge %d, %f, errCode: %d", body.UserID, resp.Asset.ID, resp.Asset.Value, errCode)
	} else {
		stat := user.GetStatistics(assetID)
		stat.Withdraw -= amount
		stat.WithdrawCount--
		stat.MarkDirty()
		log.Infof("user %d recharge %d, %f, %f", body.UserID, resp.Asset.ID, resp.Asset.Value, resp.Asset.LeftInput)

		message.Stream.Anycast(define.ModuleName.Statistics, &pb.StatisticsUserEventReq{
			EventType:  pb.USER_WITHDRAW,
			UserID:     user.ID,
			Channel:    user.Basic.Channel,
			Amount:     resp.Asset.Value,
			Timestamp:  uint32(time.Now().Unix()),
			CreateTime: uint32(user.Basic.RegTime),
		})
	}
}

func (uc *useCase) onUserRechargeMsg(user *userdata.M, body *pb.UserRechargeMsg) {
	resp, err := message.RequestAny[pb.CheckAndCommitOrderResp](define.ModuleName.Order, &pb.CheckAndCommitOrderReq{
		TxHash:    body.TxHash,
		OrderType: 1,
	})
	if err != nil {
		log.Error(err)
		return
	}
	assetID := int32(resp.Asset.ID)
	_, errCode := uc.AssetCase().Add(user, assetID, resp.Asset.Value, resp.Asset.LeftInput, "recharge")
	if errCode != pb.SUCCESS {
		log.Errorf("user %d recharge %d, %f, errCode: %d", body.UserID, resp.Asset.ID, resp.Asset.Value, errCode)
	} else {
		stat := user.GetStatistics(assetID)
		stat.Recharge += resp.Asset.Value
		stat.RechargeCount++
		stat.MarkDirty()
		log.Infof("user %d recharge %d, %f, %f", body.UserID, resp.Asset.ID, resp.Asset.Value, resp.Asset.LeftInput)
		message.Stream.Anycast(define.ModuleName.Statistics, &pb.StatisticsUserEventReq{
			EventType:   pb.USER_RECHARGE_COMPLETE,
			UserID:      user.ID,
			Channel:     user.Basic.Channel,
			Amount:      resp.Asset.Value,
			IsFirstTime: stat.RechargeCount == 1,
			Timestamp:   uint32(time.Now().Unix()),
			CreateTime:  uint32(user.Basic.RegTime),
		})
	}
}

func (uc *useCase) onGetUserRechargeAddressReq(user *userdata.M, body *pb.GetUserRechargeAddressReq, response func(*pb.GetUserRechargeAddressResp, error)) {
	resp, err := message.RequestAny[pb.GetOrSetUserRechargeAddressResp](define.ModuleName.Order, &pb.GetOrSetUserRechargeAddressReq{
		UserID: user.ID,
		Chain:  body.Chain,
	})
	if err != nil {
		log.Error(err)
		return
	}
	response(&pb.GetUserRechargeAddressResp{Code: resp.Code, Address: resp.Address, Extra: resp.Extra}, nil)
}

func (uc *useCase) onGetCoinAndChainListReq(user *userdata.M, body *pb.GetCoinAndChainListReq, response func(*pb.GetCoinAndChainListResp, error)) {
	resp, err := message.RequestAny[pb.GetCoinMapResp](define.ModuleName.Order, &pb.GetCoinMapReq{})
	if err != nil {
		response(&pb.GetCoinAndChainListResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	response(&pb.GetCoinAndChainListResp{
		Code:         pb.SUCCESS,
		RechargeList: resp.RechargeList,
		WithdrawList: resp.WithdrawList,
	}, nil)
}

func (uc *useCase) onWithdrawToLobbyReq(user *userdata.M, body *pb.WithdrawToLobbyReq, response func(*pb.WithdrawResp, error)) {
	uc.onWithdrawReq(user, &pb.WithdrawReq{
		Value:   body.Value,
		Chain:   body.Chain,
		Coin:    body.Coin,
		Address: body.Address,
		Extra:   body.Extra,
	}, response)
}

func (uc *useCase) onWithdrawReq(user *userdata.M, body *pb.WithdrawReq, response func(*pb.WithdrawResp, error)) {
	resp, err := message.RequestAny[pb.GetCoinMapResp](define.ModuleName.Order, &pb.GetCoinMapReq{})
	if err != nil {
		response(&pb.WithdrawResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	var coinConfig *pb.CoinInfo
	for _, coin := range resp.WithdrawList {
		if coin.Coin == body.Coin && coin.Chain == body.Chain {
			coinConfig = coin
			break
		}
	}
	if coinConfig == nil {
		response(&pb.WithdrawResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	subValue := body.Value + coinConfig.Config.Fees
	errCode := uc.AssetCase().Enough(user, int32(coinConfig.Config.AssetId), subValue)
	if errCode != pb.SUCCESS {
		response(&pb.WithdrawResp{Code: errCode}, nil)
		return
	}
	if user.Assets[int32(coinConfig.Config.AssetId)].LeftInput > 0 {
		response(&pb.WithdrawResp{Code: pb.INPUT_NOT_ENOUGH}, nil)
		return
	}
	respCreate, err := message.RequestAny[pb.CreateWithdrawOrderResp](define.ModuleName.Order, &pb.CreateWithdrawOrderReq{
		UserID:      user.ID,
		Chain:       body.Chain,
		CoinID:      body.Coin,
		AssetAmount: body.Value,
		AssetId:     int64(coinConfig.Config.AssetId),
		AssetFrees:  coinConfig.Config.Fees,
		To:          body.Address,
	})
	if err != nil {
		response(&pb.WithdrawResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	if respCreate.Code != pb.SUCCESS {
		response(&pb.WithdrawResp{Code: respCreate.Code}, nil)
		return
	}
	assetID := int32(coinConfig.Config.AssetId)
	balance, errCode := uc.AssetCase().Sub(user, assetID, subValue, "withdraw")
	if errCode == pb.SUCCESS {
		stat := user.GetStatistics(assetID)
		stat.Withdraw += subValue
		stat.WithdrawCount++
		stat.MarkDirty()
	}
	response(&pb.WithdrawResp{Code: errCode, Balance: balance}, nil)
}
