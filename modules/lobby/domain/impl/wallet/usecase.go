package wallet

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"

	"github.com/jfcwrlight/core/basic/domainops"
)

var uc *useCase

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IWallet](d, domain.WalletIndex, uc)
	userops.Handle(uc, uc.onUserRechargeMsg)
	userops.Handle(uc, uc.onUserWithdrawRejectedMsg)
	userops.Response(uc, uc.onGetUserRechargeAddressReq)
	userops.Response(uc, uc.onGetCoinAndChainListReq)
	userops.Response(uc, uc.onWithdrawReq)
	userops.Response(uc, uc.onWithdrawToLobbyReq)
}
