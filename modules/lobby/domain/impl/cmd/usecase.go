package cmd

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"

	"github.com/jfcwrlight/core/basic/domainops"
)

var uc *useCase

type useCase struct {
	*domain.Domain
	fns map[string]func(user *userdata.M, args []string)
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
		fns:    make(map[string]func(user *userdata.M, args []string)),
	}
	domainops.Register[api.ICmd](d, domain.CmdIndex, uc)
	userops.Response(uc, uc.onCommandReq)
}
