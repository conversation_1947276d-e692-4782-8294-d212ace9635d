package statistics

import (
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
)

func (uc *useCase) onOverAllRTPMsg(user *userdata.M, body *pb.OverAllRTPMsg) {
	stats := uc.statsCache.Load()
	var gameRTP int32 = 0
	var jackpotRTP int32 = 0
	if stats != nil {
		gameRTP = int32(stats.TotalOutput / stats.TotalInput * 10000)
		jackpotRTP = int32(stats.TotalJackpot / stats.TotalInput * 10000)
	}
	var allRTP int32 = gameRTP + jackpotRTP
	userops.Broadcast(&pb.OverAllRTPAck{
		AllRTP:     allRTP,
		GameRTP:    gameRTP,
		JackpotRTP: jackpotRTP,
	})
}

func (uc *useCase) onWinnersMsg(user *userdata.M, body *pb.WinnersMsg) {
	winners := uc.winnercache.Load().winners
	userops.Notify(user, &pb.WinnersAck{
		List: winners,
	})
}

func (uc *useCase) onStatisticsReq(user *userdata.M, body *pb.StatisticsReq) {
	uc.Statistics(user, body)
}
