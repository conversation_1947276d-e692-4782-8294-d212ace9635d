package statistics

import (
	"context"
	"encoding/json"
	"s2/define"
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"
	"s2/pb"
	"sort"
	"sync"
	"sync/atomic"
	"time"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/infra/rdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
	"github.com/redis/go-redis/v9"
)

// StatsCacheData 统计数据缓存结构
type StatsCacheData struct {
	TodayGame     []*pb.StatisticsGameData
	YesterdayGame []*pb.StatisticsGameData
	HistoryGame   []*pb.StatisticsGameData
	TodaySeat     []*pb.StatisticsGameSeatData
	YesterdaySeat []*pb.StatisticsGameSeatData
	HistorySeat   []*pb.StatisticsGameSeatData

	// 实时数据索引（基于历史汇总数据）
	RealtimeGameIndex map[int32]map[int32]*pb.StatisticsGameData               // [AssetID][GameID]
	RealtimeSeatIndex map[int32]map[int32]map[int32]*pb.StatisticsGameSeatData // [AssetID][GameID][SeatID]

	TotalInput   float64
	TotalOutput  float64
	TotalJackpot float64
	LastUpdate   time.Time

	// 昨日数据获取日期，用于判断是否需要重新获取昨日数据
	YesterdayDataDate string // 格式: "2006-01-02"
}

const (
	maxWinners = 10
	winnersKey = "statistics:winners"
)

var uc *useCase

type useCase struct {
	*domain.Domain
	winnercache atomic.Pointer[struct {
		winners []*pb.WinnerInfo
	}]
	statsCache atomic.Pointer[StatsCacheData] //统计数据缓存
	// cache atomic.Pointer[common.StatsCache]
	mu sync.Mutex // 添加互斥锁保护缓存更新
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.Istatistics](d, domain.StatisticsIndex, uc)

	userops.Handle(uc, uc.onOverAllRTPMsg)
	userops.Handle(uc, uc.onWinnersMsg)
	userops.Handle(uc, uc.onStatisticsReq)

	// 初始化缓存
	uc.winnercache.Store(&struct {
		winners []*pb.WinnerInfo
	}{
		winners: []*pb.WinnerInfo{},
	})
	// uc.cache.Store(&common.StatsCache{
	// 	GameStats: make(map[string]*common.GameStatistics),
	// 	SeatStats: make(map[string]map[int32]*common.SeatStatistics),
	// })

	// 从Redis加载获奖记录
	loadWinnersFromRedis()

	go uc.updateStats()
}

func loadWinnersFromRedis() {
	ctx := context.Background()
	data, err := rdb.Default().Get(ctx, winnersKey).Result()
	if err != nil {
		if err != redis.Nil {
			log.Error("load winners from redis error:", err)
		}
		return
	}

	var winners []*pb.WinnerInfo
	if err := json.Unmarshal([]byte(data), &winners); err != nil {
		log.Error("unmarshal winners error:", err)
		return
	}

	uc.winnercache.Store(&struct {
		winners []*pb.WinnerInfo
	}{winners: winners})
}

func Exit(d *domain.Domain) {
	// 保存获奖记录到Redis
	ctx := context.Background()
	winners := uc.winnercache.Load().winners
	if len(winners) > 0 {
		data, err := json.Marshal(winners)
		if err != nil {
			log.Error("marshal winners error:", err)
			return
		}
		if err := rdb.Default().Set(ctx, winnersKey, data, 0).Err(); err != nil {
			log.Error("save winners to redis error:", err)
		}
	}
}

// AddWinner 安全地添加获奖记录，只保留最新的10条，新数据放在第一位
func (uc *useCase) AddWinner(winner *pb.WinnerInfo) {
	uc.mu.Lock()
	defer uc.mu.Unlock()

	oldCache := uc.winnercache.Load()
	newWinners := make([]*pb.WinnerInfo, 0, maxWinners)

	// 添加新记录到第一位
	newWinners = append(newWinners, winner)

	// 添加旧记录，但确保总数不超过maxWinners
	if len(oldCache.winners) > 0 {
		remaining := maxWinners - 1
		if remaining > 0 {
			end := remaining
			if len(oldCache.winners) < remaining {
				end = len(oldCache.winners)
			}
			newWinners = append(newWinners, oldCache.winners[:end]...)
		}
	}

	uc.winnercache.Store(&struct {
		winners []*pb.WinnerInfo
	}{winners: newWinners})
}

func (uc *useCase) updateStats() {
	uc.doUpdateStats()
	ticker := time.NewTicker(time.Second * 1)
	for {
		select {
		case <-system.RootCtx().Done():
			return
		case <-ticker.C:
			uc.doUpdateStats()
		}
	}
}

func (uc *useCase) doUpdateStats() {
	defer utils.RecoverPanic()

	// 获取今日和昨日日期
	now := time.Now()
	today := now.Format("2006-01-02")
	yesterday := now.AddDate(0, 0, -1).Format("2006-01-02")

	// 检查是否需要获取昨日数据
	currentCache := uc.statsCache.Load()
	needYesterdayData := currentCache == nil || currentCache.YesterdayDataDate != today || currentCache.YesterdayGame == nil || currentCache.YesterdaySeat == nil
	// 请求今日游戏数据
	todayGameResp, err := message.RequestAny[pb.GMStatisticsGameDataResp](define.ModuleName.Statistics, &pb.StatisticsGameDataReq{
		StartDate: today,
		EndDate:   today,
		AssetID:   int32(pb.ASSET_GOLD),
		Channel:   "",
	})
	if err != nil {
		log.Error("Failed to get today game data:", err)
	} else {
		log.Debugf("Today game data: %d records", len(todayGameResp.List))
	}

	// 请求昨日游戏数据（仅在需要时）
	var yesterdayGameResp *pb.GMStatisticsGameDataResp
	if needYesterdayData {
		yesterdayGameResp, err = message.RequestAny[pb.GMStatisticsGameDataResp](define.ModuleName.Statistics, &pb.StatisticsGameDataReq{
			StartDate: yesterday,
			EndDate:   yesterday,
			AssetID:   int32(pb.ASSET_GOLD),
			Channel:   "",
		})
		if err != nil {
			log.Error("Failed to get yesterday game data:", err)
		} else {
			log.Debugf("Yesterday game data: %d records", len(yesterdayGameResp.List))
		}
	} else {
		log.Debugf("Yesterday game data already cached for today, skipping request")
	}

	// 请求历史汇总游戏数据
	historyGameResp, err := message.RequestAny[pb.GMStatisticsGameDataResp](define.ModuleName.Statistics, &pb.StatisticsGameDataReq{
		StartDate: "", // 空表示查询历史汇总
		EndDate:   "",
		AssetID:   int32(pb.ASSET_GOLD),
		Channel:   "",
	})
	if err != nil {
		log.Error("Failed to get history game data:", err)
	} else {
		log.Debugf("History game data: %d records", len(historyGameResp.List))
	}

	// 请求今日座位数据
	todaySeatResp, err := message.RequestAny[pb.StatisticsGameSeatDataResp](define.ModuleName.Statistics, &pb.StatisticsGameSeatDataReq{
		StartDate: today,
		EndDate:   today,
		AssetID:   int32(pb.ASSET_GOLD),
	})
	if err != nil {
		log.Error("Failed to get today seat data:", err)
	} else {
		log.Debugf("Today seat data: %d records", len(todaySeatResp.List))
	}

	// 请求昨日座位数据（仅在需要时）
	var yesterdaySeatResp *pb.StatisticsGameSeatDataResp
	if needYesterdayData {
		yesterdaySeatResp, err = message.RequestAny[pb.StatisticsGameSeatDataResp](define.ModuleName.Statistics, &pb.StatisticsGameSeatDataReq{
			StartDate: yesterday,
			EndDate:   yesterday,
			AssetID:   int32(pb.ASSET_GOLD),
		})
		if err != nil {
			log.Error("Failed to get yesterday seat data:", err)
		} else {
			log.Debugf("Yesterday seat data: %d records", len(yesterdaySeatResp.List))
		}
	} else {
		log.Debugf("Yesterday seat data already cached for today, skipping request")
	}

	// 请求历史汇总座位数据
	historySeatResp, err := message.RequestAny[pb.StatisticsGameSeatDataResp](define.ModuleName.Statistics, &pb.StatisticsGameSeatDataReq{
		StartDate: "", // 空表示查询历史汇总
		EndDate:   "",
		AssetID:   int32(pb.ASSET_GOLD),
	})
	if err != nil {
		log.Error("Failed to get history seat data:", err)
	} else {
		log.Debugf("History seat data: %d records", len(historySeatResp.List))
	}

	// 更新缓存
	uc.updateStatsCache(todayGameResp, yesterdayGameResp, historyGameResp, todaySeatResp, yesterdaySeatResp, historySeatResp, today)
}

// updateStatsCache 更新统计数据缓存
func (uc *useCase) updateStatsCache(
	todayGame *pb.GMStatisticsGameDataResp,
	yesterdayGame *pb.GMStatisticsGameDataResp,
	historyGame *pb.GMStatisticsGameDataResp,
	todaySeat *pb.StatisticsGameSeatDataResp,
	yesterdaySeat *pb.StatisticsGameSeatDataResp,
	historySeat *pb.StatisticsGameSeatDataResp,
	today string) {

	// 获取当前缓存数据（用于保留昨日数据）
	currentCache := uc.statsCache.Load()

	// 创建新的缓存数据
	newCacheData := &StatsCacheData{
		RealtimeGameIndex: make(map[int32]map[int32]*pb.StatisticsGameData),
		RealtimeSeatIndex: make(map[int32]map[int32]map[int32]*pb.StatisticsGameSeatData),
		LastUpdate:        time.Now(),
		YesterdayDataDate: today, // 设置昨日数据获取日期为今天
	}

	// 提取今日游戏数据
	if todayGame != nil && todayGame.Code == pb.SUCCESS {
		newCacheData.TodayGame = todayGame.List
	}

	// 提取昨日游戏数据
	if yesterdayGame != nil && yesterdayGame.Code == pb.SUCCESS {
		newCacheData.YesterdayGame = yesterdayGame.List
	} else if currentCache != nil {
		// 如果不需要获取昨日数据，保留当前缓存中的昨日数据
		newCacheData.YesterdayGame = currentCache.YesterdayGame
	}

	// 提取历史汇总游戏数据
	if historyGame != nil && historyGame.Code == pb.SUCCESS {
		newCacheData.HistoryGame = historyGame.List

		totalInput := 0.0
		totalOutput := 0.0
		totalJackpot := 0.0
		// 构建实时游戏数据索引（基于历史汇总）
		for _, gameData := range historyGame.List {
			assetID := gameData.AssetID
			gameID := gameData.GameID

			if newCacheData.RealtimeGameIndex[assetID] == nil {
				newCacheData.RealtimeGameIndex[assetID] = make(map[int32]*pb.StatisticsGameData)
			}
			newCacheData.RealtimeGameIndex[assetID][gameID] = gameData
			totalInput += gameData.TotalInput
			totalOutput += gameData.TotalOutput
			totalJackpot += gameData.TotalJackpot
		}
		newCacheData.TotalInput = totalInput
		newCacheData.TotalOutput = totalOutput
		newCacheData.TotalJackpot = totalJackpot
	}

	// 提取今日座位数据
	if todaySeat != nil && todaySeat.Code == pb.SUCCESS {
		newCacheData.TodaySeat = todaySeat.List
	}

	// 提取昨日座位数据
	if yesterdaySeat != nil && yesterdaySeat.Code == pb.SUCCESS {
		newCacheData.YesterdaySeat = yesterdaySeat.List
	} else if currentCache != nil {
		// 如果不需要获取昨日数据，保留当前缓存中的昨日数据
		newCacheData.YesterdaySeat = currentCache.YesterdaySeat
	}

	// 提取历史汇总座位数据
	if historySeat != nil && historySeat.Code == pb.SUCCESS {
		newCacheData.HistorySeat = historySeat.List

		// 构建实时座位数据索引（基于历史汇总）
		for _, seatData := range historySeat.List {
			assetID := seatData.AssetID
			gameID := seatData.GameID
			seatID := seatData.SeatID

			if newCacheData.RealtimeSeatIndex[assetID] == nil {
				newCacheData.RealtimeSeatIndex[assetID] = make(map[int32]map[int32]*pb.StatisticsGameSeatData)
			}
			if newCacheData.RealtimeSeatIndex[assetID][gameID] == nil {
				newCacheData.RealtimeSeatIndex[assetID][gameID] = make(map[int32]*pb.StatisticsGameSeatData)
			}
			newCacheData.RealtimeSeatIndex[assetID][gameID][seatID] = seatData
		}
	}

	// 原子性更新缓存
	uc.statsCache.Store(newCacheData)

	// 统计缓存中的数据量
	gameCount := len(newCacheData.TodayGame) + len(newCacheData.YesterdayGame) + len(newCacheData.HistoryGame)
	seatCount := len(newCacheData.TodaySeat) + len(newCacheData.YesterdaySeat) + len(newCacheData.HistorySeat)

	// 统计实时索引数据量
	realtimeGameCount := 0
	realtimeSeatCount := 0
	for _, gameMap := range newCacheData.RealtimeGameIndex {
		realtimeGameCount += len(gameMap)
	}
	for _, assetMap := range newCacheData.RealtimeSeatIndex {
		for _, seatMap := range assetMap {
			realtimeSeatCount += len(seatMap)
		}
	}

	log.Debugf("Stats cache updated (yesterday data reused) - Game records: %d, Seat records: %d, Realtime games: %d, Realtime seats: %d",
		gameCount, seatCount, realtimeGameCount, realtimeSeatCount)
}

// GetCachedStats 获取缓存的统计数据
func (uc *useCase) GetCachedStats() *StatsCacheData {
	return uc.statsCache.Load()
}

// GetRealtimeGameData 按货币ID和游戏ID查询实时游戏数据
func (uc *useCase) GetRealtimeGameData(assetID int32, gameID int32) *pb.StatisticsGameData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	if gameMap, exists := cache.RealtimeGameIndex[assetID]; exists {
		if gameData, exists := gameMap[gameID]; exists {
			return gameData
		}
	}
	return nil
}

// GetRealtimeSeatData 按货币ID、游戏ID和座位ID查询实时座位数据
func (uc *useCase) GetRealtimeSeatData(assetID int32, gameID int32, seatID int32) *pb.StatisticsGameSeatData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	if gameMap, exists := cache.RealtimeSeatIndex[assetID]; exists {
		if seatMap, exists := gameMap[gameID]; exists {
			if seatData, exists := seatMap[seatID]; exists {
				return seatData
			}
		}
	}
	return nil
}

// GetGamesByAsset 按货币ID获取所有游戏数据
func (uc *useCase) GetGamesByAsset(assetID int32) []*pb.StatisticsGameData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	if gameMap, exists := cache.RealtimeGameIndex[assetID]; exists {
		var games []*pb.StatisticsGameData
		for _, gameData := range gameMap {
			games = append(games, gameData)
		}
		return games
	}
	return nil
}

// GetSeatsByAssetAndGame 按货币ID和游戏ID获取所有座位数据
func (uc *useCase) GetSeatsByAssetAndGame(assetID int32, gameID int32) []*pb.StatisticsGameSeatData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	if gameMap, exists := cache.RealtimeSeatIndex[assetID]; exists {
		if seatMap, exists := gameMap[gameID]; exists {
			var seats []*pb.StatisticsGameSeatData
			for _, seatData := range seatMap {
				seats = append(seats, seatData)
			}
			return seats
		}
	}
	return nil
}

// GetAllAssets 获取所有可用的货币ID列表
func (uc *useCase) GetAllAssets() []int32 {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	var assets []int32
	assetSet := make(map[int32]bool)

	// 从游戏数据中收集AssetID
	for assetID := range cache.RealtimeGameIndex {
		if !assetSet[assetID] {
			assets = append(assets, assetID)
			assetSet[assetID] = true
		}
	}

	// 从座位数据中收集AssetID
	for assetID := range cache.RealtimeSeatIndex {
		if !assetSet[assetID] {
			assets = append(assets, assetID)
			assetSet[assetID] = true
		}
	}

	return assets
}

// GetGamesByAssetSorted 按货币ID获取游戏数据并按TotalInput排序
func (uc *useCase) GetGamesByAssetSorted(assetID int32, limit int) []*pb.StatisticsGameData {
	games := uc.GetGamesByAsset(assetID)
	if games == nil {
		return nil
	}

	// 按TotalInput降序排序
	sort.Slice(games, func(i, j int) bool {
		return games[i].TotalInput > games[j].TotalInput
	})

	// 限制返回数量
	if limit > 0 && len(games) > limit {
		games = games[:limit]
	}

	return games
}

// GetSeatsByAssetAndGameSorted 按货币ID和游戏ID获取座位数据并按TotalInput排序
func (uc *useCase) GetSeatsByAssetAndGameSorted(assetID int32, gameID int32, limit int) []*pb.StatisticsGameSeatData {
	seats := uc.GetSeatsByAssetAndGame(assetID, gameID)
	if seats == nil {
		return nil
	}

	// 按TotalInput降序排序
	sort.Slice(seats, func(i, j int) bool {
		return seats[i].TotalInput > seats[j].TotalInput
	})

	// 限制返回数量
	if limit > 0 && len(seats) > limit {
		seats = seats[:limit]
	}

	return seats
}

// GetHotGames 获取热门游戏 (按TotalInput排序)
func (uc *useCase) GetHotGames(limit int) []*pb.StatisticsGameData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	var gameData []*pb.StatisticsGameData

	// 如果今日数据少于10个，使用昨日数据
	if len(cache.TodayGame) < 10 && len(cache.YesterdayGame) > 10 {
		log.Debugf("Today game data insufficient (%d < 10), using yesterday data", len(cache.TodayGame))
		gameData = make([]*pb.StatisticsGameData, len(cache.YesterdayGame))
		copy(gameData, cache.YesterdayGame)
	} else if len(cache.TodayGame) > 0 {
		gameData = make([]*pb.StatisticsGameData, len(cache.TodayGame))
		copy(gameData, cache.TodayGame)
	} else {
		return nil
	}

	// 按TotalInput降序排序
	sort.Slice(gameData, func(i, j int) bool {
		return gameData[i].TotalInput > gameData[j].TotalInput
	})

	// 限制返回数量
	if limit > 0 && len(gameData) > limit {
		gameData = gameData[:limit]
	}

	log.Debugf("Hot games found: %d games", len(gameData))
	return gameData
}

// GetHotSeats 获取热门座位 (按TotalInput排序)
func (uc *useCase) GetHotSeats(limit int) []*pb.StatisticsGameSeatData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	var seatData []*pb.StatisticsGameSeatData

	// 如果今日数据少于10个，使用昨日数据
	if len(cache.TodaySeat) < 10 && len(cache.YesterdaySeat) > 10 {
		log.Debugf("Today seat data insufficient (%d < 10), using yesterday data", len(cache.TodaySeat))
		seatData = make([]*pb.StatisticsGameSeatData, len(cache.YesterdaySeat))
		copy(seatData, cache.YesterdaySeat)
	} else if len(cache.TodaySeat) > 0 {
		seatData = make([]*pb.StatisticsGameSeatData, len(cache.TodaySeat))
		copy(seatData, cache.TodaySeat)
	} else {
		return nil
	}

	// 按TotalInput降序排序
	sort.Slice(seatData, func(i, j int) bool {
		return seatData[i].TotalInput > seatData[j].TotalInput
	})

	// 限制返回数量
	if limit > 0 && len(seatData) > limit {
		seatData = seatData[:limit]
	}

	log.Debugf("Hot seats found: %d seats", len(seatData))
	return seatData
}

// GetGameDataByType 根据类型获取游戏数据
func (uc *useCase) GetGameDataByType(dataType string) []*pb.StatisticsGameData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	switch dataType {
	case "today":
		return cache.TodayGame
	case "yesterday":
		return cache.YesterdayGame
	case "history":
		return cache.HistoryGame
	default:
		return nil
	}
}

// GetSeatDataByType 根据类型获取座位数据
func (uc *useCase) GetSeatDataByType(dataType string) []*pb.StatisticsGameSeatData {
	cache := uc.statsCache.Load()
	if cache == nil {
		return nil
	}

	switch dataType {
	case "today":
		return cache.TodaySeat
	case "yesterday":
		return cache.YesterdaySeat
	case "history":
		return cache.HistorySeat
	default:
		return nil
	}
}
