package account

import (
	"s2/common/c2s"
	"s2/define"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/message"
)

type module struct {
	iface.IModule
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	return m
}

func (m module) Name() string {
	return define.ModuleName.Account
}

func (m *module) Init() error {
	message.Response(m, onThirdAppAuthOrCreateAccountReq)
	message.Response(m, onTelegramLoginToAccountReq)
	message.Response(m, onGMUserBaseInfoToAccountReq)
	c2s.Enable(m)
	// c2s.Response(onAuthOrCreateAccountReq)
	c2s.ResponseCtx(onAuthOrCreateAccountReq)
	c2s.ResponseCtx(onWalletLoginReq)
	c2s.Response(onAccountLoginReq)
	c2s.ResponseCtx(onTelegramLoginReq)
	c2s.Response(onSendCodeReq)
	c2s.ResponseCtx(onEmailRegisterReq)
	c2s.Response(onEmailLoginReq)
	c2s.Response(onChangePasswordReq)
	c2s.Response(onConfigReq)
	c2s.Response(onNavigationModuleReq)
	err := mdb.Default().Table(TableName).AutoMigrate(&Account{})
	AutoIncrement()
	return err
}
