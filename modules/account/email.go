package account

import (
	"context"
	"fmt"
	"net/mail"
	"net/smtp"
	"strings"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"google.golang.org/api/idtoken"
)

func SendMail(to_, subject, body string) error {
	headers := make(map[string]string)
	from := mail.Address{Name: "Admin", Address: conf.Str("smtp.form", "")}
	to := mail.Address{Name: "User", Address: to_}
	headers["From"] = from.String()
	headers["To"] = to.String()
	headers["Subject"] = subject
	headers["Content-Type"] = "text/html; charset=UTF-8"
	var message strings.Builder
	for k, v := range headers {
		message.WriteString(fmt.Sprintf("%s: %s\r\n", k, v))
	}
	user := conf.Str("smtp.user", "")
	pass := conf.Str("smtp.pass", "")
	port := conf.Num[int]("smtp.port", 0)
	host := conf.Str("smtp.host", "")
	message.WriteString("\r\n" + body)
	auth := smtp.PlainAuth("", user, pass, host)
	err := smtp.SendMail(fmt.Sprintf("%s:%d", host, port), auth, from.Address, []string{to.Address}, []byte(message.String()))
	return err
}

// GoogleUser 包含从 Google Token 解析出的用户信息
type GoogleUser struct {
	UserID string
	Name   string
	Email  string
}

// CheckGoogleToken 验证谷歌令牌并返回用户信息
func CheckGoogleToken(token string) GoogleUser {
	clientID := conf.Str("google.clientId", "")
	if clientID == "" {
		return GoogleUser{UserID: "-1"}
	}
	// 验证 Google ID 令牌
	payload, err := idtoken.Validate(context.Background(), token, clientID)
	if err != nil {
		log.Errorf("Token validation failed: %v", err)
		return GoogleUser{UserID: "-1"}
	}

	// 提取用户信息
	userID, _ := payload.Claims["sub"].(string)
	name, _ := payload.Claims["name"].(string)
	email, _ := payload.Claims["email"].(string)

	return GoogleUser{
		UserID: userID,
		Name:   name,
		Email:  email,
	}
}
