package account

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"s2/common"
	"s2/common/cache"
	"s2/define"
	"s2/pb"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"golang.org/x/crypto/bcrypt"

	"math/rand"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func SaveUserBasicInfo(account *Account) {
	cache.SaveUserBasicInfo(&cache.UserBasicInfo{
		ID:        account.ID,
		ServerID:  account.ServerID,
		RegTime:   account.RegTime,
		Email:     account.Email,
		Address:   account.Address,
		Telegram:  account.TelegramId,
		XId:       account.XId,
		DiscordId: account.DiscordId,
		Name:      account.Name,
		Channel:   account.Channel,
		ParentID:  account.ParentUid,
		IsRobot:   false,
	})
}
func checkChannel(channel string) string {
	if len(channel) > 20 {
		return channel[:20]
	}
	return channel
}
func inviteCodeToParentUid(inviteCode string) int64 {
	if inviteCode == "" {
		return 0
	}
	uid, err := common.HexToInt64(inviteCode)
	if err != nil {
		return 0
	}
	var data []*Account
	err = mdb.Default().Table(TableName).Find(&data, "id = ?", uid).Error
	if err != nil {
		log.Error(err)
		return 0
	}
	if len(data) == 0 {
		return 0
	}
	return uid
}

func onThirdAppAuthOrCreateAccountReq(body *pb.ThirdAppAuthOrCreateAccountReq, response func(*pb.ThirdAppAuthOrCreateAccountResp, error)) {
	if len(body.Account) == 0 {
		response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	body.Account = body.AppID + "_" + body.Account
	var data []*Account
	err := mdb.Default().Table(TableName).Find(&data, "account = ?", body.Account).Error
	if err != nil {
		log.Error(err)
		response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	var account *Account
	if len(data) == 0 { // 创建新账户
		tx := mdb.Default().TxReadCommit()
		account = &Account{Account: body.Account, Name: body.Account, RegTime: time.Now().Unix(), Channel: body.AppID}
		err = mdb.Default().Table(TableName).Create(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		resp, err := message.RequestAny[pb.CreateNewUserResp](define.ModuleName.Lobby, &pb.CreateNewUserReq{
			UserID:      account.ID,
			Channel:     body.AppID,
			AccountName: account.Account,
		})
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		account.ServerID = resp.ServerID
		err = tx.Table(TableName).Updates(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		tx.Commit()
	} else {
		account = data[0]
		if account.PwdDigest != "" {
			response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.ACCOUNT_ALREADY_EXISTS}, nil)
			return
		}
	}
	SaveUserBasicInfo(account)
	_, err = cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.ThirdAppAuthOrCreateAccountResp{Code: pb.SUCCESS, UserID: account.ID}, nil)
}

func onAuthOrCreateAccountReq(body *pb.AuthOrCreateAccountReq, ctx *pb.C2SPackageReq, response func(*pb.AuthOrCreateAccountResp, error)) {
	if len(body.Account) == 0 {
		response(&pb.AuthOrCreateAccountResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var data []*Account
	err := mdb.Default().Table(TableName).Find(&data, "account = ?", body.Account).Error
	if err != nil {
		log.Error(err)
		response(&pb.AuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	var account *Account
	if len(data) == 0 { // 创建新账户
		hashed, err := bcrypt.GenerateFromPassword([]byte(body.Password), bcrypt.DefaultCost)
		if err != nil {
			response(&pb.AuthOrCreateAccountResp{Code: pb.PARAM_ERROR}, nil)
			return
		}
		hash := base64.StdEncoding.EncodeToString(hashed)
		tx := mdb.Default().TxReadCommit()
		parentUid := inviteCodeToParentUid(body.InviteCode)
		channel := checkChannel(body.Channel)
		account = &Account{Account: body.Account, Name: body.Account, PwdDigest: hash, RegTime: time.Now().Unix(), RegIP: ctx.IP, Device: ctx.Device, ParentUid: parentUid, Channel: channel}
		err = tx.Table(TableName).Create(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.AuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		resp, err := message.RequestAny[pb.CreateNewUserResp](define.ModuleName.Lobby, &pb.CreateNewUserReq{
			UserID:   account.ID,
			ParentId: account.ParentUid,
			Channel:  body.Channel,
		})
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.AuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		account.ServerID = resp.ServerID
		err = tx.Table(TableName).Updates(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.AuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		tx.Commit()
	} else {
		account = data[0]
		pwdHash, err := base64.StdEncoding.DecodeString(account.PwdDigest)
		if err != nil {
			log.Errorf("Error decoding password hash for account: %s, hash: %s, error: %v", account.Account, account.PwdDigest, err)
			response(&pb.AuthOrCreateAccountResp{Code: pb.PASSWORD_ERROR}, nil)
			return
		}
		if bcrypt.CompareHashAndPassword(pwdHash, []byte(body.Password)) != nil {
			response(&pb.AuthOrCreateAccountResp{Code: pb.PASSWORD_ERROR}, nil)
			return
		}
	}
	SaveUserBasicInfo(account)
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.AuthOrCreateAccountResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.AuthOrCreateAccountResp{Code: pb.SUCCESS, Token: token}, nil)
}

func onAccountLoginReq(body *pb.AccountLoginReq, response func(*pb.AccountLoginResp, error)) {
	if len(body.Account) == 0 {
		response(&pb.AccountLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var data []*Account
	err := mdb.Default().Table(TableName).Find(&data, "account = ?", body.Account).Error
	if err != nil {
		log.Error(err)
		response(&pb.AccountLoginResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	hashed, err := bcrypt.GenerateFromPassword([]byte(body.Password), bcrypt.DefaultCost)
	if err != nil {
		response(&pb.AccountLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	hash := base64.StdEncoding.EncodeToString(hashed)
	var account *Account
	if len(data) == 0 { // 创建新账户
		response(&pb.AccountLoginResp{Code: pb.ACCOUNT_NOT_FOUND}, nil)
		return
	} else {
		account = data[0]
	}
	if hash != account.PwdDigest {
		response(&pb.AccountLoginResp{Code: pb.PASSWORD_ERROR}, nil)
		return
	}
	SaveUserBasicInfo(account)
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.AccountLoginResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.AccountLoginResp{Code: pb.SUCCESS, Token: token}, nil)
}

func onWalletLoginReq(body *pb.WalletLoginReq, ctx *pb.C2SPackageReq, response func(*pb.WalletLoginResp, error)) {
	if body.Address == "" || body.Msg == "" || body.Sign == "" {
		response(&pb.WalletLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	a, err := VerifyMessageEthereum(body.Msg, body.Sign)
	if err != nil {
		response(&pb.WalletLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	body.Address = strings.ToLower(body.Address)
	if strings.ToLower(a) != body.Address {
		response(&pb.WalletLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var data []*Account
	err = mdb.Default().Table(TableName).Find(&data, "address = ?", body.Address).Error
	if err != nil {
		log.Error(err)
		response(&pb.WalletLoginResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	var account *Account
	if len(data) == 0 { // 创建新账户
		tx := mdb.Default().TxReadCommit()
		parentUid := inviteCodeToParentUid(body.InviteCode)
		channel := checkChannel(body.Channel)
		account = &Account{Account: body.Address, Name: body.Address, Address: body.Address, RegTime: time.Now().Unix(), RegIP: ctx.IP, Device: ctx.Device, ParentUid: parentUid, Channel: channel}
		err = tx.Table(TableName).Create(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.WalletLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		resp, err := message.RequestAny[pb.CreateNewUserResp](define.ModuleName.Lobby, &pb.CreateNewUserReq{
			UserID:   account.ID,
			ParentId: account.ParentUid,
			Channel:  account.Channel,
		})
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.WalletLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		account.ServerID = resp.ServerID
		err = tx.Table(TableName).Updates(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.WalletLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		tx.Commit()
	} else {
		account = data[0]
	}
	SaveUserBasicInfo(account)
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.WalletLoginResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.WalletLoginResp{Code: pb.SUCCESS, Token: token}, nil)
}
func onTelegramLoginToAccountReq(body *pb.TelegramLoginToAccountReq, response func(*pb.TelegramLoginResp, error)) {
	onTelegramLoginReq(&pb.TelegramLoginReq{
		TelegramData: body.TelegramData,
		InviteCode:   "",
		Channel:      "",
	}, &pb.C2SPackageReq{IP: body.IP, Device: "telegram"}, response)
}

func onTelegramLoginReq(body *pb.TelegramLoginReq, ctx *pb.C2SPackageReq, response func(*pb.TelegramLoginResp, error)) {
	if body.TelegramData == "" {
		response(&pb.TelegramLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	user, _, _, err := TelegramTokenAnalysis(body.TelegramData)
	if err != nil {
		log.Error(err)
		response(&pb.TelegramLoginResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	var data []*Account
	err = mdb.Default().Table(TableName).Find(&data, "telegramId = ?", user.ID).Error
	if err != nil {
		log.Error(err)
		response(&pb.TelegramLoginResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	var account *Account
	if len(data) == 0 { // 创建新账户
		tx := mdb.Default().TxReadCommit()
		parentUid := inviteCodeToParentUid(body.InviteCode)
		channel := checkChannel(body.Channel)
		account = &Account{Account: "telegram:" + strconv.Itoa(int(user.ID)), Name: user.FirstName + " " + user.LastName, TelegramId: user.ID, RegTime: time.Now().Unix(), RegIP: ctx.IP, Device: ctx.Device, ParentUid: parentUid, Channel: channel}
		err = tx.Table(TableName).Create(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.TelegramLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		resp, err := message.RequestAny[pb.CreateNewUserResp](define.ModuleName.Lobby, &pb.CreateNewUserReq{
			UserID:   account.ID,
			ParentId: account.ParentUid,
			Channel:  account.Channel,
		})
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.TelegramLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		account.ServerID = resp.ServerID
		err = tx.Table(TableName).Updates(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.TelegramLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		tx.Commit()
	} else {
		account = data[0]
	}
	SaveUserBasicInfo(account)
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.TelegramLoginResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.TelegramLoginResp{Code: pb.SUCCESS, Token: token}, nil)
}

type CodeData struct {
	Code        string
	ExpiredTime int64
}

var emailCodeMap = map[string]*CodeData{}
var muCode sync.Mutex

func onSendCodeReq(body *pb.SendCodeReq, response func(*pb.SendCodeResp, error)) {
	if body.Email == "" {
		response(&pb.SendCodeResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	now := time.Now().UTC().Unix()
	muCode.Lock()
	oldCode := emailCodeMap[body.Email]
	if oldCode != nil && oldCode.ExpiredTime > 0 && now < oldCode.ExpiredTime {
		muCode.Unlock()
		response(&pb.SendCodeResp{Code: pb.SUCCESS}, nil)
		return
	}
	rand.Seed(time.Now().UnixNano())
	code_ := rand.Intn(900000) + 100000
	code := strconv.Itoa(code_)
	emailCodeMap[body.Email] = &CodeData{Code: code, ExpiredTime: now + 60*10}
	muCode.Unlock()

	text := conf.Str("smtp.text", "")
	text = strings.Replace(text, "______", code, -1)
	SendMail(body.Email, "Verification Code", text)

	response(&pb.SendCodeResp{Code: pb.SUCCESS}, nil)
}

func onEmailRegisterReq(body *pb.EmailRegisterReq, ctx *pb.C2SPackageReq, response func(*pb.EmailRegisterResp, error)) {
	if body.Email == "" || body.Code == "" || body.Password == "" {
		response(&pb.EmailRegisterResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var data []*Account
	err := mdb.Default().Table(TableName).Find(&data, "email = ?", body.Email).Error
	if err != nil {
		log.Error(err)
		response(&pb.EmailRegisterResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	now := time.Now().UTC().Unix()
	muCode.Lock()
	oldCode := emailCodeMap[body.Email]
	if oldCode == nil {
		muCode.Unlock()
		response(&pb.EmailRegisterResp{Code: pb.VERIFICATION_CODE_NOT_SENT}, nil)
		return
	}
	if now > oldCode.ExpiredTime {
		muCode.Unlock()
		response(&pb.EmailRegisterResp{Code: pb.VERIFICATION_CODE_EXPIRED}, nil)
		return
	}
	if oldCode.Code != body.Code {
		muCode.Unlock()
		response(&pb.EmailRegisterResp{Code: pb.VERIFICATION_CODE_ERROR}, nil)
		return
	}
	emailCodeMap[body.Email] = nil
	muCode.Unlock()

	hashed, err := bcrypt.GenerateFromPassword([]byte(body.Password), bcrypt.DefaultCost)
	if err != nil {
		response(&pb.EmailRegisterResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	hash := base64.StdEncoding.EncodeToString(hashed)
	var account *Account
	if len(data) == 0 { // 创建新账户
		tx := mdb.Default().TxReadCommit()
		parentUid := inviteCodeToParentUid(body.InviteCode)
		channel := checkChannel(body.Channel)
		account = &Account{Account: body.Email, Name: body.Email, Email: body.Email, PwdDigest: hash, RegTime: time.Now().Unix(), RegIP: ctx.IP, Device: ctx.Device, ParentUid: parentUid, Channel: channel}
		err = tx.Table(TableName).Create(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.EmailRegisterResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		resp, err := message.RequestAny[pb.CreateNewUserResp](define.ModuleName.Lobby, &pb.CreateNewUserReq{
			UserID:   account.ID,
			ParentId: account.ParentUid,
			Channel:  account.Channel,
		})
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.EmailRegisterResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		account.ServerID = resp.ServerID
		err = tx.Table(TableName).Updates(account).Error
		if err != nil {
			log.Error(err)
			tx.Rollback()
			response(&pb.EmailRegisterResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		tx.Commit()
	} else {
		response(&pb.EmailRegisterResp{Code: pb.ACCOUNT_ALREADY_EXISTS}, nil)
		return
		// account = data[0]
	}
	SaveUserBasicInfo(account)
	// if hash != account.PwdDigest {
	// 	response(&pb.EmailRegisterResp{Code: pb.PARAM_ERROR}, nil)
	// 	return
	// }
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.EmailRegisterResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.EmailRegisterResp{Code: pb.SUCCESS, Token: token}, nil)
}

func onChangePasswordReq(body *pb.ChangePasswordReq, response func(*pb.ChangePasswordResp, error)) {
	if body.Email == "" || body.PasswordNew == "" {
		response(&pb.ChangePasswordResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var data []*Account
	err := mdb.Default().Table(TableName).Find(&data, "email = ?", body.Email).Error
	if err != nil {
		log.Error(err)
		response(&pb.ChangePasswordResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	var account *Account
	if len(data) == 0 {
		response(&pb.ChangePasswordResp{Code: pb.ACCOUNT_NOT_FOUND}, nil)
		return
	} else {
		account = data[0]
	}
	if body.Code != "" {
		muCode.Lock()
		now := time.Now().UTC().Unix()
		oldCode := emailCodeMap[body.Email]
		if oldCode == nil {
			muCode.Unlock()
			response(&pb.ChangePasswordResp{Code: pb.VERIFICATION_CODE_NOT_SENT}, nil)
			return
		}
		if now > oldCode.ExpiredTime {
			muCode.Unlock()
			response(&pb.ChangePasswordResp{Code: pb.VERIFICATION_CODE_EXPIRED}, nil)
			return
		}
		if oldCode.Code != body.Code {
			muCode.Unlock()
			response(&pb.ChangePasswordResp{Code: pb.VERIFICATION_CODE_ERROR}, nil)
			return
		}
		emailCodeMap[body.Email] = nil
		muCode.Unlock()
	} else if body.PasswordOld != "" {
		hashed, err := bcrypt.GenerateFromPassword([]byte(body.PasswordOld), bcrypt.DefaultCost)
		if err != nil {
			response(&pb.ChangePasswordResp{Code: pb.PARAM_ERROR}, nil)
			return
		}
		hash := base64.StdEncoding.EncodeToString(hashed)
		if hash != account.PwdDigest {
			response(&pb.ChangePasswordResp{Code: pb.PARAM_ERROR}, nil)
			return
		}
	} else {
		response(&pb.ChangePasswordResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	hashed, err := bcrypt.GenerateFromPassword([]byte(body.PasswordNew), bcrypt.DefaultCost)
	if err != nil {
		response(&pb.ChangePasswordResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	hash := base64.StdEncoding.EncodeToString(hashed)

	tx := mdb.Default().TxReadCommit()
	account.PwdDigest = hash
	err = tx.Table(TableName).Updates(account).Error
	if err != nil {
		log.Error(err)
		tx.Rollback()
		response(&pb.ChangePasswordResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	tx.Commit()

	SaveUserBasicInfo(account)
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.ChangePasswordResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.ChangePasswordResp{Code: pb.SUCCESS, Token: token}, nil)
}

func onEmailLoginReq(body *pb.EmailLoginReq, response func(*pb.EmailLoginResp, error)) {
	if (body.Email == "" || body.Password == "") && body.GoogToken == "" {
		response(&pb.EmailLoginResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	var account *Account
	if body.GoogToken != "" {
		googleuser := CheckGoogleToken(body.GoogToken)
		log.Warn("GoogToken:", body.GoogToken, " googleuser.UserID:", googleuser.UserID, " googleuser.Email:", googleuser.Email, " googleuser.Name:", googleuser.Name)
		if googleuser.UserID == "-1" {
			response(&pb.EmailLoginResp{Code: pb.PARAM_ERROR}, nil)
			return
		}
		var data []*Account
		err := mdb.Default().Table(TableName).Find(&data, "email = ?", googleuser.Email).Error
		if err != nil {
			log.Error(err)
			response(&pb.EmailLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		if len(data) == 0 {
			tx := mdb.Default().TxReadCommit()
			account = &Account{Account: googleuser.Email, Name: googleuser.Email, Email: googleuser.Email, PwdDigest: "", RegTime: time.Now().Unix()}
			err := tx.Table(TableName).Create(account).Error
			if err != nil {
				log.Error(err)
				tx.Rollback()
				response(&pb.EmailLoginResp{Code: pb.SERVER_ERROR}, err)
				return
			}
			resp, err := message.RequestAny[pb.CreateNewUserResp](define.ModuleName.Lobby, &pb.CreateNewUserReq{
				UserID: account.ID,
			})
			if err != nil {
				log.Error(err)
				tx.Rollback()
				response(&pb.EmailLoginResp{Code: pb.SERVER_ERROR}, err)
				return
			}
			account.ServerID = resp.ServerID
			err = tx.Table(TableName).Updates(account).Error
			if err != nil {
				log.Error(err)
				tx.Rollback()
				response(&pb.EmailLoginResp{Code: pb.SERVER_ERROR}, err)
				return
			}
			tx.Commit()
		} else {
			account = data[0]
		}
	} else {
		var data []*Account
		err := mdb.Default().Table(TableName).Find(&data, "email = ?", body.Email).Error
		if err != nil {
			log.Error(err)
			response(&pb.EmailLoginResp{Code: pb.SERVER_ERROR}, err)
			return
		}
		hashed, err := bcrypt.GenerateFromPassword([]byte(body.Password), bcrypt.DefaultCost)
		if err != nil {
			response(&pb.EmailLoginResp{Code: pb.PARAM_ERROR}, nil)
			return
		}
		hash := base64.StdEncoding.EncodeToString(hashed)
		if len(data) == 0 {
			response(&pb.EmailLoginResp{Code: pb.ACCOUNT_NOT_FOUND}, nil)
			return
		} else {
			account = data[0]
		}
		if hash != account.PwdDigest || account.PwdDigest == "" {
			response(&pb.EmailLoginResp{Code: pb.PARAM_ERROR}, nil)
			return
		}
	}
	SaveUserBasicInfo(account)
	token, err := cache.UpdateOrSetUserToken(account.ID)
	if err != nil {
		log.Error(err)
		response(&pb.EmailLoginResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	response(&pb.EmailLoginResp{Code: pb.SUCCESS, Token: token}, nil)
}

func VerifyMessageEthereum(message string, signedMessage string) (string, error) {
	// Hash the unsigned message using EIP-191
	hashedMessage := []byte("\x19Ethereum Signed Message:\n" + strconv.Itoa(len(message)) + message)
	hash := crypto.Keccak256Hash(hashedMessage)
	// Get the bytes of the signed message
	decodedMessage := hexutil.MustDecode(signedMessage)
	// Handles cases where EIP-115 is not implemented (most wallets don't implement it)
	if decodedMessage[64] == 27 || decodedMessage[64] == 28 {
		decodedMessage[64] -= 27
	}
	// Recover a public key from the signed message
	sigPublicKeyECDSA, err := crypto.SigToPub(hash.Bytes(), decodedMessage)
	if sigPublicKeyECDSA == nil {
		err = errors.New("Could not get a public get from the message signature")
	}
	if err != nil {
		return "", err
	}
	return crypto.PubkeyToAddress(*sigPublicKeyECDSA).String(), nil
}

type TelegramUser struct {
	ID              int64  `json:"id"`
	FirstName       string `json:"first_name"`
	LastName        string `json:"last_name"`
	LanguageCode    string `json:"language_code"`
	AllowsWriteToPM bool   `json:"allows_write_to_pm"`
}

// 解析telegramtoken
func TelegramTokenAnalysisByH5(tgdata string) (*TelegramUser, int64, *string, error) {
	var channel *string = nil
	var parameter map[string]interface{}
	err := json.Unmarshal([]byte(tgdata), &parameter)
	if err != nil {
		log.Error("解析查询字符串失败: %v", err)
		return nil, -1, channel, err
	}
	hash := parameter["hash"]
	var keys []string
	for key, _ := range parameter {
		if key != "hash" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)
	var result strings.Builder
	for i, key := range keys {
		if key == "auth_date" || key == "id" {
			result.WriteString(fmt.Sprintf("%s=%.0f", key, parameter[key]))
		} else {
			result.WriteString(fmt.Sprintf("%s=%v", key, parameter[key]))
		}
		if i < len(keys)-1 {
			result.WriteString("\n")
		}
	}
	s := result.String()
	token := conf.Str("telegram.token", "")
	secret_key := Sha256(token)
	calculatedHash := HmacSha256(string(secret_key), s)
	calculatedHash_ := hex.EncodeToString(calculatedHash)
	if calculatedHash_ != hash {
		log.Error("Hash 验证失败！计算出的 Hash: %s, 接收到的 Hash: %s", calculatedHash, hash)
		return nil, -1, channel, err
	}
	var user TelegramUser
	if tgdata != "" {
		err := json.Unmarshal([]byte(tgdata), &user)
		if err != nil {
			log.Error("解析 'user' 字段的 JSON 失败: %v", err)
			return nil, -1, channel, err
		}
		log.Infof("解析后的用户数据:%+v", user)
	}
	return &user, -1, channel, nil
}

// 解析telegramtoken
func TelegramTokenAnalysis(tgdata string) (*TelegramUser, int64, *string, error) {
	if !strings.Contains(tgdata, "query_id=") {
		return TelegramTokenAnalysisByH5(tgdata)
	}
	var channel *string = nil
	values, err := url.ParseQuery(tgdata)
	if err != nil {
		log.Error("解析查询字符串失败: %v", err)
		return nil, -1, channel, err
	}
	token := conf.Str("telegram.token", "")
	receivedHash := values.Get("hash")
	if receivedHash == "" {
		log.Error("未找到 hash 参数")
		return nil, -1, channel, err
	}
	values.Del("hash")
	var keys []string
	for key := range values {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	var dataLines []string
	for _, key := range keys {
		value := values.Get(key)
		dataLines = append(dataLines, fmt.Sprintf("%s=%s", key, value))
	}
	data_check_string := strings.Join(dataLines, "\n")
	log.Infof("TOKEN ERROR data_check_string:%s", data_check_string)
	secret_key := HmacSha256("WebAppData", token)
	calculatedHash := HmacSha256(string(secret_key), data_check_string)
	calculatedHash_ := hex.EncodeToString(calculatedHash)
	if calculatedHash_ != receivedHash {
		log.Error("Hash 验证失败！计算出的 Hash: %s, 接收到的 Hash: %s", calculatedHash, receivedHash)
		return nil, -1, channel, err
	}
	userJSON := values.Get("user")
	var user TelegramUser
	if userJSON != "" {
		err := json.Unmarshal([]byte(userJSON), &user)
		if err != nil {
			log.Error("解析 'user' 字段的 JSON 失败: %v", err)
			return nil, -1, channel, err
		}
		log.Infof("解析后的用户数据:%+v", user)
	}
	var startParamInt int64 = -1
	startParamStr := values.Get("start_param")
	if startParamStr != "" {
		startParamInt_, err := strconv.Atoi(startParamStr)
		if err != nil {
			parts := strings.Split(startParamStr, "_")
			if len(parts) == 1 { //startapp=channel
				channel = &parts[0]
			} else if len(parts) == 2 { //startapp=channel_uid
				channel = &parts[0]
				startParamInt_, err = strconv.Atoi(parts[1])
				if err != nil {
					return nil, -1, channel, err
				}
				startParamInt = int64(startParamInt_)
			} else {
				return nil, -1, channel, err
			}
		} else { ////startapp=uid
			startParamInt = int64(startParamInt_)
		}
	}
	log.Infof("start_param 的值是: %s (整数形式: %d)", startParamStr, startParamInt)
	return &user, startParamInt, channel, nil
}

func onGMUserBaseInfoToAccountReq(body *pb.GMUserBaseInfoToAccountReq, response func(*pb.GMUserBaseInfoToAccountResp, error)) {
	var data []*Account
	err := mdb.Default().Table(TableName).Find(&data, "id = ?", body.UserID).Error
	if err != nil {
		log.Error(err)
		response(&pb.GMUserBaseInfoToAccountResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	if len(data) == 0 {
		response(&pb.GMUserBaseInfoToAccountResp{Code: pb.ACCOUNT_NOT_FOUND}, nil)
		return
	}
	account := data[0]
	response(&pb.GMUserBaseInfoToAccountResp{
		Code: pb.SUCCESS,
		Data: &pb.GMUserBaseInfo{
			UserID:         account.ID,
			Name:           account.Name,
			Address:        account.Address,
			ParentUid:      account.ParentUid,
			Channel:        account.Channel,
			RegisterIP:     account.RegIP,
			RegisterDevice: account.Device,
			CreateTime:     account.RegTime,
		},
	}, nil)
}

// HmacSha256 计算HmacSha256
// key 是加密所使用的key
// data 是加密的内容
func HmacSha256(key string, data string) []byte {
	mac := hmac.New(sha256.New, []byte(key))
	_, _ = mac.Write([]byte(data))

	return mac.Sum(nil)
}

// Sha256 计算SHA-256哈希值
// data 是要计算哈希值的内容
func Sha256(data string) []byte {
	hash := sha256.New()
	_, _ = hash.Write([]byte(data))
	return hash.Sum(nil)
}
