package account

import (
	"database/sql"
	"fmt"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
)

const TableName = "account"

type Account struct {
	ID         int64  `gorm:"column:id;primary_key;"`
	Account    string `gorm:"column:account;type:varchar(100);uniqueIndex:account_IDX;"`
	Address    string `gorm:"column:address;type:varchar(100);index:address_IDX;"`
	Email      string `gorm:"column:email;type:varchar(254);index:email_IDX;"`
	TelegramId int64  `gorm:"column:telegramId;index:telegramId_IDX;"`
	XId        string `gorm:"column:xId;index:xId_IDX;"`
	DiscordId  string `gorm:"column:discordId;index:discordId_IDX;"`
	Name       string `gorm:"column:Name;"`
	ParentUid  int64  `gorm:"column:parentUid;"`
	PwdDigest  string `gorm:"column:pwd_digest;"`
	ServerID   uint32 `gorm:"column:serverID;"`
	Channel    string `gorm:"column:channel;"`
	RegIP      string `gorm:"column:reg_ip;"`
	RegTime    int64  `gorm:"column:reg_time;"`
	Device     string `gorm:"column:device;"`
}

func AutoIncrement() error {
	// var autoIncrement int
	var autoIncrement sql.NullInt64 // 使用 sql.NullInt64 处理可能的 NULL 值
	dbName := conf.Map[any]("mdb", nil)["default"].(map[string]any)["name"].(string)
	// 查询当前 AUTO_INCREMENT 值
	err := mdb.Default().Table(TableName).Raw(
		"SELECT AUTO_INCREMENT FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
		dbName, TableName,
	).Scan(&autoIncrement).Error
	if err != nil {
		log.Error("查询 AUTO_INCREMENT 失败: %v", err)
		return err
	}
	// 检查 AUTO_INCREMENT 是否有效（非 NULL）
	if !autoIncrement.Valid {
		log.Error("表 %s 没有设置 AUTO_INCREMENT 属性", TableName)
		// 可以选择在这里返回错误，或者继续处理
		// 这里我们选择继续检查最大 ID
	} else {
		log.Errorf("当前 AUTO_INCREMENT 值: %d", autoIncrement.Int64)
		if autoIncrement.Int64 > ********* {
			log.Error("AUTO_INCREMENT 已超过阈值，无需修改")
			return nil
		}
	}
	// 查询表中 id 列的最大值
	var maxID sql.NullInt64
	err = mdb.Default().Table(TableName).Raw("SELECT MAX(id) FROM " + TableName).Scan(&maxID).Error
	if err != nil {
		log.Error("查询最大 ID 失败: %v", err)
		return err
	}
	startUid := conf.Num[int]("account.startUid", 0)
	// 设置新的 AUTO_INCREMENT 值
	var newAutoIncrement int64 = int64(startUid) // 默认值
	if maxID.Valid && maxID.Int64 > newAutoIncrement {
		newAutoIncrement = maxID.Int64 + 1
	}
	// 执行 ALTER TABLE 语句更新 AUTO_INCREMENT
	sql := fmt.Sprintf("ALTER TABLE %s AUTO_INCREMENT = %d", TableName, newAutoIncrement)
	err = mdb.Default().Exec(sql).Error
	if err != nil {
		log.Error("设置 AUTO_INCREMENT 失败: ", err)
		return err
	}

	log.Warn("已将 AUTO_INCREMENT 设置为: ", newAutoIncrement)
	return nil
}
