package account

import (
	"s2/common"
	"s2/pb"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
)

func onConfigReq(body *pb.ConfigReq, response func(*pb.ConfigResp, error)) {
	jackpotPopUrl := conf.Str("clientconfig.jackpotPopUrl", "")
	verfiyUrl := conf.Str("clientconfig.verfiyUrl", "")
	hashUrl := conf.Str("clientconfig.hashUrl", "")
	verfiyApiUrl := conf.Str("clientconfig.verfiyApiUrl", "")
	response(&pb.ConfigResp{Code: pb.SUCCESS, JackpotPopUrl: jackpotPopUrl, VerfiyUrl: verfiyUrl, HashUrl: hashUrl, VerfiyApiUrl: verfiyApiUrl}, nil)
}

func onNavigationModuleReq(body *pb.NavigationModuleReq, response func(*pb.NavigationModuleResp, error)) {
	var modules []*common.NavigationModule
	query := mdb.Default().Table(common.TableNameNavigationModule).Where("1=1")
	err := query.Order("sort_id ASC, id ASC").Find(&modules).Error
	if err != nil {
		log.Error(err)
		response(&pb.NavigationModuleResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	// 转换为protobuf格式
	list := make([]*pb.NavigationModule, 0, len(modules))
	for _, module := range modules {
		list = append(list, module.ToPB())
	}
	response(&pb.NavigationModuleResp{Code: pb.SUCCESS, List: list}, nil)
}
