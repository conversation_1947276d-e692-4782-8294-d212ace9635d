package gamedoor

import (
	"fmt"
	"igame"
	"net/http"
	"s2/common/cache"
	"s2/gsconf"
	"s2/pb"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (m *module) OnEnterXPPGame(ctx *gin.Context) {

}

func (m *module) OnHandleXPPGameService(ctx *gin.Context) {
	// 解析表单数据
	err := ctx.Request.ParseForm()
	if err != nil {
		ctx.String(http.StatusBadRequest, "Failed to parse form data")
		return
	}
	mgckey := ctx.Request.FormValue("mgckey")

	token, err := ParamToken(mgckey)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	bc, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	action := ctx.Request.FormValue("action")
	if action == "doInit" {
		resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
			UserID: bc.ID,
		})
		if err != nil {
			ctx.Data(http.StatusOK, "text/html;charset=UTF-8", []byte("获取用户余额失败"))
		}
		balance := resp.Balance[token.AssetID]

		inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
			return iic.CurrencyID == token.AssetID && iic.GameID == token.GameID
		})
		if inputInfo == nil {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
		valueList := []string{}
		for _, v := range inputInfo.ValueList {
			valueList = append(valueList, fmt.Sprintf("%f", v))
		}

		// ctx = basic.SpinContext{}
		// ctx["max"] = 0
		// ctx["data"] = [][]byte{}
		// ctx["data"] = append(ctx["data"].([][]byte)
		//  []byte("scatters=1~2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,100,60,0,0,0~10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,0,0,0~1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1&defc=50&reel_set1=4,11,4,4,9,9,8,8,11,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,9,5,4,6,11,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,12~5,5,8,8,11,6,6,10,10,9,9,4,4,12,3,3,11,5,5,10,10,4,7,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,1,11,9,9,10,10,4,4,9,9,11,8,8,10,10,12,6~11,11,4,4,7,9,9,10,10,4,4,3,3,6,6,4,4,9,9,10,10,8,8,11,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,8,8,3,3,5,5,4,7,8,8,12~4,8,8,10,4,10,8,11,5,5,3,3,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,10,4,10,8,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,11,10,10,4,4,9,9,11,5,5,12,6~9,10,10,4,4,9,9,8,8,4,4,5,5,9,9,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,9,9,10,4,10,8,11,6,6,8,8,9,9,11,6,6,8,8,10,4,10,8,11,6,6,12~9,9,8,8,10,10,5,5,4,7,11,10,10,9,9,1,6,6,11,9,9,10,10,4,4,12,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,11,9,9,4,7,8,8,10,4,10,8,11,12&rid=1930955492453806080&bonuses=0&l=20&ver=3&def_sa=4,7,5,11,3,9&sc=10.00,20.00,30.00,34.00,40.00,50.00,59.00,60.00,68.00,70.00,80.00,90.00,100.00,102.00,108.00,118.00,136.00,170.00,177.00,204.00,206.00,216.00,236.00,238.00,272.00,295.00,306.00,324.00,340.00,354.00,412.00,413.00,432.00,472.00,500.00,531.00,540.00,590.00,618.00,648.00,756.00,824.00,864.00,972.00,1000.00,1030.00,1080.00,1236.00,1442.00,1500.00,1648.00,1854.00,2000.00,2060.00,2500.00,3000.00,3500.00,4000.00,4500.00,5000.00&purInit_e=1,1&sh=5&wilds=2~0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0~1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1&total_bet_max=50000000&reel_set3=11,11,4,4,9,9,8,8,11,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,9,5,4,6,11,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,12,6~4,5,5,8,8,11,6,6,10,10,9,9,4,4,12,3,3,11,5,5,10,10,4,7,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,1,11,9,9,10,10,4,4,9,9,11,8,8,10,10,12~11,11,4,4,7,9,9,10,10,4,4,3,3,6,6,4,4,9,9,10,10,8,8,11,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,8,8,3,3,5,5,4,7,8,8,12,6~4,8,8,10,4,10,8,11,5,5,3,3,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,10,4,10,8,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,11,10,10,4,4,9,9,11,5,5,12,6~10,10,4,4,9,9,8,8,4,4,5,5,9,9,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,9,9,10,4,10,8,11,6,6,8,8,9,9,11,6,6,8,8,10,4,10,8,11,6,6,12~6,9,9,8,8,10,10,5,5,4,7,11,10,10,9,9,1,6,6,11,9,9,10,10,4,4,12,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,11,9,9,4,7,8,8,10,4,10,8,11,12,6&def_s=4,10,9,7,3,8,6,11,6,5,6,11,4,10,10,3,3,8,5,9,6,9,6,11,3,10,8,10,3,8&def_sb=5,7,11,9,3,11&na=s&reel_set0=4,11,4,4,9,9,11,8,8,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,11,6,6,9,9,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,10,10,4,7,7,7~5,5,8,8,11,6,6,10,10,9,9,4,4,1,3,3,11,5,5,4,7,10,10,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,10,10,9,9,11,4,4,9,9,11,1,8,8,10,10,4,7,11,11,11~11,11,4,4,7,9,9,10,10,4,4,11,6,6,4,4,9,9,11,8,8,10,10,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,3,3,8,8,5,5,4,7,8,8,10,10,3,3,3~10,4,8,8,10,4,10,8,11,5,5,10,10,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,3,3,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,10,4,10,8,11,4,4,9,9,11,5,5,6,6,11,11,11,6~10,10,4,4,9,9,8,8,4,4,9,9,5,5,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,10,10,9,9,11,9,5,4,6,8,8,11,6,6,8,8,10,4,10,8,11,6,6,5,5,9,9,9~9,9,8,8,10,4,10,8,11,4,7,9,9,10,10,9,5,4,6,11,9,9,10,10,4,4,4,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,1,11,5,5,4,7,8,8,10,4,10,8,11,6,6,4,4,4,7,7&counter=2&wl_i=tbm~25000,tbm_a~20000&small_game_type=0&reel_set4=4,11,4,4,9,9,11,8,8,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,3,3,11,5,5,4,7,11,6,6,9,9,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,10,10,4,7,7,7~5,5,8,8,11,6,6,10,10,9,9,4,4,4,3,3,11,5,5,4,7,10,10,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,8,10,10,9,9,11,4,4,9,9,11,8,8,10,10,4,7,11,11,11~11,11,4,4,7,9,9,10,10,4,4,11,6,6,4,4,9,9,11,8,8,10,10,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,10,9,5,4,6,3,3,8,8,5,5,4,7,8,8,10,10,3,3,3~10,4,8,8,10,4,10,8,11,5,5,10,10,9,5,4,6,10,10,4,4,7,11,6,6,9,9,3,3,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,9,10,4,10,8,11,4,4,9,9,11,5,5,6,6,11,11,11,6~10,10,4,4,9,9,8,8,4,4,9,9,5,5,8,8,11,4,7,9,9,3,3,11,10,10,9,9,5,5,4,7,7,7,10,10,9,9,11,11,11,9,5,4,6,8,8,11,6,6,8,8,10,4,10,8,11,6,6,5,5,9,9,9~9,9,8,8,10,4,10,8,11,4,7,9,9,10,10,9,5,4,6,11,9,9,10,10,4,4,4,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,6,11,5,5,4,7,8,8,10,4,10,8,11,6,6,4,4,4,7,7&balance_bonus=0.00&rt=d&stime=1749210639478&sb=5,10,11,8,1,7&purInit=[{\"bet\":2000,\"type\":\"default\"},{\"bet\":10000,\"type\":\"default\"}]&bl=0&gmb=0,0,0&sa=8,3,4,3,11,3&reel_set_size=5&total_bet_min=0.01&tw=0&game_multiple=10000&bls=20,25&balance=80000.00&cfgs=1&balance_cash=80000.00&c=50&sw=6&sver=5&reel_set2=4,11,4,4,9,9,11,8,8,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,11,6,6,9,9,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,10,10,4,7,7,7,6~7,5,5,8,8,11,6,6,10,10,9,9,4,4,1,3,3,11,5,5,4,7,10,10,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,10,10,9,9,11,4,4,9,9,11,1,8,8,10,10,4,7,11,11,11~4,11,4,4,7,9,9,10,10,4,4,11,6,6,4,4,9,9,11,8,8,10,10,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,3,3,8,8,5,5,4,7,8,8,10,10,3,3,3,1,6~8,8,10,4,10,8,11,5,5,10,10,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,3,3,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,10,4,10,8,11,4,4,9,9,11,5,5,6,6,9,9,9~4,10,10,4,4,9,9,8,8,4,4,9,9,5,5,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,10,10,9,9,11,9,5,4,6,8,8,11,6,6,8,8,10,4,10,8,11,6,6,5,5,1~9,9,8,8,10,4,10,8,11,4,7,9,9,10,10,9,5,4,6,11,9,9,10,10,4,4,4,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,1,11,5,5,4,7,8,8,10,4,10,8,11,6,6,4,4,4,6&index=1&reel_set=0&gameInfo={\"rtps\":{\"ante\":\"96.50\",\"purchase_1\":\"96.55\",\"purchase_0\":\"96.52\",\"regular\":\"96.53\"},\"props\":{\"max_rnd_sim\":\"1\",\"max_rnd_hr\":\"71428571\",\"max_rnd_win\":\"25000\",\"max_rnd_win_a\":\"20000\",\"max_rnd_hr_a\":\"47619048\"}}&ntp=0.00&paytable=0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,500,500,200,200,0,0,0,0,0,0,0;500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,200,200,50,50,0,0,0,0,0,0,0;300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,100,100,40,40,0,0,0,0,0,0,0;240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,40,40,30,30,0,0,0,0,0,0,0;200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,30,30,20,20,0,0,0,0,0,0,0;160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,24,24,16,16,0,0,0,0,0,0,0;100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,20,20,10,10,0,0,0,0,0,0,0;80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,18,18,8,8,0,0,0,0,0,0,0;40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,15,15,5,5,0,0,0,0,0,0,0;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0&s=4,10,9,7,3,8,6,11,6,5,6,11,4,10,10,3,3,8,5,9,6,9,6,11,3,10,8,10,3,8&"))
		// HandleGameInit(w, r)
		data := `total_bet_max=50000000&index=1&def_sb=5,7,11,9,3,11&stime=1750131820760&sa=8,3,4,3,11,3&sb=5,10,11,8,1,7&purInit_e=1,1&l=20&reel_set4=4,11,4,4,9,9,11,8,8,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,3,3,11,5,5,4,7,11,6,6,9,9,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,10,10,4,7,7,7~5,5,8,8,11,6,6,10,10,9,9,4,4,4,3,3,11,5,5,4,7,10,10,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,8,10,10,9,9,11,4,4,9,9,11,8,8,10,10,4,7,11,11,11~11,11,4,4,7,9,9,10,10,4,4,11,6,6,4,4,9,9,11,8,8,10,10,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,10,9,5,4,6,3,3,8,8,5,5,4,7,8,8,10,10,3,3,3~10,4,8,8,10,4,10,8,11,5,5,10,10,9,5,4,6,10,10,4,4,7,11,6,6,9,9,3,3,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,9,10,4,10,8,11,4,4,9,9,11,5,5,6,6,11,11,11,6~10,10,4,4,9,9,8,8,4,4,9,9,5,5,8,8,11,4,7,9,9,3,3,11,10,10,9,9,5,5,4,7,7,7,10,10,9,9,11,11,11,9,5,4,6,8,8,11,6,6,8,8,10,4,10,8,11,6,6,5,5,9,9,9~9,9,8,8,10,4,10,8,11,4,7,9,9,10,10,9,5,4,6,11,9,9,10,10,4,4,4,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,6,11,5,5,4,7,8,8,10,4,10,8,11,6,6,4,4,4,7,7&balance_bonus=0.00&wilds=2~0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0~1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1&purInit=[{"bet":2000,"type":"default"},{"bet":10000,"type":"default"}]&total_bet_min=0.01&reel_set2=4,11,4,4,9,9,11,8,8,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,11,6,6,9,9,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,10,10,4,7,7,7,6~7,5,5,8,8,11,6,6,10,10,9,9,4,4,1,3,3,11,5,5,4,7,10,10,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,10,10,9,9,11,4,4,9,9,11,1,8,8,10,10,4,7,11,11,11~4,11,4,4,7,9,9,10,10,4,4,11,6,6,4,4,9,9,11,8,8,10,10,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,3,3,8,8,5,5,4,7,8,8,10,10,3,3,3,1,6~8,8,10,4,10,8,11,5,5,10,10,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,3,3,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,10,4,10,8,11,4,4,9,9,11,5,5,6,6,9,9,9~4,10,10,4,4,9,9,8,8,4,4,9,9,5,5,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,10,10,9,9,11,9,5,4,6,8,8,11,6,6,8,8,10,4,10,8,11,6,6,5,5,1~9,9,8,8,10,4,10,8,11,4,7,9,9,10,10,9,5,4,6,11,9,9,10,10,4,4,4,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,1,11,5,5,4,7,8,8,10,4,10,8,11,6,6,4,4,4,6&reel_set_size=5&wl_i=tbm~25000;tbm_a~20000&defc=50&bonuses=0&tw=0&game_multiple=10000&def_s=4,10,9,7,3,8,6,11,6,5,6,11,4,10,10,3,3,8,5,9,6,9,6,11,3,10,8,10,3,8&reel_set=0&gameInfo={rtps:{ante:"96.50",purchase_1:"96.55",purchase_0:"96.52",regular:"96.53"},props:{max_rnd_sim:"1",max_rnd_hr:"71428571",max_rnd_win:"25000",max_rnd_win_a:"20000",max_rnd_hr_a:"47619048"}}&sw=6&small_game_type=0&bls=20,25&cfgs=1&rt=d&reel_set1=4,11,4,4,9,9,8,8,11,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,9,5,4,6,11,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,12~5,5,8,8,11,6,6,10,10,9,9,4,4,12,3,3,11,5,5,10,10,4,7,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,1,11,9,9,10,10,4,4,9,9,11,8,8,10,10,12,6~11,11,4,4,7,9,9,10,10,4,4,3,3,6,6,4,4,9,9,10,10,8,8,11,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,8,8,3,3,5,5,4,7,8,8,12~4,8,8,10,4,10,8,11,5,5,3,3,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,10,4,10,8,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,11,10,10,4,4,9,9,11,5,5,12,6~9,10,10,4,4,9,9,8,8,4,4,5,5,9,9,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,9,9,10,4,10,8,11,6,6,8,8,9,9,11,6,6,8,8,10,4,10,8,11,6,6,12~9,9,8,8,10,10,5,5,4,7,11,10,10,9,9,1,6,6,11,9,9,10,10,4,4,12,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,11,9,9,4,7,8,8,10,4,10,8,11,12&c=50&sver=5&counter=2&ntp=0.00&na=s&scatters=1~2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,2000,100,60,0,0,0~10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,0,0,0~1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1&paytable=0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,1000,500,500,200,200,0,0,0,0,0,0,0;500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,500,200,200,50,50,0,0,0,0,0,0,0;300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,300,100,100,40,40,0,0,0,0,0,0,0;240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,240,40,40,30,30,0,0,0,0,0,0,0;200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,200,30,30,20,20,0,0,0,0,0,0,0;160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,160,24,24,16,16,0,0,0,0,0,0,0;100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,20,20,10,10,0,0,0,0,0,0,0;80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,18,18,8,8,0,0,0,0,0,0,0;40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,40,15,15,5,5,0,0,0,0,0,0,0;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0&reel_set3=11,11,4,4,9,9,8,8,11,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,9,5,4,6,11,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,12,6~4,5,5,8,8,11,6,6,10,10,9,9,4,4,12,3,3,11,5,5,10,10,4,7,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,1,11,9,9,10,10,4,4,9,9,11,8,8,10,10,12~11,11,4,4,7,9,9,10,10,4,4,3,3,6,6,4,4,9,9,10,10,8,8,11,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,8,8,3,3,5,5,4,7,8,8,12,6~4,8,8,10,4,10,8,11,5,5,3,3,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,10,4,10,8,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,11,10,10,4,4,9,9,11,5,5,12,6~10,10,4,4,9,9,8,8,4,4,5,5,9,9,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,9,9,10,4,10,8,11,6,6,8,8,9,9,11,6,6,8,8,10,4,10,8,11,6,6,12~6,9,9,8,8,10,10,5,5,4,7,11,10,10,9,9,1,6,6,11,9,9,10,10,4,4,12,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,11,9,9,4,7,8,8,10,4,10,8,11,12,6&gmb=0,0,0` +
			`&balance=` + strconv.Itoa(int(balance)) +
			`&ver=3&s=4,10,9,7,3,8,6,11,6,5,6,11,4,10,10,3,3,8,5,9,6,9,6,11,3,10,8,10,3,8&balance_cash=` + strconv.Itoa(int(balance)) +
			`&def_sa=4,7,5,11,3,9&sc=` + strings.Join(valueList, ",") +
			`&sh=5&bl=0&reel_set0=4,11,4,4,9,9,11,8,8,10,10,5,5,8,8,11,4,7,9,9,10,10,4,4,1,3,3,11,5,5,4,7,11,6,6,9,9,10,10,9,5,4,6,8,8,10,4,10,8,11,6,6,10,10,10,8,8,10,10,4,7,7,7~5,5,8,8,11,6,6,10,10,9,9,4,4,1,3,3,11,5,5,4,7,10,10,9,5,4,6,10,10,8,8,11,4,7,9,5,4,6,8,8,10,10,9,9,11,4,4,9,9,11,1,8,8,10,10,4,7,11,11,11~11,11,4,4,7,9,9,10,10,4,4,11,6,6,4,4,9,9,11,8,8,10,10,1,8,8,10,4,10,8,11,6,6,4,7,7,7,5,5,8,8,11,10,10,9,5,4,6,3,3,8,8,5,5,4,7,8,8,10,10,3,3,3~10,4,8,8,10,4,10,8,11,5,5,10,10,9,5,4,6,10,10,4,4,7,11,6,6,9,9,1,3,3,11,3,3,6,6,10,10,10,8,8,11,5,5,4,7,9,9,10,4,10,8,11,4,4,9,9,11,5,5,6,6,11,11,11,6~10,10,4,4,9,9,8,8,4,4,9,9,5,5,8,8,11,4,7,9,9,3,3,11,1,10,10,9,9,5,5,4,7,7,7,10,10,9,9,11,9,5,4,6,8,8,11,6,6,8,8,10,4,10,8,11,6,6,5,5,9,9,9~9,9,8,8,10,4,10,8,11,4,7,9,9,10,10,9,5,4,6,11,9,9,10,10,4,4,4,3,3,9,9,5,5,8,8,11,6,6,4,4,9,9,8,8,6,6,1,11,5,5,4,7,8,8,10,4,10,8,11,6,6,4,4,4,7,7&rid=1934819206789623808&`
		// data := ctx["data"].([][]byte)[index-1]
		// delete(ctx, "spinID")

		// w.Header().Set("Content-Type", "text/html;charset=UTF-8")
		// w.WriteHeader(http.StatusOK)
		// w.Write([]byte(data))
		ctx.Data(http.StatusOK, "text/html;charset=UTF-8", []byte(data))
	} else if action == "doSpin" {
		c, _ := strconv.ParseFloat(ctx.Request.FormValue("c"), 64)
		line := igame.Line_(token.GameID)
		var input = c * float64(line)
		var mode int32 = 0
		resp, err := message.Request[pb.GameSpinResp](m.GetHistoryServerId(), &pb.GamePGSpinReq{
			UserID:   bc.ID,
			GameID:   token.GameID,
			Currency: token.AssetID,
			DeskID:   token.DeskID,
			Gamble:   c,
			Lv:       1,
			Input:    input,
			Mode:     mode,
		})
		if err != nil {
			log.Error(e)
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
		if resp.Code != pb.SUCCESS {
			log.Error(resp)
			ctx.JSON(http.StatusOK, pb.Error{Code: resp.Code})
			return
		}
		// var detail []byte
		// var res1 = map[string]any{}
		// err = json.Unmarshal([]byte(resp.Detail), &res1)
		// if err != nil {
		// 	var res2 = []map[string]any{}
		// 	err = json.Unmarshal([]byte(resp.Detail), &res2)
		// 	if err != nil {
		// 		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		// 		return
		// 	} else {
		// 		res2[0]["JackpotInfo"] = resp.JackpotInfo
		// 		res2[0]["Policy"] = resp.Policy
		// 		detail, _ = json.Marshal(res2)
		// 	}
		// } else {
		// 	res1["JackpotInfo"] = resp.JackpotInfo
		// 	res1["Policy"] = resp.Policy
		// 	detail, _ = json.Marshal(res1)
		// }
		// s, _ := CompressJSON(detail)
		// ctx.String(http.StatusOK, s)
		ctx.Data(http.StatusOK, "text/html;charset=UTF-8", []byte(resp.Detail))
	}

}

func (m *module) OnHandleXPPPlayGame(ctx *gin.Context) {
	b1 := `{
		"error": 0,
		"description": "OK",
		"currency": "",
		"currencyOriginal": "",
		"gameName": "vs20fruitswx",
		"mgckey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AADKhln1TgNMyK-sd0T8IXsvatRSbo9WZxh74GexmS8",
		"jurisdictionRequirements": "",
		"amountType": "COIN",
		"brandRequirements": "NOCUR_CBW~!R$,VAPP",
		"gameConfig": {
			"LOGOUT": "https://api.panlaxy.io/xgame/gs2c/logout.do",
			"region": "Asia",
			"sessionTimeout": "-1"
		},
		"mobileLobbyUrl": "",
		"lobbyUrl": ""
		}`
	ctx.Data(http.StatusOK, "application/json; charset=utf-8", []byte(b1))
	if true {
		return
	}
	err := ctx.Request.ParseForm()
	if err != nil {
		ctx.String(http.StatusBadRequest, "Failed to parse form data")
		return
	}
	otk := ctx.Request.FormValue("otk")
	token, err := ParamToken(otk)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	_, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	gameinfo := table.Get[gsconf.GameInfoConf](token.GameID)
	if gameinfo == nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	if !gameinfo.Open {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_MAINTENANCE})
		return
	}
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == token.AssetID && iic.GameID == token.GameID
	})
	if inputInfo == nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	LOGOUT := "\"https://api.panlaxy.io/xgame/gs2c/logout.do\""
	gameName := "\"vs20sugarrushx\""
	gameName = "\"vs20fruitswx\""
	b := `{
			"error": 0,
			"description": "OK",
			"currency": "",
			"currencyOriginal": "",
			"gameName": ` + gameName + `,
			"mgckey": "` + otk + `",
			"jurisdictionRequirements": "",
			"amountType": "COIN",
			"brandRequirements": "NOCUR_CBW~!R$,VAPP",
			"gameConfig": {
			"LOGOUT": ` + LOGOUT + `,
			"region": "Asia",
			"sessionTimeout": "-1"
			},
			"mobileLobbyUrl": "",
			"lobbyUrl": ""
		}`
	// s, _ := CompressJSON([]byte(b))
	ctx.Data(http.StatusOK, "application/json; charset=utf-8", []byte(b))
}

func (m *module) OnHandleXPPStats(ctx *gin.Context) {
	//		b := `{
	//		"error": 0,
	//		"description": "OK"
	//	}`
	//
	//	ctx.Data(http.StatusOK, "application/json; charset=utf-8", []byte(b))
}

func (m *module) OnHandleXPPHistorySettings(ctx *gin.Context) {
	b := `{
  "brandRequirements": [
    "NOCUR_CBW~!R$",
    "VAPP"
  ],
  "jurisdiction": "99",
  "jurisdictionRequirements": [],
  "language": "en"
}`
	ctx.Data(http.StatusOK, "application/json; charset=utf-8", []byte(b))
}
func (m *module) OnHandleXPPHistory(ctx *gin.Context) {
	b := `[
  {
    "roundId": "1930941176803520512",
    "balance": "78000.00",
    "bet": "1000.00",
    "currency": "",
    "currencySymbol": "",
    "dateTime": 1749207226000,
    "hash": "ec20c70a033b4b753fcfa3e48fe600ca",
    "roundDetails": "",
    "win": "0.00"
  },
  {
    "roundId": "1930940980296183808",
    "balance": "79000.00",
    "bet": "1000.00",
    "currency": "",
    "currencySymbol": "",
    "dateTime": 1749207179000,
    "hash": "ec20c70a033b4b753fcfa3e48fe600ca",
    "roundDetails": "",
    "win": "0.00"
  }
]`
	ctx.Data(http.StatusOK, "application/json; charset=utf-8", []byte(b))

}

func (m *module) OnHandleXPPSettings(ctx *gin.Context) {
	err := ctx.Request.ParseForm()
	if err != nil {
		ctx.String(http.StatusBadRequest, "Failed to parse form data")
		return
	}
	// settings := r.FormValue("settings")
	b := `SoundState=true_true_true_false_false;FastPlay=false;Intro=true;StopMsg=0;TurboSpinMsg=0;BetInfo=0_0;BatterySaver=false;ShowCCH=false;ShowFPH=true;CustomGameStoredData=;Coins=false;Volume=1;InitialScreen=7,8,8,5,8,8,5_3,4,7,6,4,7,6_9,5,9,5,5,9,5_6,6,5,3,6,5,3_9,7,3,9,7,3,9_3,8,6,6,8,6,6_5,9,6,9,9,6,9;SBPLock=true`
	// w.Header().Set("Content-Type", "text/html;charset=UTF-8")
	// w.Header().Set("nel", `{"success_fraction":0,"report_to":"cf-nel","max_age":604800}`)
	// w.WriteHeader(http.StatusOK)
	// w.Write([]byte(b))
	ctx.Data(http.StatusOK, "text/html;charset=UTF-8", []byte(b))
}
func (m *module) OnHandleXPPUnread(ctx *gin.Context) {
	b := `{
			"error": 0,
			"description": "OK",
			"announcements": []
		}`
	ctx.Data(http.StatusOK, "application/json; charset=utf-8", []byte(b))
}

func (m *module) OnHandleXPPGames(ctx *gin.Context) {
	root := "https://statics.pg.panlaxy.io"
	b := `{
	"activePromos": {
		"races": [],
		"tournaments": []
	},
	"description": "OK",
	"error": 0,
	"gameIconsURL": "` + root + `/gs2c/lobby/icons",
	"gameLaunchURL": "` + root + `/gs2c/html5Game.html?sip=localhost:9528&gi=20005&ot=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************.fPTBcv9QVcYy9bVXWqWdVFPNxFVnt4IIouZBAveyLPY&cid=0&ops=luck_single_10013&l=zh&oc=0&n=20005&ct=BRL",
	"lobbyCategories": [
		{
		"categorySymbol": "all",
		"categoryName": "All games",
		"lobbyGames": [
			{
			"gi": "20001",
			"name": "Gates of Olympus",
			"symbol": "vs20olympgate"
			}
		]
		},
		{
		"categorySymbol": "hot",
		"categoryName": "Hot games",
		"lobbyGames": [
			{
			"gi": "20001",
			"name": "Gates of Olympus",
			"symbol": "vs20olympgate"
			}
		]
		}
	]
	}`
	ctx.String(http.StatusOK, b)
}

func (m *module) OnHandleXPPActive(ctx *gin.Context) {
	// w.Header().Set("Content-Type", "application/json")
	// w.WriteHeader(http.StatusOK)
	// w.Write([]byte(""))
	ctx.JSON(http.StatusOK, "{}")
}
func (m *module) OnHandleXPPPrizes(ctx *gin.Context) {
	// w.Header().Set("Content-Type", "application/json")
	// w.WriteHeader(http.StatusOK)
	// w.Write([]byte(""))
	ctx.JSON(http.StatusOK, "{}")
}
func (m *module) OnHandleXPPRaceDetails(ctx *gin.Context) {
	// w.Header().Set("Content-Type", "application/json")
	// w.WriteHeader(http.StatusOK)
	// w.Write([]byte(""))
	ctx.JSON(http.StatusOK, "{}")
}
func (m *module) OnHandleXPPTournamentDetails(ctx *gin.Context) {
	// w.Header().Set("Content-Type", "application/json")
	// w.WriteHeader(http.StatusOK)
	// w.Write([]byte(""))
	ctx.JSON(http.StatusOK, "{}")
}
