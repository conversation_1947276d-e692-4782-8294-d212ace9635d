package gamedoor

import (
	"encoding/json"
	"igame"
	"net/http"
	"s2/common/cache"
	"s2/gsconf"
	"s2/pb"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (m *module) OnSpinXBTAGame(ctx *gin.Context) {
	type Request struct {
		Keepalive           bool   `json:"keepalive,omitempty"`
		Q                   string `json:"q,omitempty"`
		BetPerLine          int64  `json:"betPerLine,omitempty"` // 投注额
		Nlines              int64  `json:"nlines,omitempty"`     // 线数
		Denom               int64  `json:"denom,omitempty"`
		BuyBonus            any    `json:"buyBonus,omitempty"`
		SelectID            any    `json:"selectId,omitempty"`
		HideInsideInHistory int64  `json:"hideInsideInHistory,omitempty"`
		ShowingInMoney      int64  `json:"showingInMoney,omitempty"`
		VipOn               int64  `json:"vipOn,omitempty"`
		C                   int64  `json:"c,omitempty"`
		GhistId             string `json:"ghist_id,omitempty"`
		SID                 string `json:"sid,omitempty"`
		CurFloor            int64  `json:"curFloor,omitempty"`
		UserAgent           string `json:"userAgent,omitempty"`
		UserAction          string `json:"userAction,omitempty"`
		Setting             string `json:"setting,omitempty"`
	}
	var req Request
	if err := ctx.BindJSON(&req); err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}

	token, err := ParamToken(req.SID)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}

	bc, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	var response []byte
	var data interface{}

	assetResp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: bc.ID,
	})
	if err != nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}

	// 正常游戏
	// spinReq := &pb.GamePGSpinReq{}
	spinReq := &pb.GameBTASpinReq{}
	// 获取用户设置
	settingResp, err := message.Request[pb.GetUserSettingResp](m.GetServerId(bc), &pb.GetUserSettingReq{
		UserID: bc.ID,
		GameID: token.GameID,
	})
	if err != nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	setting := map[string]any{}
	json.Unmarshal([]byte(settingResp.Data), &setting)
	if setting["language"] == nil {
		setting["language"] = "en"
	}
	if setting["smallGameType"] == nil {
		setting["smallGameType"] = "off"
	}

	// 小游戏选项
	if req.Setting != "" {
		if strings.Contains(req.Setting, "dealer") {
			spinReq.SmallGameType = "dealer"
			data = map[string]any{
				"jsonVars": map[string]any{
					"doublePrefer": "dealer",
				},
			}
		} else if strings.Contains(req.Setting, "redblack") {
			spinReq.SmallGameType = "redblack"
			data = map[string]any{
				"jsonVars": map[string]any{
					"doublePrefer": "redblack",
				},
			}
		} else if strings.Contains(req.Setting, "off") {
			spinReq.SmallGameType = "off"
			data = map[string]any{
				"jsonVars": map[string]any{
					"doublePrefer": "off",
				},
			}
		}

		// 语言选项
		if strings.Contains(req.Setting, "language") {
			languageMap := map[string]any{}
			json.Unmarshal([]byte(req.Setting), &languageMap)
			if v, ok := languageMap["language"]; ok {
				spinReq.Language = v.(string)
			}
			data = map[string]any{
				"jsonVars": map[string]any{
					"language": spinReq.Language,
				},
			}
		}

		setting, err := message.Request[pb.GameBTAUserSettingResp](m.GetServerId(bc), &pb.GameBTAUserSettingReq{
			UserID:        bc.ID,
			GameID:        token.GameID,
			Language:      spinReq.Language,
			SmallGameType: spinReq.SmallGameType,
		})
		if err != nil {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
		if setting.Code != pb.SUCCESS {
			ctx.JSON(http.StatusOK, pb.Error{Code: setting.Code})
			return
		}

		response = func() []byte {
			b, _ := json.Marshal(data)
			s, _ := CompressJSON(b)
			return []byte(s)
		}()
	}

	switch req.Q {
	case "enter":
		gameinfo := table.Get[gsconf.GameInfoConf](token.GameID)
		if gameinfo == nil {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
		if !gameinfo.Open {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_MAINTENANCE})
			return
		}
		inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
			return iic.CurrencyID == token.AssetID && iic.GameID == token.GameID
		})
		if inputInfo == nil {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}

		balance := assetResp.Balance[token.AssetID]
		// line := igame.Line_(token.GameID)
		rule := igame.SpinRule_(token.GameID)
		ruleMap := map[string]any{}
		json.Unmarshal([]byte(rule), &ruleMap)
		data = map[string]any{
			"useracc": map[string]any{
				"amount":       balance,
				"currency":     "",
				"altcurr":      "",
				"currencyUnit": 1,
				"symbol_first": "0",
				"currencyType": 2,
			},
			"gs": ruleMap,
			"uservars": map[string]any{
				"ip":           ctx.ClientIP(),
				"language":     setting["language"],
				"doublePrefer": setting["smallGameType"],
			},
		}
		response = func() []byte {
			b, _ := json.Marshal(data)
			s, _ := CompressJSON(b)
			return []byte(s)
		}()
	case "start":
		line := igame.Line_(token.GameID)
		var input float64 = float64(req.BetPerLine) * float64(line)
		var mode int32 = 0
		if req.SelectID != nil {
			mode = int32(req.SelectID.(float64)) + 1
		}

		switch token.GameID {
		case 400163:
			if req.VipOn == 1 {
				mode = 4
			}
		case 400160:
			if req.VipOn == 1 {
				mode = 5
			}
		case 400128:
			if req.BuyBonus != nil && req.BuyBonus.(float64) == 1 {
				mode = 1
			}
		case 400122:
			if req.VipOn == 1 {
				mode = 4
			}
		case 400154:
			if req.VipOn == 1 {
				mode = 5
			}
		}

		spinReq.UserID = bc.ID
		spinReq.GameID = token.GameID
		spinReq.Currency = token.AssetID
		spinReq.DeskID = token.DeskID
		spinReq.Gamble = float64(req.BetPerLine)
		spinReq.Input = input
		spinReq.Mode = mode
		spinReq.Language = setting["language"].(string)
		if token.GameID == 400009 {
			spinReq.SmallGameType = setting["smallGameType"].(string)
		}
		spinReq.Step = "start"
		resp, err := message.Request[pb.GameSpinResp](m.GetServerId(bc), spinReq)
		if err != nil {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
		if resp.Code != pb.SUCCESS {
			ctx.JSON(http.StatusOK, pb.Error{Code: resp.Code})
			return
		}
		response = func() []byte {
			s, _ := CompressJSON([]byte(resp.Detail))
			return []byte(s)
		}()
	case "play":
		switch token.GameID {
		case 400009:
			// askDouble, askDoubleRed, askDoubleBlack, askDoubleSpade, askDoubleClub, askDoubleHeart, askDoubleDiamond
			smallGameReq := &pb.SmallGameBtaReq{
				UserID: bc.ID,
				GameID: token.GameID,
			}
			switch req.UserAction {
			case "askDouble":
				smallGameReq.Choice = "askDouble"
			case "askDoubleBackToGame":
				smallGameReq.Choice = "askDoubleBackToGame"
			case "askDouble1":
				smallGameReq.Choice = "1"
			case "askDouble2":
				smallGameReq.Choice = "2"
			case "askDouble3":
				smallGameReq.Choice = "3"
			case "askDouble4":
				smallGameReq.Choice = "4"
			case "askDoubleRed":
				smallGameReq.Choice = "Red"
			case "askDoubleBlack":
				smallGameReq.Choice = "Black"
			case "askDoubleSpade":
				smallGameReq.Choice = "Spade"
			case "askDoubleClub":
				smallGameReq.Choice = "Club"
			case "askDoubleHeart":
				smallGameReq.Choice = "Heart"
			case "askDoubleDiamond":
				smallGameReq.Choice = "Diamond"
			}

			smallGameReq.SmallGameType = setting["smallGameType"].(string)
			// 发起小游戏请求并存入 ctx
			smallGameResp, _ := message.Request[pb.SmallGameBtaResp](m.GetServerId(bc), smallGameReq)
			if smallGameResp.Code != pb.SUCCESS {
				ctx.JSON(http.StatusOK, pb.Error{Code: smallGameResp.Code})
				return
			}
			response = func() []byte {
				var res = map[string]any{}
				json.Unmarshal([]byte(smallGameResp.Detail), &res)
				s, _ := CompressJSON([]byte(res["result"].(string)))
				return []byte(s)
			}()
		}
	case "finish":
		finishResp, _ := message.Request[pb.GetSmallGameBtaFinishResp](m.GetServerId(bc), &pb.GetSmallGameBtaFinishReq{
			UserID:   bc.ID,
			GameID:   token.GameID,
			Currency: token.AssetID,
		})
		if finishResp.Code != pb.SUCCESS {
			ctx.JSON(http.StatusOK, pb.Error{Code: finishResp.Code})
			return
		}
		response = func() []byte {
			s, _ := CompressJSON([]byte(finishResp.Data))
			return []byte(s)
		}()
	default:
		if response == nil {
			response = func() []byte {
				s, _ := CompressJSON([]byte(`{"useracc":{"jpsInfo":[]}}`))
				return []byte(s)
			}()
		}
	}
	ctx.String(http.StatusOK, string(response))
}
