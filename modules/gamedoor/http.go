package gamedoor

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/binary"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"s2/common/cache"
	"s2/pb"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/message/codec"
)

var (
	speedLimit     = map[string]int32{}
	lastReqTime    = map[string]int64{}
	speedLimitLock = sync.Mutex{}
	encryptKey     = []byte(conf.Str("message.encryptKey", ""))
	encryptIV      = []byte(conf.Str("message.encryptIV", ""))
)

func (m *module) defaultHandler(ctx *gin.Context) {
	b, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.Error(err)
		ctx.String(http.StatusBadRequest, "read body failed")
		return
	}
	encrypt := ctx.Request.Header.Get("encrypt") != ""
	if encrypt {
		b, err = decryptAES(b)
		if err != nil {
			log.Error(err)
			ctx.String(http.StatusBadRequest, "decrypt error")
			return
		}
	}
	var req = struct {
		Cmd  string
		Data string
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Error(err)
		ctx.String(http.StatusBadRequest, "bad request")
		return
	}
	isLimit := checkSpeedLimit(ctx)
	if isLimit {
		ctx.String(http.StatusBadRequest, "")
		log.Warnf("checkSpeedLimit %s", string(b))
		return
	}
	if len(req.Data) == 0 {
		req.Data = "{}"
	}
	since := time.Now()
	var resp *pb.C2SPackageGameResp
	if strings.Contains(req.Cmd, ".") {
		strs := strings.Split(req.Cmd, ".")
		moduleName, do := strs[0], strs[1]
		body := make([]byte, 0, len(req.Data)+codec.DefaultMsgIDLen+1)
		body = binary.LittleEndian.AppendUint32(body, codec.NameToID(do))
		body = append(body, byte(codec.MarshalByJSON))
		body = append(body, []byte(req.Data)...)
		resp, err = message.RequestAny[pb.C2SPackageGameResp](moduleName, &pb.C2SPackageGameReq{
			Body: body,
		})
	} else {
		token := ctx.Request.Header.Get("Token")
		if len(token) == 0 {
			ctx.String(http.StatusBadRequest, "")
			log.Warnf("request token empty, %s", string(b))
			return
		}
		bc, e := cache.QueryUserBasicInfoByToken(token)
		if e != nil {
			log.Error(e, " token:", token)
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
		body := make([]byte, 0, len(req.Data)+codec.DefaultMsgIDLen+1)
		body = binary.LittleEndian.AppendUint32(body, codec.NameToID(req.Cmd))
		body = append(body, byte(codec.MarshalByJSON))
		body = append(body, []byte(req.Data)...)
		resp, err = message.Request[pb.C2SPackageGameResp](m.GetServerId(bc), &pb.C2SPackageGameReq{
			UserID: bc.ID,
			Body:   body,
		})
	}
	if err != nil {
		ctx.String(http.StatusInternalServerError, "")
		log.Error(err)
		return
	}
	latency := time.Since(since)
	if latency > time.Millisecond*50 {
		log.Warnf("latency too high: %s, req: %#v, resp: %#v", latency, req, resp)
	}
	if !encrypt {
		ctx.Data(http.StatusOK, "application/json;", resp.Body)
		return
	}
	b = encryptAES(resp.Body)
	ctx.Data(http.StatusOK, "application/octet-stream", b)
}

func IntFieldValue(a any, field string) (int64, bool) {
	defer func() {
		if r := recover(); r != nil {
			return // res, ok = 0, false
		}
	}()
	v := reflect.ValueOf(a)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	f := v.FieldByName(field)
	n := f.Int()
	return n, true
}

func checkSpeedLimit(ctx *gin.Context) bool {
	addr := ctx.ClientIP()
	const maxCountPerSecond = 100
	now := time.Now().Unix()
	speedLimitLock.Lock()
	defer speedLimitLock.Unlock()
	lastTime, ok := lastReqTime[addr]
	if !ok || lastTime != now {
		lastReqTime[addr] = now
		speedLimit[addr] = 1
		return false
	}
	speedLimit[addr]++
	n := speedLimit[addr]
	return n > maxCountPerSecond
}

func paddingBytes(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padBytes := bytes.Repeat([]byte{byte(padding)}, padding)
	newBytes := append(src, padBytes...)
	return newBytes
}

func unPaddingBytes(src []byte) []byte {
	l := len(src)
	n := int(src[l-1])
	return src[:l-n]
}

func encryptAES(src []byte) []byte {
	block, err := aes.NewCipher(encryptKey)
	if err != nil {
		panic(err)
	}
	src = paddingBytes(src, block.BlockSize())
	cbcDecrypter := cipher.NewCBCEncrypter(block, encryptIV)
	dst := make([]byte, len(src))
	cbcDecrypter.CryptBlocks(dst, src)
	return dst
}

func decryptAES(src []byte) ([]byte, error) {
	block, err := aes.NewCipher(encryptKey)
	if err != nil {
		return nil, err
	}
	cbcDecrypter := cipher.NewCBCDecrypter(block, encryptIV)
	dst := make([]byte, len(src))
	cbcDecrypter.CryptBlocks(dst, src)
	dst = unPaddingBytes(dst)
	return dst, nil
}
