package gamedoor

import (
	"encoding/json"
	"net/http"
	"s2/pb"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (m *module) OnXPGLogV2(ctx *gin.Context) {
	baseCtx := map[string]any{}
	if err := ctx.BindJSON(&baseCtx); err != nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	type GamLogData struct {
		Gameid int32 `json:"gid"`
	}
	var req GamLogData = GamLogData{
		Gameid: int32(baseCtx["gid"].(float64)),
	}
	baseCtx["apiurl"] = ctx.Request.URL.Path
	baseCtx_, _ := json.Marshal(baseCtx)
	resp, err := message.Request[pb.GamePGSpinRecordResp](m.GetHistoryServerId(), &pb.GamePGSpinRecordReq{
		GameID: req.Gameid,
		Ctx:    string(baseCtx_),
	})
	if err != nil {
		log.Error(err)
	}
	ctx.Data(http.StatusOK, "application/json", resp.Data)
}

func (m *module) OnXPGLog(ctx *gin.Context) {
	baseCtx := map[string]any{}
	if err := ctx.BindJSON(&baseCtx); err != nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	type GamLogData struct {
		Gameid    int32 `json:"gameId"`
		UserId    int64 `json:"userId"`
		StartTime int32 `json:"startTime"`
		EndTime   int32 `json:"endTime"`
		Page      int32 `json:"page"`
		PageSize  int32 `json:"pageSize"`
	}
	var req GamLogData = GamLogData{
		Gameid:    int32(baseCtx["gameId"].(float64)),
		UserId:    int64(baseCtx["userId"].(float64)),
		StartTime: int32(baseCtx["startTime"].(float64)),
		EndTime:   int32(baseCtx["endTime"].(float64)),
		Page:      int32(baseCtx["page"].(float64)),
		PageSize:  int32(baseCtx["pageSize"].(float64)),
	}
	baseCtx["apiurl"] = ctx.Request.URL.Path
	baseCtx_, _ := json.Marshal(baseCtx)
	resp, err := message.Request[pb.GamePGSpinRecordResp](m.GetHistoryServerId(), &pb.GamePGSpinRecordReq{
		UserID:    req.UserId,
		Starttime: int64(req.StartTime),
		Endtime:   int64(req.EndTime),
		GameID:    req.Gameid,
		// AssetID:   10000,
		Offset: int32(req.Page),
		Limit:  int32(req.PageSize),
		Ctx:    string(baseCtx_),
	})
	if err != nil {
		log.Error(err)
	}
	ctx.Data(http.StatusOK, "application/json", resp.Data)
}

func (m *module) OnXPGDetails(ctx *gin.Context) {
	baseCtx := map[string]any{}
	if err := ctx.BindJSON(&baseCtx); err != nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	body := struct {
		ID       string `json:"id"`
		CreateAt string `json:"startTime"`
	}{}
	body.ID = baseCtx["id"].(string)
	body.CreateAt = baseCtx["startTime"].(string)
	createAt, err := strconv.Atoi(body.CreateAt)
	if err != nil {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	id, err := strconv.ParseUint(body.ID, 10, 64)
	if err != nil {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	baseCtx["apiurl"] = ctx.Request.URL.Path
	baseCtx_, _ := json.Marshal(baseCtx)
	resp, err := message.Request[pb.GameSpinPGRecordDetailResp](m.GetHistoryServerId(), &pb.GameSpinPGRecordDetailReq{
		ID:       id,
		CreateAt: int64(createAt),
		Ctx:      string(baseCtx_),
	})
	if err != nil {
		log.Error(err)
		ctx.JSON(http.StatusInternalServerError, pb.Error{})
		return
	}
	if resp.Code != pb.SUCCESS {
		log.Error(err)
		ctx.JSON(http.StatusInternalServerError, pb.Error{Code: resp.Code})
		return
	}
	ctx.Data(http.StatusOK, "application/json", []byte(resp.Data))
}
