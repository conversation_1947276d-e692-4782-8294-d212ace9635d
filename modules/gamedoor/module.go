package gamedoor

import (
	"fmt"
	"reflect"
	"s2/common/cache"
	"s2/define"
	"s2/gsconf"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils/hs"
)

type module struct {
	iface.IModule
	*hs.HttpService
}
type GameServerConfig struct {
	GameID           uint32   `yaml:"gameID"`
	ZoneIDs          []uint32 `yaml:"zoneIDs"`
	RouteID          uint32   `yaml:"routeID"`
	HistoryServerID  uint32   `yaml:"historyServerID"`
	WhitelistUids    []int64  `yaml:"whitelistUids"`
	WhitelistRouteID uint32   `yaml:"whitelistRouteID"`
}

var (
	gameServerConfig GameServerConfig
	mu               sync.RWMutex
)

func New() iface.IModule {
	m := &module{
		IModule: basic.NewEventLoop(basic.DefaultMQLen),
	}
	m.HttpService = hs.NewHttpService()
	return m
}

func (m module) Name() string {
	return define.ModuleName.GameDoor
}

func (m *module) Init() error {
	go m.LoopLoadConfig()
	m.POST("/", m.defaultHandler)

	m.initHistory()

	m.InitRounterGame()
	/////////////////pg|king///////////////
	m.POST("/api/retailTrans/GetSlotLog", m.OnXPGLog) //LuckyNeko TreasuresofAztec SpeedWinner GemstonesGold CaptainBounty
	m.POST("/api/retailTrans/GetSlotDetails", m.OnXPGDetails)
	m.POST("/api/retailTrans/GetSlotDetailsWildApe", m.OnXPGDetails) //LuckyNeko TreasuresofAztec SpeedWinner GemstonesGold CaptainBounty
	m.POST("/api/RetailTrans/BetSummary", m.OnXPGLogV2)              //GaneshaGold
	m.POST("/api/RetailTrans/BetHistory", m.OnXPGLogV2)              //GaneshaGold

	///////////////////////////////////////

	///////////////////pp/////////////////
	m.POST("/gs2c/playGame.do", m.OnHandleXPPPlayGame)
	m.POST("/gs2c/stats.do", m.OnHandleXPPStats)
	m.POST("/gs2c/ge/v4/gameService", m.OnHandleXPPGameService)
	m.POST("/gs2c/announcements/unread/", m.OnHandleXPPUnread)
	m.POST("/gs2c/saveSettings.do", m.OnHandleXPPSettings)
	m.POST("/gs2c/minilobby/games", m.OnHandleXPPGames)
	m.POST("/gs2c/promo/active/", m.OnHandleXPPActive)
	m.POST("/gs2c/promo/tournament/details/", m.OnHandleXPPTournamentDetails)
	m.POST("/gs2c/promo/race/details/", m.OnHandleXPPRaceDetails)
	m.POST("/gs2c/promo/race/prizes/", m.OnHandleXPPPrizes)
	m.POST("/api/history/v2/settings/general", m.OnHandleXPPHistorySettings)
	m.POST("/api/history/v2/play-session/last-items", m.OnHandleXPPHistory)
	///////////////////////////////////////

	//////////////////belatra//////////////
	m.POST("/game", m.OnSpinXBTAGame)
	///////////////////////////////////////
	return nil
}

func (m *module) InitRounterGame() {
	allGameInfo := table.GetALL[gsconf.GameInfoConf]()
	rv := reflect.ValueOf(m)
	ctxType := reflect.TypeOf(&gin.Context{})
	for i := 0; i < len(allGameInfo); i++ {
		gameInfo := allGameInfo[i]
		for j := 0; j < len(gameInfo.ListeningAPI); j += 2 {
			apiurl := gameInfo.ListeningAPI[j]
			callbacl := gameInfo.ListeningAPI[j+1]
			method := rv.MethodByName(callbacl)
			if !method.IsValid() {
				log.Warnf("no handler method %q on module", callbacl)
				continue
			}
			mType := method.Type()
			if mType.NumIn() != 1 || mType.In(0) != ctxType {
				log.Warnf("method %q has wrong signature, expecting func(*gin.Context)", callbacl)
				continue
			}
			handler := func(c *gin.Context) {
				method.Call([]reflect.Value{reflect.ValueOf(c)})
			}
			m.POST(apiurl, handler)
		}
	}
}

func (m *module) Run() error {
	return m.ListenAndServe(fmt.Sprintf(":%d", conf.Num[int]("gamedoor.port")))
}

func (m *module) Exit() error {
	m.IModule.Exit()
	return m.Stop(time.Second * 10)
}

func (m *module) LoopLoadConfig() {
	m.loadServerId()
	ticker := time.NewTicker(time.Second * 5)
	for {
		select {
		case <-system.RootCtx().Done():
			return
		case <-ticker.C:
			m.loadServerId()
		}
	}
}

func (m *module) loadServerId() error {
	mu.Lock()
	defer mu.Unlock()
	conf.LoadFromYAML()
	conf.Scan("curGameServer", &gameServerConfig)
	return nil
}

func (m *module) GetServerId(bc *cache.UserBasicInfo) uint32 {
	mu.RLock()
	defer mu.RUnlock()
	gameID := gameServerConfig.GameID
	if bc.ZoneID == nil {
		bc.ZoneID = make(map[uint32]uint32)
	}
	if bc.ZoneID[gameID] == 0 {
		bc.ZoneID[gameID] = gameServerConfig.ZoneIDs[bc.ID%int64(len(gameServerConfig.ZoneIDs))]
		cache.SaveUserBasicInfo(bc)
	}
	zoneId := bc.ZoneID[gameID]
	routeID := gameServerConfig.RouteID
	if len(gameServerConfig.WhitelistUids) > 0 {
		for _, v := range gameServerConfig.WhitelistUids {
			if v == bc.ID {
				gameID = gameServerConfig.WhitelistRouteID
				break
			}
		}
	}
	return conf.GetGameServerId(gameID, uint32(zoneId), uint32(routeID))
}

func (m *module) GetHistoryServerId() uint32 {
	mu.RLock()
	defer mu.RUnlock()
	return gameServerConfig.HistoryServerID
}
