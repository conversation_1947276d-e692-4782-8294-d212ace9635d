package gamedoor

import (
	"encoding/json"
	"net/http"
	"s2/common/cache"
	"s2/gsconf"
	"s2/pb"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (m *module) OnEnterXKingGame(ctx *gin.Context) {
	baseCtx := map[string]any{}
	if err := ctx.BindJSON(&baseCtx); err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	type EnterGameData struct {
		Password string `json:"password"`
	}
	var tokenData = EnterGameData{
		Password: baseCtx["password"].(string),
	}
	token, err := ParamToken(tokenData.Password)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	bc, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	gameinfo := table.Get[gsconf.GameInfoConf](token.GameID)
	if gameinfo == nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	if !gameinfo.Open {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_MAINTENANCE})
		return
	}
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == token.AssetID && iic.GameID == token.GameID
	})
	if inputInfo == nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	baseCtx_, _ := json.Marshal(baseCtx)
	resp, err := message.Request[pb.KingGameEnterGameResp](m.GetServerId(bc), &pb.KingGameEnterGameReq{
		UserID:   bc.ID,
		GameID:   token.GameID,
		Currency: token.AssetID,
		Ctx:      string(baseCtx_),
	})
	if err != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	if resp.Code != pb.SUCCESS {
		log.Error(resp)
		ctx.JSON(http.StatusOK, pb.Error{Code: resp.Code})
		return
	}
	detailMap := map[string]any{}
	json.Unmarshal([]byte(resp.Detail), &detailMap)
	detailMap["data"].(map[string]any)["token"] = tokenData.Password
	detail, _ := json.Marshal(detailMap)
	s, _ := CompressJSON([]byte(detail))
	ctx.String(http.StatusOK, string(s))
}
func (m *module) OnSpinXKingGame(ctx *gin.Context) {
	baseCtx := map[string]any{}
	if err := ctx.BindJSON(&baseCtx); err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	type GambleData struct {
		Gamble    float64 `json:"gamble"`
		Gameid    int32   `json:"gameid"`
		Levelid   int32   `json:"levelid"`
		LineCount int32   `json:"lineCount"`
		Lv        int32   `json:"lv"`
		Tableid   int32   `json:"tableid"`
		Token     string  `json:"token"`
		BBuyFree  bool    `json:"bBuyFree"`
	}
	var tokenData = GambleData{
		Gamble:    float64(baseCtx["gamble"].(float64)),
		Gameid:    int32(baseCtx["gameid"].(float64)),
		Levelid:   int32(baseCtx["levelid"].(float64)),
		LineCount: int32(baseCtx["lineCount"].(float64)),
		Lv:        int32(baseCtx["lv"].(float64)),
		Tableid:   int32(baseCtx["tableid"].(float64)),
		Token:     baseCtx["token"].(string),
	}
	if baseCtx["bBuyFree"] == nil {
		tokenData.BBuyFree = false
	}
	buyFree, ok := baseCtx["bBuyFree"].(bool)
	if !ok {
		tokenData.BBuyFree = false
	} else {
		tokenData.BBuyFree = buyFree
	}
	token, err := ParamToken(tokenData.Token)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	bc, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	baseCtx_, _ := json.Marshal(baseCtx)
	resp, err := message.Request[pb.GameSpinResp](m.GetServerId(bc), &pb.GameKingSpinReq{
		UserID:   bc.ID,
		GameID:   token.GameID,
		Currency: token.AssetID,
		DeskID:   token.DeskID,
		Ctx:      string(baseCtx_),
	})
	if err != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	s, _ := CompressJSON([]byte(resp.Detail))
	ctx.String(http.StatusOK, s)
}
