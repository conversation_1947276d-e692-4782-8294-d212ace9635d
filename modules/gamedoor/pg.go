package gamedoor

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"igame"
	"net/http"
	"s2/common/cache"
	"s2/gsconf"
	"s2/pb"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

type Token struct {
	Token   string
	GameID  int32
	DeskID  int32
	AssetID int32
}

func ParamToken(s string) (*Token, error) {
	parts := strings.Split(s, ",")
	token := parts[0]
	gameID, err := strconv.ParseInt(parts[1], 10, 32)
	if err != nil {
		return nil, fmt.Errorf("无效的 gameID：%v", err)
	}
	// 解析 deskID
	deskID, err := strconv.ParseInt(parts[2], 10, 32)
	if err != nil {
		return nil, fmt.Errorf("无效的 deskID：%v", err)
	}
	// 解析 currency
	currency, err := strconv.ParseInt(parts[3], 10, 32)
	if err != nil {
		return nil, fmt.Errorf("无效的 currency：%v", err)
	}
	return &Token{Token: token, GameID: int32(gameID), DeskID: int32(deskID), AssetID: int32(currency)}, nil
}

func CompressJSON(b []byte) (string, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	if _, err := writer.Write(b); err != nil {
		log.Infof("Gzip compression error: %v", err)
		writer.Close()
		return "", err
	}
	if err := writer.Close(); err != nil {
		log.Infof("Gzip writer close error: %v", err)
		return "", err
	}
	compressedBase64 := base64.StdEncoding.EncodeToString(buf.Bytes())
	return compressedBase64, nil
}
func (m *module) OnEnterXPGGame(ctx *gin.Context) {
	type EnterGameData struct {
		Account  string `json:"account"`
		Password string `json:"password"`
		Gameid   int64  `json:"gameid"`
		Levelid  int64  `json:"levelid"`
	}
	var req EnterGameData
	if err := ctx.BindJSON(&req); err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	token, err := ParamToken(req.Password)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	bc, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	gameinfo := table.Get[gsconf.GameInfoConf](token.GameID)
	if gameinfo == nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	if !gameinfo.Open {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_MAINTENANCE})
		return
	}
	inputInfo := table.Find[gsconf.InputInfoConf](func(iic *gsconf.InputInfoConf) bool {
		return iic.CurrencyID == token.AssetID && iic.GameID == token.GameID
	})
	if inputInfo == nil {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	resp, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: bc.ID,
	})
	balance := resp.Balance[token.AssetID]
	// param1=518404038369
	// &param2=80fba977d063a6f7262a8a9c95f61140
	// &param3=8860120
	// &param4=88601200
	// &param5=127.0.0.1:9528/wss
	// &param6=en
	// &param8=1
	// &apiurl=http://127.0.0.1:9528&t=1739268479183#slide0
	line := igame.Line_(token.GameID)
	valueList := []float64{1}
	for _, v := range inputInfo.ValueList {
		valueList = append(valueList, v/float64(line))
	}
	BetLv := 1
	if token.GameID == 10000002 {
		valueList = []float64{5, 50, 400}
		BetLv = 10
	}
	valueListStr, _ := json.Marshal(valueList)
	log.Info(string(valueListStr))
	buy_Bouns := line * 100
	buy_FreeMaxGold := valueList[len(valueList)-1] * float64(line) * 100
	contextStr := `{"iTotalFree":0,"currTotalFree":0, "betScore":0, "beiLv":[1,2,3,5],"SingleLineScore":` + string(valueListStr) + `,"Buy_Bouns":` + strconv.FormatInt(int64(buy_Bouns), 10) + `,"Buy_FreeMaxGold":` + strconv.FormatFloat(buy_FreeMaxGold, 'f', -1, 64) + `,"TotalFreeCount":0,"currFreeCount":0,"FreeWinGold":0,"BetScore":` + strconv.FormatFloat(valueList[0], 'f', -1, 64) + `,"IncreaseMul":0,"WaysoftheQilinMul":0,"Lv":1}`
	context := &contextStr
	b, _ := json.Marshal(map[string]any{
		"code":   0,
		"status": true,
		"msg":    "Success",
		"data": map[string]any{
			"tableid": token.DeskID, //可能是游戏会话或桌面的唯一标识符。在老虎机游戏中，这可能表示玩家的当前游戏实例 ID。
			"token":   req.Password, //认证令牌
			"data": map[string]any{
				"userid":         bc.ID, //游戏的唯一标识符，这里可能是 Mahjong Way 的 ID，由提供商分配。
				"NickName":       bc.Name,
				"gold":           balance,   //用户当前的金币或虚拟货币余额，可用于投注。
				"betScores":      valueList, // []any{2, 10, 50}, //可用的投注金额数组，玩家可以从中选择下注金额。
				"lJackPotScores": []any{},
				"GlodMultiple":   1,  //原版100 可能是 "GoldMultiple" 的拼写错误，表示金币或奖金的倍率，可能用于奖金轮或特殊功能。
				"wicon":          "", //用户头像或图标的字段，这里为空，表示未设置或不可用。
			},
			"gameid":         token.GameID,
			"levelid":        token.DeskID,
			"BetLv":          BetLv,        //可能是投注级别或层级，可能表示当前投注模式或倍率级别。
			"Currency":       "USD",        //使用的货币，这里是巴西雷亚尔（BRL），用于处理交易和显示金额。
			"totalLineCount": line,         //支付线的总数。Mahjong Way 游戏通常有 20 条支付线，这与该值一致。
			"minBetScore":    valueList[0], //允许的最小投注金额
			"Context":        context,
			"otherGameID":    0,
			"result":         1,
			"msg":            nil,
		},
	})
	s, _ := CompressJSON(b)
	ctx.String(http.StatusOK, s)
}
func (m *module) OnSpinXPGGame(ctx *gin.Context) {
	type GambleData struct {
		Gamble    float64 `json:"gamble"`
		Gameid    int32   `json:"gameid"`
		Levelid   int32   `json:"levelid"`
		LineCount int32   `json:"lineCount"`
		Lv        int32   `json:"lv"`
		Tableid   int32   `json:"tableid"`
		Token     string  `json:"token"`
		BBuyFree  bool    `json:"bBuyFree"`
	}
	var req GambleData
	if err := ctx.BindJSON(&req); err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	token, err := ParamToken(req.Token)
	if err != nil {
		ctx.String(http.StatusBadRequest, "token error")
		return
	}
	if len(token.Token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		return
	}
	bc, e := cache.QueryUserBasicInfoByToken(token.Token)
	if e != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	line := igame.Line_(token.GameID)
	var input float64 = float64(req.Gamble) * float64(req.Lv) * float64(line)
	var mode int32 = 0
	if req.BBuyFree {
		mode = 1
	}
	resp, err := message.Request[pb.GameSpinResp](m.GetServerId(bc), &pb.GamePGSpinReq{
		UserID:   bc.ID,
		GameID:   token.GameID,
		Currency: token.AssetID,
		DeskID:   token.DeskID,
		Gamble:   req.Gamble,
		Lv:       req.Lv,
		Input:    input,
		Mode:     mode,
	})
	if err != nil {
		log.Error(e)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	if resp.Code != pb.SUCCESS {
		log.Error(resp)
		ctx.JSON(http.StatusOK, pb.Error{Code: resp.Code})
		return
	}
	s, _ := CompressJSON([]byte(resp.Detail))
	ctx.String(http.StatusOK, s)
}
