package order

import (
	"fmt"
	"s2/common"
	"time"
)

// RechargeOrder 表示区块链交易的订单记录
type RechargeOrder struct {
	TxHash      string    `gorm:"primaryKey;type:varchar(128);column:tx_hash"` // 交易哈希
	UserID      int64     `gorm:"not null;type:bigint;column:uid;index"`       // 用户 ID
	Chain       string    `gorm:"not null;type:varchar(12);column:chain"`
	Address     string    `gorm:"not null;type:varchar(128);column:address"`
	Amount      string    `gorm:"not null;type:varchar(50);column:amount"`            // 充值金额，存储为字符串
	AssetId     int32     `gorm:"type:bigint;column:asset_id"`                        // 资源ID
	AssetAmount float64   `gorm:"type:double;column:asset_amount"`                    // 资源金额
	Decimals    int64     `gorm:"not null;type:bigint;column:decimals"`               // 小数点位数
	CoinType    string    `gorm:"not null;type:varchar(50);column:coin_type"`         // 货币类型
	CoinId      int32     `gorm:"not null;type:bigint;column:coin_id"`                // 货币ID
	Status      int32     `gorm:"not null;type:bigint;default:1;column:status;index"` // 订单状态
	Timestamp   uint64    `gorm:"not null;type:bigint;column:timestamp"`              // 时间戳
	PlatOrderId string    `gorm:"type:varchar(128);column:plat_order_id"`             //渠道订单id
	ParentID    int64     `gorm:"not null;type:bigint;column:parent_id;index"`        // 上级ID
	Channel     string    `gorm:"not null;type:varchar(128);column:channel;index"`    // 渠道名称
	CreatedAt   time.Time `gorm:"column:created_at;index"`                            // 创建时间
}

// TableName 自定义表名为 transaction_orders
func (RechargeOrder) TableName() string {
	return "recharge_orders"
}

// 确认的充值订单表名
func confirmedRechargeTableName(t time.Time) string {
	return fmt.Sprintf("recharge_orders_confirmed_%d", common.Months(t))
}

// 确认的提款订单表名
func confirmedWithdrawTableName(t time.Time) string {
	return fmt.Sprintf("withdraw_orders_confirmed_%d", common.Months(t))
}

// WithdrawOrder 表示提款订单记录
type WithdrawOrder struct {
	ID          string    `gorm:"primaryKey;type:varchar(128);column:id"`             // 订单号
	UserID      int64     `gorm:"not null;type:bigint;column:uid;index"`              // 用户 ID
	Chain       string    `gorm:"not null;type:varchar(12);column:chain"`             // 链类型
	CoinType    string    `gorm:"not null;type:varchar(50);column:coin_type"`         // 货币类型
	Amount      string    `gorm:"not null;type:varchar(50);column:amount"`            // 提款金额
	AssetId     int32     `gorm:"type:bigint;column:asset_id"`                        // 资源ID
	AssetAmount float64   `gorm:"type:double;column:asset_amount"`                    // 手续费 资源金额
	AssetFees   float64   `gorm:"type:double;column:asset_Fees"`                      // 手续费 资源金额
	Decimals    int64     `gorm:"not null;type:bigint;column:decimals"`               // 小数点位数
	ToAddress   string    `gorm:"type:varchar(128);column:to_address"`                // 提款地址
	TxHash      string    `gorm:"type:varchar(128);column:tx_hash"`                   // 交易哈希
	Status      int32     `gorm:"not null;type:bigint;default:1;column:status;index"` // 订单状态
	ParentID    int64     `gorm:"not null;type:bigint;column:parent_id;index"`        // 上级ID
	Channel     string    `gorm:"not null;type:varchar(128);column:channel;index"`    // 渠道名称
	CreatedAt   time.Time `gorm:"column:created_at;index"`                            // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at"`                                  // 更新时间
}

// TableName 自定义表名为 withdraw_orders
func (WithdrawOrder) TableName() string {
	return "withdraw_orders"
}
