package order

import (
	"encoding/json"
	"io"
	"net/http"
	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/pb"

	"time"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/shopspring/decimal"
)

func okpayHandler(ctx *gin.Context) {
	b, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.Error(err)
		ctx.String(http.StatusBadRequest, "read body failed")
		return
	}
	var req = struct {
		status string
		data   struct {
			order_id    string
			unique_id   string
			pay_user_id string
			amount      string
			coin        string
		}
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Error(err)
		ctx.String(http.StatusBadRequest, "bad request")
		return
	}
	log.Infof("okpayHandler req: %+v", req)
	_, err = ChangeRechargeOrderPending(req.data.order_id)
	if err != nil {
		log.Error(err)
		ctx.String(http.StatusBadRequest, "bad request")
		return
	}
	var resp = struct {
		status string
	}{
		status: "success",
	}
	respB, _ := json.Marshal(resp)
	ctx.Data(http.StatusOK, "application/json;", respB)
}

func (m *module) onGetOrSetUserRechargeAddressReq(body *pb.GetOrSetUserRechargeAddressReq, response func(*pb.GetOrSetUserRechargeAddressResp, error)) {
	chain, ok := m.chains[body.Chain]
	if !ok {
		response(&pb.GetOrSetUserRechargeAddressResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	address, extra, err := chain.GetUserRechargeAddress(body.UserID)
	if err != nil {
		response(&pb.GetOrSetUserRechargeAddressResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	response(&pb.GetOrSetUserRechargeAddressResp{
		Code:    pb.SUCCESS,
		Address: address,
		Extra:   extra,
	}, nil)
}

func (m *module) onWithdrawOrderOperationReq(body *pb.WithdrawOrderOperationReq, response func(*pb.WithdrawOrderOperationResp, error)) {
	state := pb.OrderStatus_REJECTED_PENDING
	if body.Operation == 1 {
		state = pb.OrderStatus_PENDING
	} else if body.Operation == 2 {
		state = pb.OrderStatus_REJECTED_PENDING
	} else {
		response(&pb.WithdrawOrderOperationResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	_, err := UpdateWithdrawOrderStatus(body.ID, state, body.Channel)
	if err != nil {
		response(&pb.WithdrawOrderOperationResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	response(&pb.WithdrawOrderOperationResp{Code: pb.SUCCESS}, nil)
}

func (m *module) onCheckAndCommitOrderReq(body *pb.CheckAndCommitOrderReq, response func(*pb.CheckAndCommitOrderResp, error)) {
	if body.OrderType == 1 {
		order, err := CheckAndSubmitRechargeOrder(body.TxHash)
		if err != nil {
			response(&pb.CheckAndCommitOrderResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		inputX := conf.Num[float64]("chain.inputX", 0)
		response(&pb.CheckAndCommitOrderResp{
			Code:   pb.SUCCESS,
			UserID: order.UserID,
			Asset: &pb.IDValFloat{
				ID:        int64(order.AssetId),
				Value:     order.AssetAmount,
				LeftInput: order.AssetAmount * inputX,
			},
		}, nil)
	} else {
		order, err := CheckAndSubmitWithdrawOrder(body.TxHash)
		if err != nil {
			log.Errorf("CheckAndSubmitWithdrawOrder err: %v", err)
			response(&pb.CheckAndCommitOrderResp{Code: pb.SERVER_ERROR}, nil)
			return
		}
		response(&pb.CheckAndCommitOrderResp{
			Code:   pb.SUCCESS,
			UserID: order.UserID,
			Asset: &pb.IDValFloat{
				ID:        int64(order.AssetId),
				Value:     order.AssetAmount + order.AssetFees,
				LeftInput: 0,
			},
		}, nil)
	}

}

func (m *module) onGetCoinMapReq(body *pb.GetCoinMapReq, response func(*pb.GetCoinMapResp, error)) {
	resp := &pb.GetCoinMapResp{Code: pb.SUCCESS}
	coinALL := coins.GetALL()
	for _, coin := range coinALL {
		config := coin.AssetConfig()
		if coin.RechargeOpen() {
			resp.RechargeList = append(resp.RechargeList, &pb.CoinInfo{
				Coin:   coin.Coin(),
				Chain:  coin.Chain().Type(),
				Config: &config,
			})
		}
		if coin.WithdrawOpen() {
			resp.WithdrawList = append(resp.WithdrawList, &pb.CoinInfo{
				Coin:   coin.Coin(),
				Chain:  coin.Chain().Type(),
				Config: &config,
			})
		}
	}
	response(resp, nil)
}

func (m *module) onCreateRechargeOrderReq(body *pb.CreateRechargeOrderReq, response func(*pb.CreateRechargeOrderResp, error)) {
	coins := coins.GetALL()
	var coin stypes.ICoin
	for _, c := range coins {
		if c.Coin() == body.CoinID && c.Chain().Type() == body.Chain {
			coin = c
			break
		}
	}
	if coin == nil {
		response(&pb.CreateRechargeOrderResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	if body.Value <= 0 {
		response(&pb.CreateRechargeOrderResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	amount := decimal.NewFromFloat(body.Value).Mul(decimal.NewFromInt(coin.CoinDecimals())).String()
	chain, ok := chains.Get(body.Chain)
	if !ok {
		return
	}
	orderId := CreateOrderId(body.UserID)
	pay_url, order_id, err := coin.CreateOrder(map[string]any{
		"orderId": orderId,
		"amount":  body.Value,
	})
	if err != nil {
		log.Errorf("Failed to create recharge order: %v", err)
		response(&pb.CreateRechargeOrderResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	order, err := NewRechargeOrderChecking(
		orderId,
		body.UserID,
		chain,
		coin,
		amount,
		order_id,
	)
	if err != nil {
		log.Errorf("Failed to create recharge order: %v", err)
		response(&pb.CreateRechargeOrderResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	amt, _ := decimal.NewFromString(order.Amount)
	af := amt.Div(decimal.NewFromInt(order.Decimals)).InexactFloat64()
	response(&pb.CreateRechargeOrderResp{
		Code:   pb.SUCCESS,
		PayUrl: pay_url,
		Order: &pb.RechargeOrderInfo{
			TxHash:    order.TxHash,
			UserID:    order.UserID,
			Chain:     order.Chain,
			Address:   order.Address,
			Amount:    af,
			Decimals:  order.Decimals,
			CoinType:  order.CoinType,
			CoinId:    int32(order.CoinId),
			Status:    pb.EnumOrderStatus(order.Status),
			Timestamp: order.Timestamp,
			CreatedAt: uint64(order.CreatedAt.Unix()),
		},
	}, nil)
}

func (m *module) onCreateWithdrawOrderReq(body *pb.CreateWithdrawOrderReq, response func(*pb.CreateWithdrawOrderResp, error)) {
	coins := coins.GetALL()
	var coin stypes.ICoin
	for _, c := range coins {
		if c.Coin() == body.CoinID && c.Chain().Type() == body.Chain {
			coin = c
			break
		}
	}
	if coin == nil {
		response(&pb.CreateWithdrawOrderResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	if body.AssetAmount <= 0 {
		response(&pb.CreateWithdrawOrderResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	// 创建提款订单
	config := coin.AssetConfig()
	amount := decimal.NewFromFloat(body.AssetAmount).Div(decimal.NewFromFloat(config.ExchangeRate)).Mul(decimal.NewFromInt(coin.CoinDecimals())).String()
	order, err := CreateWithdrawOrder(
		body.UserID,
		body.Chain.String(),
		body.CoinID.String(),
		amount,
		coin.CoinDecimals(),
		int32(config.AssetId),
		body.AssetAmount,
		body.AssetFrees,
		body.To,
	)
	if err != nil {
		log.Errorf("Failed to create withdraw order: %v", err)
		response(&pb.CreateWithdrawOrderResp{Code: pb.SERVER_ERROR}, nil)
		return
	}
	amt, _ := decimal.NewFromString(order.Amount)
	af := amt.Div(decimal.NewFromInt(order.Decimals)).InexactFloat64()
	response(&pb.CreateWithdrawOrderResp{
		Code: pb.SUCCESS,
		Order: &pb.WithdrawOrderInfo{
			ID:        order.ID,
			UserID:    order.UserID,
			Chain:     order.Chain,
			CoinType:  order.CoinType,
			Amount:    af,
			ToAddress: order.ToAddress,
			TxHash:    order.TxHash,
			Status:    pb.EnumOrderStatus(order.Status),
			CreatedAt: uint64(order.CreatedAt.Unix()),
			UpdatedAt: uint64(order.UpdatedAt.Unix()),
		},
	}, nil)
}

// onHistoryWithdrawOrderReq handles pagination and filters for withdraw orders
func (m *module) onHistoryWithdrawOrderReq(body *pb.HistoryWithdrawOrderReq, response func(*pb.HistoryWithdrawOrderResp, error)) {
	req := body
	if req.EndTime > 1e10 { // If timestamp is in milliseconds
		req.EndTime = req.EndTime / 1000
	}
	if req.StartTime > 1e10 { // If timestamp is in milliseconds
		req.StartTime = req.StartTime / 1000
	}
	page := int(req.PageNumber)
	if page <= 0 {
		page = 1
	}
	size := int(req.PageSize)
	if size <= 0 {
		size = 20
	}
	offset := (page - 1) * size
	var orders []WithdrawOrder
	if req.EndTime == 0 {
		req.EndTime = uint64(time.Now().Unix())
	}
	tableName := ""
	if body.Type == 1 {
		tableName = WithdrawOrder{}.TableName()
	} else if body.Type == 2 {
		tableName = confirmedWithdrawTableName(time.Unix(int64(req.EndTime), 0))
	} else if body.Type == 3 { //待审核
		tableName = WithdrawOrder{}.TableName()
	} else {
		tableName = confirmedWithdrawTableName(time.Unix(int64(req.EndTime), 0))
	}
	query := mdb.Default().Table(tableName)
	if body.Type == 3 {
		query = query.Where("status = ?", pb.OrderStatus_CHECKING)
	}
	if req.UserID != 0 {
		query = query.Where("uid = ?", req.UserID)
	}
	if req.ParentID != 0 {
		query = query.Where("parent_id = ?", req.ParentID)
	}
	if req.Channel != "" {
		query = query.Where("channel = ?", req.Channel)
	}
	if req.StartTime != 0 {
		query = query.Where("created_at >= ?", time.Unix(int64(req.StartTime), 0))
	}
	if req.EndTime != 0 {
		query = query.Where("created_at <= ?", time.Unix(int64(req.EndTime), 0))
	}
	if req.OrderID != 0 {
		query = query.Where("id = ?", req.OrderID)
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response(&pb.HistoryWithdrawOrderResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	if err := query.Order("created_at desc").Offset(offset).Limit(size).Find(&orders).Error; err != nil {
		response(&pb.HistoryWithdrawOrderResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	resp := &pb.HistoryWithdrawOrderResp{Code: pb.SUCCESS, Total: total}
	for _, o := range orders {
		amt, _ := decimal.NewFromString(o.Amount)
		amount := amt.Div(decimal.NewFromInt(o.Decimals)).InexactFloat64()
		resp.Orders = append(resp.Orders, &pb.WithdrawOrderInfo{
			ID:          o.ID,
			UserID:      o.UserID,
			Chain:       o.Chain,
			CoinType:    o.CoinType,
			Amount:      amount,
			ToAddress:   o.ToAddress,
			AssetAmount: o.AssetAmount,
			AssetFees:   o.AssetFees,
			AssetId:     o.AssetId,
			TxHash:      o.TxHash,
			ParentID:    o.ParentID,
			Channel:     o.Channel,
			Status:      pb.EnumOrderStatus(o.Status),
			AssetName:   pb.EnumAsset_name[int32(o.AssetId)],
			CreatedAt:   uint64(o.CreatedAt.Unix()),
			UpdatedAt:   uint64(o.UpdatedAt.Unix()),
		})
	}
	response(resp, nil)
}

// onHistoryRechargeOrderReq handles pagination and filters for recharge orders
func (m *module) onHistoryRechargeOrderReq(body *pb.HistoryRechargeOrderReq, response func(*pb.HistoryRechargeOrderResp, error)) {
	req := body
	if req.EndTime > 1e10 { // If timestamp is in milliseconds
		req.EndTime = req.EndTime / 1000
	}
	if req.StartTime > 1e10 { // If timestamp is in milliseconds
		req.StartTime = req.StartTime / 1000
	}
	page := int(req.PageNumber)
	if page <= 0 {
		page = 1
	}
	size := int(req.PageSize)
	if size <= 0 {
		size = 20
	}
	offset := (page - 1) * size
	var orders []RechargeOrder
	if req.EndTime == 0 {
		req.EndTime = uint64(time.Now().Unix())
	}
	tableName := ""
	if body.Type == 1 {
		tableName = RechargeOrder{}.TableName()
	} else if body.Type == 2 {
		tableName = confirmedRechargeTableName(time.Unix(int64(req.EndTime), 0))
	} else {
		tableName = confirmedRechargeTableName(time.Unix(int64(req.EndTime), 0))
	}
	query := mdb.Default().Table(tableName)
	if req.UserID != 0 {
		query = query.Where("uid = ?", req.UserID)
	}
	if req.ParentID != 0 {
		query = query.Where("parent_id = ?", req.ParentID)
	}
	if req.Channel != "" {
		query = query.Where("channel = ?", req.Channel)
	}
	if req.StartTime != 0 {
		query = query.Where("created_at >= ?", time.Unix(int64(req.StartTime), 0))
	}
	if req.EndTime != 0 {
		query = query.Where("created_at <= ?", time.Unix(int64(req.EndTime), 0))
	}
	if req.TxHash != "" {
		query = query.Where("tx_hash = ?", req.TxHash)
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		response(&pb.HistoryRechargeOrderResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	if err := query.Order("created_at desc").Offset(offset).Limit(size).Find(&orders).Error; err != nil {
		response(&pb.HistoryRechargeOrderResp{Code: pb.SERVER_ERROR}, err)
		return
	}
	resp := &pb.HistoryRechargeOrderResp{Code: pb.SUCCESS, Total: total}
	for _, o := range orders {
		amt, _ := decimal.NewFromString(o.Amount)
		amount := amt.Div(decimal.NewFromInt(o.Decimals)).InexactFloat64()
		resp.Orders = append(resp.Orders, &pb.RechargeOrderInfo{
			TxHash:      o.TxHash,
			UserID:      o.UserID,
			Chain:       o.Chain,
			Address:     o.Address,
			Amount:      amount,
			AssetAmount: o.AssetAmount,
			AssetId:     o.AssetId,
			Decimals:    o.Decimals,
			CoinType:    o.CoinType,
			CoinId:      int32(o.CoinId),
			Status:      pb.EnumOrderStatus(o.Status),
			Timestamp:   o.Timestamp,
			AssetName:   pb.EnumAsset_name[int32(o.AssetId)],
			CreatedAt:   uint64(o.CreatedAt.Unix()),
		})
	}
	response(resp, nil)
}
