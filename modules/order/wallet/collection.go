package wallet

import (
	"math/big"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"time"

	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	speedLimit = make(chan struct{}, 10)
)

func ChangeBalance(coin stypes.ICoin, address string, value *big.Int) {
	tx := mdb.Default().TxReadCommit()
	defer mdb.RecoverWithRollback(tx)
	valueFloat, _ := value.Float64()
	valueFloat /= float64(coin.CoinDecimals())
	wb := &WalletBalance{
		Address: address,
		Balance: valueFloat,
	}
	tableName := balanceTableName(coin.CoinSymbol())
	err := tx.Table(tableName).Clauses(clause.OnConflict{
		DoUpdates: clause.Assignments(map[string]interface{}{
			"balance": gorm.Expr("balance + ?", valueFloat),
		}),
	}).Create(wb).Error
	if err != nil {
		tx.Rollback()
		log.Error(err)
		return
	}
	err = tx.Table(tableName).Where("address = ?", wb.Address).First(wb).Error
	if err != nil {
		tx.Rollback()
		log.Error(err)
		return
	}
	tx.Commit()
	if wb.Balance >= coin.CollectionLimit() {
		doCollection(coin, address)
	}
}

func doCollection(coin stypes.ICoin, address string) {
	defer utils.RecoverPanic()
	select {
	case speedLimit <- struct{}{}:
		conc.Go(func() {
			defer func() {
				<-speedLimit
			}()
			private, err := GetPrivateKeyByAddress(coin.Chain().Type(), address)
			if err != nil {
				log.Error(err)
				return
			}
			amount, err := coin.Chain().Collection(coin, private)
			if err != nil {
				log.Errorf("doSweep %s %s %v", coin.CoinSymbol(), address, err)
				return
			}
			ChangeBalance(coin, address, big.NewInt(0).Sub(big.NewInt(0), amount))
		})
	default:
		log.Warnf("doSweep %s %s %s", coin.CoinSymbol(), address, "too many sweep tasks")
	}
}

// 归集
func collectionLoop() {
	time.Sleep(time.Second * 5)
	collectionTick()
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()
	for {
		select {
		case <-system.RootCtx().Done():
			return
		case <-ticker.C:
			collectionTick()
		}
	}
}

// 扫描所有币的余额，如果余额大于等于 sweep limit，则进行扫币
func collectionTick() {
	defer utils.RecoverPanic()
	coinList := coins.GetALL()
	for _, coin := range coinList {
		rows, err := mdb.Default().Table(balanceTableName(coin.CoinSymbol())).
			Select("address").Where("balance >= ?", coin.CollectionLimit()).Rows()
		if err != nil {
			log.Error(err)
			continue
		}
		for rows.Next() {
			var address string
			err := rows.Scan(&address)
			if err != nil {
				break
			}
			doCollection(coin, address)
		}
	}
}
