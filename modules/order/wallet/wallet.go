package wallet

import (
	"fmt"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/bits-and-blooms/bloom/v3"
	"github.com/bluele/gcache"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"gorm.io/gorm"
)

var (
	wallets = map[pb.EnumChain]*Wallet{}
)

// Wallet 管理特定链下的所有地址合集，使用布隆过滤器优化查询
type Wallet struct {
	chain    stypes.IChain
	filter   *bloom.BloomFilter // 布隆过滤器，用于快速地址检查
	userIDs  gcache.Cache
	privates gcache.Cache
}

// WalletAddress 存储钱包地址信息，使用 user_id 和 chain_type 作为复合主键，address 字段全局唯一
type WalletAddress struct {
	UserID  int64  `gorm:"column:user_id;primaryKey"`                            // 用户唯一标识符，作为主键一部分
	Chain   string `gorm:"column:chain;primaryKey;type:varchar(32);not null"`    // 链类型
	Address string `gorm:"column:address;type:varchar(66);not null;uniqueIndex"` // 区块链地址，确保全局唯一性
	Private string `gorm:"column:private;type:varchar(128);not null;"`           // 私钥
}

// TableName 自定义表名为 wallet_addresses
func (WalletAddress) TableName() string {
	return "wallet_address"
}

// InitWallet 创建一个新的初始化钱包实例，绑定到 Chain
func InitWallet(chains []stypes.IChain) error {
	err := mdb.Default().AutoMigrate(&WalletAddress{})
	if err != nil {
		log.Panic(err)
	}
	for _, chain := range chains {
		// 获取链类型
		chainID := chain.Type()

		// 初始化钱包实例
		filter := bloom.NewWithEstimates(1000000, 0.01) // 初始化布隆过滤器，容量1000000，误判率0.01
		wallets[chainID] = &Wallet{
			chain:    chain,
			filter:   filter,
			userIDs:  gcache.New(10000).LRU().Build(),
			privates: gcache.New(10000).LRU().Build(),
		}
	}
	rows, err := mdb.Default().Table(WalletAddress{}.TableName()).Select("chain", "address").Rows()
	if err != nil {
		return err
	}
	defer rows.Close()
	for rows.Next() {
		var address WalletAddress
		err := rows.Scan(&address.Chain, &address.Address)
		if err != nil {
			return err
		}
		chain, ok := pb.EnumChain_value[address.Chain]
		if !ok {
			log.Warnf("chain not found %s", address.Address)
			continue
		}
		wallet := wallets[pb.EnumChain(chain)]
		if wallet == nil {
			continue
		}
		wallets[pb.EnumChain(chain)].filter.AddString(address.Address)
	}
	if err := rows.Err(); err != nil {
		log.Panicf("Rows iteration error: %v", err)
	}
	coinList := coins.GetALL()
	for _, coin := range coinList {
		err = mdb.Default().Table(balanceTableName(coin.CoinSymbol())).AutoMigrate(&WalletBalance{})
		if err != nil {
			log.Panic(err)
		}
	}

	conc.Go(func() {
		collectionLoop()
	})
	return nil
}

// ContainsAddress 精确检查地址是否存在，并返回对应的 UserID，包含布隆过滤器快速检查
// 返回值：(exists bool, userID int64, err error)
// - 如果地址存在，返回 (true, userID, nil)
// - 如果地址不存在，返回 (false, 0, nil)
// - 如果查询出错，返回 (false, 0, err)
func ContainsAddress(chain pb.EnumChain, address string) (bool, int64, error) {
	w := wallets[chain]
	if value, err := w.userIDs.Get(address); err == nil {
		return true, value.(int64), nil
	}
	// 首先通过布隆过滤器快速检查
	if !w.filter.Test([]byte(address)) {
		return false, 0, nil // 布隆过滤器认为地址不存在，直接返回
	}

	var addr WalletAddress
	err := mdb.Default().Where("address = ?", address).First(&addr).Error
	if err == gorm.ErrRecordNotFound {
		return false, 0, nil // 地址不存在
	}
	if err != nil {
		return false, 0, fmt.Errorf("failed to query address %s: %v", address, err)
	}
	w.userIDs.Set(address, addr.UserID)
	return true, addr.UserID, nil
}

// 添加一个地址到钱包和数据库
func GetRechargeAddress(userID int64, chain stypes.IChain) (*WalletAddress, error) {
	address := []*WalletAddress{}
	err := mdb.Default().Table(WalletAddress{}.TableName()).
		Where("chain = ? AND user_id = ?", chain.Type().String(), userID).Find(&address).Error
	if err != nil {
		log.Error(err)
		return nil, err
	}
	if len(address) > 0 {
		return address[0], nil
	}
	return nil, nil
}

// 添加一个地址到钱包和数据库
func SaveRechargeAddress(userID int64, chain stypes.IChain, address string, private string) error {
	err := mdb.Default().Create(&WalletAddress{
		Chain:   chain.Type().String(),
		UserID:  userID,
		Address: address,
		Private: private,
	}).Error
	// 更新布隆过滤器
	wallets[chain.Type()].filter.Add([]byte(address))
	if err != nil {
		log.Error(fmt.Errorf("failed to add address %s for user %d: %v", address, userID, err))
		return err
	}
	return nil
}

func GetPrivateKeyByAddress(chain pb.EnumChain, address string) (string, error) {
	wallet, ok := wallets[chain]
	if !ok {
		return "", fmt.Errorf("chain %s wallet not found", chain)
	}
	private, err := wallet.privates.Get(address)
	if err == nil {
		return private.(string), nil
	}
	var data WalletAddress
	err = mdb.Default().Model(&WalletAddress{}).Find(&data, "address = ?", address).Error
	if err != nil {
		return "", err
	}
	private_, err := wallet.chain.GetUserRechargePrivate(data.UserID)
	if err != nil {
		return "", err
	}
	wallet.privates.Set(data.Address, private_)
	return private_, nil
}
