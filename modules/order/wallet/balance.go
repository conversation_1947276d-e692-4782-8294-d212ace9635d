package wallet

import (
	"fmt"
	"s2/pb"
)

type WalletBalance struct {
	Address      string  `gorm:"column:address;primaryKey;type:varchar(66);primaryKey"` // 地址
	Balance      float64 `gorm:"column:balance;not null"`                               // 累计进帐
	CollectionAt int64   `gorm:"column:collection_at;not null"`                         // 上次归集的时间戳
}

func balanceTableName(coinSymbol pb.EnumCoinSymbol) string {
	return fmt.Sprintf("wallet_balance_%s", coinSymbol)
}
