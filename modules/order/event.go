package order

import (
	"s2/common/cache"
	"s2/modules/order/chains"
	"s2/modules/order/coins"
	"s2/modules/order/stypes"
	"s2/modules/order/wallet"
	"s2/pb"

	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func init() {
	events.Subscribe(nil, onEventTransfer, stypes.EventTransfer{}.Name())
	events.Subscribe(nil, onTONEventTransfer, stypes.TONEventTransfer{}.Name())
}
func onTONEventTransfer(event stypes.TONEventTransfer) {
	chain, ok := chains.Get(event.Chain)
	if !ok {
		return
	}
	coin, ok := coins.Get(event.Coin)
	if !ok {
		return
	}
	err := NewRechargeOrder(event.Uid, chain, coin, event.From, event.Value.String(), event.TxHash, event.Timestamp)
	if err != nil {
		log.Error(err)
	}
	uc, err := cache.QueryUserBasicInfo(event.Uid)
	if err != nil {
		log.Error(err)
		return
	}
	message.Stream.Cast(uc.ServerID, &pb.UserRechargeMsg{
		UserID: event.Uid,
		TxHash: event.TxHash,
	})
}
func onEventTransfer(event stypes.EventTransfer) {
	chain, ok := chains.Get(event.Chain)
	if !ok {
		return
	}
	if chain.IsOfficialAddress(event.From) || chain.IsOfficialAddress(event.To) {
		return
	}
	coin, ok := coins.Get(event.Coin)
	if !ok {
		return
	}
	to := event.To
	exist, uid, err := wallet.ContainsAddress(chain.Type(), to)
	if err != nil {
		log.Error(err)
		return
	}
	if !exist {
		return
	}
	err = NewRechargeOrder(uid, chain, coin, to, event.Value.String(), event.TxHash, event.Timestamp)
	if err != nil {
		log.Error(err)
	}
	uc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		log.Error(err)
		return
	}
	wallet.ChangeBalance(coin, to, event.Value)
	message.Stream.Cast(uc.ServerID, &pb.UserRechargeMsg{
		UserID: uid,
		TxHash: event.TxHash,
	})
}
