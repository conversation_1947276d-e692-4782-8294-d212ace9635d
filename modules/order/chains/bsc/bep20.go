package bsc

import (
	"context"
	"fmt"
	"math/big"
	"s2/modules/order/stypes"
	"s2/pb"
	"strings"

	"s2/common/web3"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/jfcwrlight/core/log"
)

// BEP20 定义了 BEP20 代币的标准接口
type BEP20 interface {
	// 基本查询接口
	Name() (string, error)
	Symbol() (string, error)
	Decimals() (uint8, error)
	TotalSupply() (*big.Int, error)
	BalanceOf(address string) (*big.Int, error)

	// 转账相关接口
	Transfer(privateKey string, to string, amount *big.Int) (string, error)
	TransferFrom(privateKey string, from string, to string, amount *big.Int) (string, error)
	Approve(privateKey string, spender string, amount *big.Int) (string, error)
	Allowance(owner string, spender string) (*big.Int, error)

	// 扩展接口
	IncreaseAllowance(privateKey string, spender string, addedValue *big.Int) (string, error)
	DecreaseAllowance(privateKey string, spender string, subtractedValue *big.Int) (string, error)
}

var transferEventSignature = crypto.Keccak256Hash([]byte("Transfer(address,address,uint256)"))

// BEP20Token 实现了 BEP20 接口
type BEP20Token struct {
	contractAddress common.Address
	abi             abi.ABI
	coinSymbol      pb.EnumCoinSymbol
	decimals        int64
	coin            pb.EnumCoin
	collectionLimit float64
	chain           stypes.IChain
	rechargeOpen    bool
	withdrawOpen    bool
	config          pb.CoinConfig
}

// NewBEP20Token 创建一个新的 BEP20 代币实例
func NewBEP20Token(chain stypes.IChain, contractAddress string, coinSymbol pb.EnumCoinSymbol, decimals int64, coin pb.EnumCoin, collectionLimit float64, rechargeOpen bool, withdrawOpen bool, config pb.CoinConfig) *BEP20Token {
	// 定义 BEP20 标准 ABI
	tokenABI, _ := abi.JSON(strings.NewReader(`[
		{
			"constant": true,
			"inputs": [],
			"name": "name",
			"outputs": [{"name": "", "type": "string"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "symbol",
			"outputs": [{"name": "", "type": "string"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "decimals",
			"outputs": [{"name": "", "type": "uint8"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [],
			"name": "totalSupply",
			"outputs": [{"name": "", "type": "uint256"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [{"name": "account", "type": "address"}],
			"name": "balanceOf",
			"outputs": [{"name": "", "type": "uint256"}],
			"type": "function"
		},
		{
			"constant": false,
			"inputs": [
				{"name": "recipient", "type": "address"},
				{"name": "amount", "type": "uint256"}
			],
			"name": "transfer",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		},
		{
			"constant": false,
			"inputs": [
				{"name": "sender", "type": "address"},
				{"name": "recipient", "type": "address"},
				{"name": "amount", "type": "uint256"}
			],
			"name": "transferFrom",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		},
		{
			"constant": false,
			"inputs": [
				{"name": "spender", "type": "address"},
				{"name": "amount", "type": "uint256"}
			],
			"name": "approve",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		},
		{
			"constant": true,
			"inputs": [
				{"name": "owner", "type": "address"},
				{"name": "spender", "type": "address"}
			],
			"name": "allowance",
			"outputs": [{"name": "", "type": "uint256"}],
			"type": "function"
		},
		{
			"constant": false,
			"inputs": [
				{"name": "spender", "type": "address"},
				{"name": "addedValue", "type": "uint256"}
			],
			"name": "increaseAllowance",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		},
		{
			"constant": false,
			"inputs": [
				{"name": "spender", "type": "address"},
				{"name": "subtractedValue", "type": "uint256"}
			],
			"name": "decreaseAllowance",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		},
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "from", "type": "address"},
				{"indexed": true, "name": "to", "type": "address"},
				{"indexed": false, "name": "value", "type": "uint256"}
			],
			"name": "Transfer",
			"type": "event"
		},
		{
			"anonymous": false,
			"inputs": [
				{"indexed": true, "name": "owner", "type": "address"},
				{"indexed": true, "name": "spender", "type": "address"},
				{"indexed": false, "name": "value", "type": "uint256"}
			],
			"name": "Approval",
			"type": "event"
		}
	]`))
	return &BEP20Token{
		contractAddress: common.HexToAddress(contractAddress),
		abi:             tokenABI,
		coinSymbol:      coinSymbol,
		decimals:        decimals,
		coin:            coin,
		collectionLimit: collectionLimit,
		chain:           chain,
		rechargeOpen:    rechargeOpen,
		withdrawOpen:    withdrawOpen,
		config:          config,
	}
}

func (e *BEP20Token) ClientRead() *ethclient.Client {
	chain := e.chain.(*Chain)
	return chain.ClientRead()
}

func (e *BEP20Token) ClientWrite() *ethclient.Client {
	chain := e.chain.(*Chain)
	return chain.ClientWrite()
}

func (e *BEP20Token) ClientId() *big.Int {
	chain := e.chain.(*Chain)
	return chain.ClientId()
}

// 实现基本查询接口
func (t *BEP20Token) Name() (string, error) {
	var result string
	err := t.callContract("name", nil, &result)
	return result, err
}

func (t *BEP20Token) Symbol() (string, error) {
	var result string
	err := t.callContract("symbol", nil, &result)
	return result, err
}

func (t *BEP20Token) Decimals() (uint8, error) {
	var result uint8
	err := t.callContract("decimals", nil, &result)
	return result, err
}

func (t *BEP20Token) TotalSupply() (*big.Int, error) {
	var result *big.Int
	err := t.callContract("totalSupply", nil, &result)
	return result, err
}

func (t *BEP20Token) BalanceOf(address string) (*big.Int, error) {
	var result *big.Int
	err := t.callContract("balanceOf", []interface{}{common.HexToAddress(address)}, &result)
	return result, err
}

// 实现转账相关接口
func (t *BEP20Token) Transfer(privateKey string, to string, amount *big.Int) (string, error) {
	return t.sendTransaction(privateKey, "transfer", []interface{}{common.HexToAddress(to), amount})
}

func (t *BEP20Token) TransferFrom(privateKey string, from string, to string, amount *big.Int) (string, error) {
	return t.sendTransaction(privateKey, "transferFrom", []interface{}{common.HexToAddress(from), common.HexToAddress(to), amount})
}

// Approve 授权代币给spender使用
func (t *BEP20Token) Approve(privateKey string, spender string, amount *big.Int) (string, error) {
	return t.sendTransaction(privateKey, "approve", []interface{}{common.HexToAddress(spender), amount})
}

func (t *BEP20Token) ApproveEstimateGas(privateKey string, spender string, amount *big.Int) (uint64, error) {
	return t.estimateGas(privateKey, "approve", []interface{}{common.HexToAddress(spender), amount})
}

func (t *BEP20Token) Allowance(owner string, spender string) (*big.Int, error) {
	var result *big.Int
	err := t.callContract("allowance", []interface{}{common.HexToAddress(owner), common.HexToAddress(spender)}, &result)
	return result, err
}

// 实现扩展接口
func (t *BEP20Token) IncreaseAllowance(privateKey string, spender string, addedValue *big.Int) (string, error) {
	return t.sendTransaction(privateKey, "increaseAllowance", []interface{}{common.HexToAddress(spender), addedValue})
}

func (t *BEP20Token) DecreaseAllowance(privateKey string, spender string, subtractedValue *big.Int) (string, error) {
	return t.sendTransaction(privateKey, "decreaseAllowance", []interface{}{common.HexToAddress(spender), subtractedValue})
}

// 辅助方法
func (t *BEP20Token) callContract(method string, args []interface{}, result interface{}) error {
	data, err := t.abi.Pack(method, args...)
	if err != nil {
		return fmt.Errorf("failed to pack data: %v", err)
	}

	msg := ethereum.CallMsg{
		To:   &t.contractAddress,
		Data: data,
	}

	response, err := t.ClientRead().CallContract(context.Background(), msg, nil)
	if err != nil {
		return fmt.Errorf("failed to call contract: %v", err)
	}

	return t.abi.UnpackIntoInterface(result, method, response)
}

// estimateGas 估算 gas limit
func (t *BEP20Token) estimateGas(privateKey string, method string, args []interface{}) (uint64, error) {
	data, err := t.abi.Pack(method, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to pack data: %v", err)
	}
	fromAddress, err := web3.GetAddressFromPrivateKey(privateKey)
	// 估算 gas limit
	msg := ethereum.CallMsg{
		From:  common.HexToAddress(fromAddress),
		To:    &t.contractAddress,
		Data:  data,
		Value: big.NewInt(0),
	}
	gasLimit, err := t.ClientWrite().EstimateGas(context.Background(), msg)
	if err != nil {
		return 0, fmt.Errorf("failed to estimate gas: %v", err)
	}
	return gasLimit, nil
}

// GasPrice 获取当前 gas 价格
func (t *BEP20Token) GasPrice() (*big.Int, error) {
	gasPrice, err := t.ClientWrite().SuggestGasPrice(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %v", err)
	}
	gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(12))
	gasPrice = new(big.Int).Div(gasPrice, big.NewInt(10))
	return gasPrice, nil
}
func (t *BEP20Token) sendTransaction(privateKey string, method string, args []interface{}) (string, error) {
	data, err := t.abi.Pack(method, args...)
	if err != nil {
		return "", fmt.Errorf("failed to pack data: %v", err)
	}

	// 从私钥获取地址
	fromAddress, err := web3.GetAddressFromPrivateKey(privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to get address from private key: %v", err)
	}
	fromAddr := common.HexToAddress(fromAddress)

	gasPrice, err := t.GasPrice()
	if err != nil {
		return "", fmt.Errorf("failed to get gas price: %v", err)
	}

	// 获取 nonce
	nonce, err := t.ClientWrite().PendingNonceAt(context.Background(), fromAddr)
	if err != nil {
		return "", fmt.Errorf("failed to get nonce: %v", err)
	}

	// 估算 gas limit
	msg := ethereum.CallMsg{
		From:  fromAddr,
		To:    &t.contractAddress,
		Data:  data,
		Value: big.NewInt(0),
	}
	gasLimit, err := t.ClientWrite().EstimateGas(context.Background(), msg)
	if err != nil {
		return "", fmt.Errorf("failed to estimate gas: %v", err)
	}

	// 设置最大 gas limit
	const maxGasLimit = 10000000 // 1000万 gas
	if gasLimit > maxGasLimit {
		return "", fmt.Errorf("estimated gas limit %d exceeds maximum limit %d", gasLimit, maxGasLimit)
	}

	// 检查余额是否足够支付 gas
	balance, err := t.ClientWrite().BalanceAt(context.Background(), fromAddr, nil)
	if err != nil {
		return "", fmt.Errorf("failed to get balance: %v", err)
	}

	gasCost := new(big.Int).Mul(gasPrice, big.NewInt(int64(gasLimit)))
	if balance.Cmp(gasCost) < 0 {
		return "", fmt.Errorf("insufficient balance for gas: have %s, need %s", balance.String(), gasCost.String())
	}

	// 创建交易
	tx := types.NewTransaction(
		nonce,
		t.contractAddress,
		big.NewInt(0),
		gasLimit,
		gasPrice,
		data,
	)

	// 签名交易
	privateKeyECDSA, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %v", err)
	}

	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(t.ClientId()), privateKeyECDSA)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %v", err)
	}

	// 发送交易
	err = t.ClientWrite().SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %v", err)
	}

	log.Infof("bsc bep20 send method: %s, transaction %s", method, signedTx.Hash().Hex())
	return signedTx.Hash().Hex(), nil
}

// 事件监听
func (t *BEP20Token) WatchTransferEvents(fromBlock uint64) (<-chan types.Log, error) {
	ctx := context.Background()
	transferEventSignature := crypto.Keccak256Hash([]byte("Transfer(address,address,uint256)"))
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		Addresses: []common.Address{t.contractAddress},
		Topics: [][]common.Hash{
			{transferEventSignature},
		},
	}

	logs := make(chan types.Log)
	sub, err := t.ClientRead().SubscribeFilterLogs(ctx, query, logs)
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to logs: %v", err)
	}

	go func() {
		for {
			select {
			case err := <-sub.Err():
				log.Errorf("Subscription error: %v", err)
				return
			case <-ctx.Done():
				return
			}
		}
	}()

	return logs, nil
}

func (t *BEP20Token) WatchApprovalEvents(fromBlock uint64) (<-chan types.Log, error) {
	ctx := context.Background()
	approvalEventSignature := crypto.Keccak256Hash([]byte("Approval(address,address,uint256)"))
	query := ethereum.FilterQuery{
		FromBlock: big.NewInt(int64(fromBlock)),
		Addresses: []common.Address{t.contractAddress},
		Topics: [][]common.Hash{
			{approvalEventSignature},
		},
	}

	logs := make(chan types.Log)
	sub, err := t.ClientRead().SubscribeFilterLogs(ctx, query, logs)
	if err != nil {
		return nil, fmt.Errorf("failed to subscribe to logs: %v", err)
	}

	go func() {
		for {
			select {
			case err := <-sub.Err():
				log.Errorf("Subscription error: %v", err)
				return
			case <-ctx.Done():
				return
			}
		}
	}()

	return logs, nil
}

func (s *BEP20Token) Topics() []common.Hash {
	return []common.Hash{transferEventSignature}
}

// ScanLogs 扫描并解析区块日志中的 ERC-20 转账事件
// logs 是区块中的日志列表
// 返回转账记录列表和错误
func (s *BEP20Token) ParseLog(vLog types.Log) *struct {
	From  common.Address
	To    common.Address
	Value *big.Int
} {
	// Transfer 事件的签名
	// keccak256("Transfer(address,address,uint256)") = 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3cef
	// 遍历日志，解析 Transfer 事件
	// 确保日志来自目标合约且是 Transfer 事件
	if vLog.Address != s.contractAddress || len(vLog.Topics) != 3 || vLog.Topics[0] != transferEventSignature {
		return nil
	}
	// 解码事件数据
	event := struct {
		From  common.Address
		To    common.Address
		Value *big.Int
	}{}
	err := s.abi.UnpackIntoInterface(&event, "Transfer", vLog.Data)
	if err != nil {
		log.Error(err)
		return nil
	}
	// 从 Topics 中提取 indexed 参数
	if len(vLog.Topics) < 3 {
		return nil
	}
	event.From = common.HexToAddress(vLog.Topics[1].Hex())
	event.To = common.HexToAddress(vLog.Topics[2].Hex())
	return &event
}

// 实现ICoin接口
func (t *BEP20Token) CoinSymbol() pb.EnumCoinSymbol {
	return t.coinSymbol
}

// 实现ICoin接口
func (t *BEP20Token) CoinDecimals() int64 {
	return t.decimals
}

// 实现ICoin接口
func (t *BEP20Token) Coin() pb.EnumCoin {
	return t.coin
}

// 实现ICoin接口
func (t *BEP20Token) Balance(address string) (*big.Int, error) {
	return t.BalanceOf(address)
}

// 实现ICoin接口
func (t *BEP20Token) CollectionLimit() float64 {
	return 0.99
}

// 实现ICoin接口
func (t *BEP20Token) Chain() stypes.IChain {
	return t.chain
}

// 实现ICoin接口
func (t *BEP20Token) RechargeOpen() bool {
	return t.rechargeOpen
}

// 实现ICoin接口
func (t *BEP20Token) WithdrawOpen() bool {
	return t.withdrawOpen
}

// 实现ICoin接口
func (t *BEP20Token) AssetConfig() pb.CoinConfig {
	return t.config
}

func (t *BEP20Token) CreateOrder(arg map[string]any) (string, string, error) {
	return "", "", nil
}
