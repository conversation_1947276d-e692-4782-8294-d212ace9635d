package bsc

import (
	"context"
	"fmt"
	"math/big"
	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/jfcwrlight/core/conf"
)

// Native 定义了原生代币的接口
type Native interface {
	// 基本查询接口
	Balance(address string) (*big.Int, error)
	Nonce(address string) (uint64, error)
	GasPrice() (*big.Int, error)

	// 转账相关接口
	Transfer(fromPrivate string, to string, amount *big.Int) (string, error)
	EstimateGas(to string, amount *big.Int) (uint64, error)
}

// NativeToken 实现了原生代币接口
type NativeToken struct {
	chain           stypes.IChain
	collectionLimit float64
	rechargeOpen    bool
	withdrawOpen    bool
	config          pb.CoinConfig
}

// NewNativeToken 创建一个新的原生代币实例
func NewNativeToken(chain stypes.IChain) *NativeToken {
	return &NativeToken{
		chain:           chain,
		collectionLimit: conf.Num[float64]("chain.bsc.Native.collectionLimit", 0.99),
		rechargeOpen:    conf.Bool("chain.bsc.Native.rechargeOpen", false),
		withdrawOpen:    conf.Bool("chain.bsc.Native.withdrawOpen", false),
		config: pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.bsc.Native.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.bsc.Native.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.bsc.Native.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.bsc.Native.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.bsc.Native.MinRecharge", 0),
		},
	}
}

func (e *NativeToken) RechargeOpen() bool {
	return e.rechargeOpen
}

func (e *NativeToken) WithdrawOpen() bool {
	return e.withdrawOpen
}

func (e *NativeToken) AssetConfig() pb.CoinConfig {
	return e.config
}

func (e *NativeToken) ClientRead() *ethclient.Client {
	chain := e.chain.(*Chain)
	return chain.ClientRead()
}

func (e *NativeToken) ClientWrite() *ethclient.Client {
	chain := e.chain.(*Chain)
	return chain.ClientWrite()
}

func (e *NativeToken) ClientId() *big.Int {
	chain := e.chain.(*Chain)
	return chain.ClientId()
}

// Nonce 获取地址的 nonce
func (t *NativeToken) Nonce(address string) (uint64, error) {
	nonce, err := t.ClientWrite().PendingNonceAt(context.Background(), common.HexToAddress(address))
	if err != nil {
		return 0, fmt.Errorf("failed to get nonce: %v", err)
	}
	return nonce, nil
}

// GasPrice 获取当前 gas 价格
func (t *NativeToken) GasPrice() (*big.Int, error) {
	gasPrice, err := t.ClientWrite().SuggestGasPrice(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %v", err)
	}
	gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(12))
	gasPrice = new(big.Int).Div(gasPrice, big.NewInt(10))
	return gasPrice, nil
}

// Transfer 转账原生代币
func (t *NativeToken) Transfer(fromPrivate string, to string, amount *big.Int) (string, error) {
	// 获取发送者地址
	privateKey, err := crypto.HexToECDSA(fromPrivate)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)

	// 获取 nonce
	nonce, err := t.Nonce(fromAddress.Hex())
	if err != nil {
		return "", err
	}

	// 获取 gas 价格
	gasPrice, err := t.GasPrice()
	if err != nil {
		return "", err
	}

	// 估算 gas limit
	gasLimit, err := t.EstimateGas(to, amount)
	if err != nil {
		return "", err
	}

	// 创建交易
	tx := types.NewTransaction(
		nonce,
		common.HexToAddress(to),
		amount,
		gasLimit,
		gasPrice,
		nil, // 原生代币转账不需要 data
	)

	// 签名交易
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(big.NewInt(t.ClientId().Int64())), privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %v", err)
	}

	// 发送交易
	err = t.ClientWrite().SendTransaction(context.Background(), signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %v", err)
	}

	return signedTx.Hash().Hex(), nil
}

// EstimateGas 估算交易所需的 gas
func (t *NativeToken) EstimateGas(to string, amount *big.Int) (uint64, error) {
	toAddr := common.HexToAddress(to)
	msg := ethereum.CallMsg{
		To:    &toAddr,
		Value: amount,
	}
	gasLimit, err := t.ClientWrite().EstimateGas(context.Background(), msg)
	if err != nil {
		return 0, fmt.Errorf("failed to estimate gas: %v", err)
	}
	return gasLimit, nil
}

// 实现ICoin接口
func (t *NativeToken) CoinSymbol() pb.EnumCoinSymbol {
	return pb.BSCBNB
}

// 实现ICoin接口
func (t *NativeToken) CoinDecimals() int64 {
	return 1e18
}

// 实现ICoin接口
func (t *NativeToken) Coin() pb.EnumCoin {
	return pb.BNB
}

// 实现ICoin接口
func (t *NativeToken) CollectionLimit() float64 {
	return t.collectionLimit
}

// Balance 查询地址余额 实现ICoin接口
func (t *NativeToken) Balance(address string) (*big.Int, error) {
	balance, err := t.ClientWrite().BalanceAt(context.Background(), common.HexToAddress(address), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance: %v", err)
	}
	return balance, nil
}

// 实现ICoin接口
func (t *NativeToken) Chain() stypes.IChain {
	return t.chain
}

func (t *NativeToken) CreateOrder(arg map[string]any) (string, string, error) {
	return "", "", nil
}
