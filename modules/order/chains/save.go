package chains

import (
	"github.com/jfcwrlight/core/infra/mdb"
	"gorm.io/gorm"
)

// init 初始化数据库表（辅助函数，仅在程序启动时调用）
func init() {
	// 使用 AutoMigrate 自动创建或迁移 chain_latest_blocks 表
	err := mdb.Default().AutoMigrate(&ChainLatestScan{})
	if err != nil {
		panic(err)
	}
}

// ChainLatestScan 模型，定义 chain_latest_scan 表的结构
type ChainLatestScan struct {
	ChainName string `gorm:"primaryKey"` // 链名称，如 "eth"
	Info      string `gorm:"not null"`   // 最新扫描区块信息
}

// SaveLatestScanInfo 保存链的最新区块号到数据库
// 参数 db 是 GORM 数据库连接，ctx 用于控制操作的超时或取消
// chainName 是链的名称，blockNumber 是最新区块号
// 返回 error 表示操作是否成功
func SaveLatestScanInfo(chainName string, blockInfo string) error {
	// log.Infof("SaveLatestBlock %s %d", chainName, blockNumber)
	return mdb.Default().
		Save(&ChainLatestScan{
			ChainName: chainName,
			Info:      blockInfo,
		}).Error
}

// GetLastScanInfo 获取链的最新扫描区块号
// 参数 db 是 GORM 数据库连接，ctx 用于控制操作的超时或取消
// chainName 是链的名称
// 返回最新区块号和 error
func GetLastScanInfo(chainName string) (string, error) {
	var chainBlock ChainLatestScan
	result := mdb.Default().Where("chain_name = ?", chainName).First(&chainBlock)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return "", nil // 没有记录时返回 0
		}
		return "", result.Error
	}
	return chainBlock.Info, nil
}
