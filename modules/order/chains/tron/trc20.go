package tron

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"time"

	"s2/common/web3"
	"s2/modules/order/stypes"
	"s2/pb"

	"bytes"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/fbsobreira/gotron-sdk/pkg/common"
	coreproto "github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"google.golang.org/protobuf/proto"
)

// transferEventSig is the Keccak256 hash of Transfer(address,address,uint256)
var transferEventSig, _ = common.FromHex("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")

// TRC20Token implements TRC-20 token interface

// TRC20Token 实现 TRC-20 代币接口
// TODO: 使用 Tron SDK 完善代币查询与转账逻辑

type TRC20Token struct {
	client          *client.GrpcClient
	chain           stypes.IChain
	contractAddress string
	coinSymbol      pb.EnumCoinSymbol
	decimals        int64
	coin            pb.EnumCoin
	collectionLimit float64
	rechargeOpen    bool
	withdrawOpen    bool
	config          pb.CoinConfig
}

// NewTRC20Token 创建 TRC20 代币实例
func NewTRC20Token(cli *client.GrpcClient, chain stypes.IChain, contract string, coinSymbol pb.EnumCoinSymbol, decimals int64, coin pb.EnumCoin, limit float64, rechargeOpen bool, withdrawOpen bool, config pb.CoinConfig) *TRC20Token {
	return &TRC20Token{
		client:          cli,
		chain:           chain,
		contractAddress: contract,
		coinSymbol:      coinSymbol,
		decimals:        decimals,
		coin:            coin,
		collectionLimit: limit,
		rechargeOpen:    rechargeOpen,
		withdrawOpen:    withdrawOpen,
		config:          config,
	}
}

// Balance 查询 TRC20 代币余额
func (t *TRC20Token) Balance(address string) (*big.Int, error) {
	// 调用 SDK 获取合约余额
	bal, err := t.client.TRC20ContractBalance(address, t.contractAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to get TRC20 balance: %v", err)
	}
	return bal, nil
}

// Transfer 转账 TRC20 代币
func (t *TRC20Token) Transfer(privateKey string, to string, amount *big.Int) (string, error) {
	// derive from address
	fromAddr, err := web3.GetTRONAddressFromPrivateKey(privateKey)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build raw transaction
	txExt, err := t.client.TRC20Send(fromAddr, to, t.contractAddress, amount, 30000000)
	if err != nil {
		return "", fmt.Errorf("failed to build TRC20 send tx: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no transaction returned to sign")
	}
	// sign
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return "", fmt.Errorf("invalid private key for signing: %v", err)
	}
	sig, err := crypto.Sign(hash[:], privKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign tx: %v", err)
	}
	tx.Signature = [][]byte{sig}
	// broadcast
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := t.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("broadcast tx error: %v", err)
	}
	if !ret.GetResult() {
		return "", fmt.Errorf("tx rejected: %s", string(ret.GetMessage()))
	}
	txid := hex.EncodeToString(txExt.GetTxid())
	b, err := t.chain.(*Chain).WaitForTransaction(txid, 3*time.Minute, 5*time.Second)
	if !b || err != nil {
		return "", fmt.Errorf("Transfer tx failed: %v", err)
	}
	return txid, nil
}

// CoinSymbol 返回代币符号
func (t *TRC20Token) CoinSymbol() pb.EnumCoinSymbol {
	return t.coinSymbol
}

// CoinDecimals 返回小数位数
func (t *TRC20Token) CoinDecimals() int64 {
	return t.decimals
}

// Coin 返回 EnumCoin
func (t *TRC20Token) Coin() pb.EnumCoin {
	return t.coin
}

// CollectionLimit 返回触发归集的最小金额
func (t *TRC20Token) CollectionLimit() float64 {
	return t.collectionLimit
}

// Chain 返回所属链
func (t *TRC20Token) Chain() stypes.IChain {
	return t.chain
}

// Approve 授权spender使用代币
func (t *TRC20Token) Approve(privateKey string, spender string, amount *big.Int) (string, error) {
	// derive from address
	fromAddr, err := web3.GetTRONAddressFromPrivateKey(privateKey)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	txExt, err := t.client.TRC20Approve(fromAddr, spender, t.contractAddress, amount, 30000000)
	if err != nil {
		return "", fmt.Errorf("failed to build approve tx: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no transaction returned to sign")
	}
	// sign and broadcast like Transfer
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, _ := crypto.HexToECDSA(privateKey)
	sig, _ := crypto.Sign(hash[:], privKey)
	tx.Signature = [][]byte{sig}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := t.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil || !ret.GetResult() {
		return "", fmt.Errorf("approve tx error: %v %v", err, ret)
	}
	txid := hex.EncodeToString(txExt.GetTxid())
	b, err := t.chain.(*Chain).WaitForTransaction(txid, 3*time.Minute, 5*time.Second)
	if !b || err != nil {
		return "", fmt.Errorf("approve tx failed: %v", err)
	}
	return txid, nil
}

// Allowance 查询owner对spender的授权额度
func (t *TRC20Token) Allowance(owner string, spender string) (*big.Int, error) {
	// build allowance call data: signature + padded owner + padded spender
	ownerAddrB, err := address.Base58ToAddress(owner)
	if err != nil {
		return nil, fmt.Errorf("invalid owner address: %v", err)
	}
	spenderAddrB, err := address.Base58ToAddress(spender)
	if err != nil {
		return nil, fmt.Errorf("invalid spender address: %v", err)
	}
	const allowanceSignature = "0xdd62ed3e"
	ownerPadded := common.LeftPadBytes(ownerAddrB.Bytes(), 32)
	spenderPadded := common.LeftPadBytes(spenderAddrB.Bytes(), 32)
	data := allowanceSignature + common.Bytes2Hex(ownerPadded) + common.Bytes2Hex(spenderPadded)
	ext, err := t.client.TRC20Call("", t.contractAddress, data, true, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to call allowance: %v", err)
	}
	if len(ext.ConstantResult) == 0 {
		return nil, fmt.Errorf("no constant result for allowance")
	}
	hexStr := common.BytesToHexString(ext.ConstantResult[0])
	result, err := t.client.ParseTRC20NumericProperty(hexStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse allowance result: %v", err)
	}
	return result, nil
}

// TransferFrom 从from地址转账到to地址
func (t *TRC20Token) TransferFrom(privateKey string, from string, to string, amount *big.Int) (string, error) {
	// derive from address
	owner, err := web3.GetTRONAddressFromPrivateKey(privateKey)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build raw transaction
	txExt, err := t.client.TRC20TransferFrom(owner, from, to, t.contractAddress, amount, 80000000)
	if err != nil {
		return "", fmt.Errorf("failed to build transferFrom tx: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no transaction returned to sign")
	}
	// sign
	rawBytes, _ := proto.Marshal(tx.GetRawData())
	hash := sha256.Sum256(rawBytes)
	privKey, _ := crypto.HexToECDSA(privateKey)
	sig, _ := crypto.Sign(hash[:], privKey)
	tx.Signature = [][]byte{sig}
	// broadcast
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := t.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil || !ret.GetResult() {
		return "", fmt.Errorf("transferFrom tx error: %v %v", err, ret)
	}
	txid := hex.EncodeToString(txExt.GetTxid())
	b, err := t.chain.(*Chain).WaitForTransaction(txid, 3*time.Minute, 5*time.Second)
	if !b || err != nil {
		return "", fmt.Errorf("TransferFrom tx failed: %v", err)
	}
	return txid, nil
}

// ParseLog parses TRC20 Transfer event logs
func (t *TRC20Token) ParseLog(log *coreproto.TransactionInfo_Log) *struct {
	From  string
	To    string
	Value *big.Int
} {
	topics := log.GetTopics()
	// 需要至少三个 topics: event sig, from, to
	if len(topics) < 3 {
		return nil
	}
	// 仅处理 Transfer 事件
	if !bytes.Equal(topics[0], transferEventSig) {
		return nil
	}
	// topics[1]/[2] are left-padded 32-byte values; extract last 20 bytes and add TRON prefix
	if len(topics[1]) < 32 || len(topics[2]) < 32 {
		return nil
	}
	// Extract raw 20-byte addresses
	fromRaw := topics[1][12:32]
	toRaw := topics[2][12:32]
	// Build TRON Address (21 bytes: prefix + 20-byte hash)
	fromBytes := make([]byte, address.AddressLength)
	fromBytes[0] = address.TronBytePrefix
	copy(fromBytes[1:], fromRaw)
	from := address.Address(fromBytes).String()
	toBytes := make([]byte, address.AddressLength)
	toBytes[0] = address.TronBytePrefix
	copy(toBytes[1:], toRaw)
	to := address.Address(toBytes).String()
	value := new(big.Int).SetBytes(log.GetData())
	return &struct {
		From  string
		To    string
		Value *big.Int
	}{
		From:  from,
		To:    to,
		Value: value,
	}
}

func (t *TRC20Token) RechargeOpen() bool {
	return t.rechargeOpen
}

func (t *TRC20Token) WithdrawOpen() bool {
	return t.withdrawOpen
}

func (t *TRC20Token) AssetConfig() pb.CoinConfig {
	return t.config
}

func (t *TRC20Token) CreateOrder(arg map[string]any) (string, string, error) {
	return "", "", nil
}
