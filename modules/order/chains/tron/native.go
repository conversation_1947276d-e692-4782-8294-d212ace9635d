package tron

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"time"

	"s2/modules/order/stypes"
	"s2/pb"

	"s2/common/web3"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/jfcwrlight/core/conf"
	"google.golang.org/protobuf/proto"
)

// NativeToken implements native TRX token interface
// TODO: use Tron SDK to query balance and transfer transactions

// NativeToken 包含 gRPC 客户端用于与 TRON 网络交互
type NativeToken struct {
	client          *client.GrpcClient
	chain           stypes.IChain
	collectionLimit float64
	rechargeOpen    bool
	withdrawOpen    bool
	config          pb.CoinConfig
}

// NewNativeToken 创建一个新的 TRX 原生代币实例
func NewNativeToken(cli *client.GrpcClient, chain stypes.IChain) *NativeToken {
	return &NativeToken{
		client:          cli,
		chain:           chain,
		collectionLimit: conf.Num[float64]("chain.tron.Native.collectionLimit", 0.99),
		rechargeOpen:    conf.Bool("chain.tron.Native.rechargeOpen", false),
		withdrawOpen:    conf.Bool("chain.tron.Native.withdrawOpen", false),
		config: pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.tron.Native.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.tron.Native.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.tron.Native.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.tron.Native.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.tron.Native.MinRecharge", 0),
		},
	}
}

// Balance 查询地址余额，单位为 SUN (1 TRX = 1e6 SUN)
func (t *NativeToken) Balance(address string) (*big.Int, error) {
	acct, err := t.client.GetAccountDetailed(address)
	if err != nil {
		if err.Error() == "account not found" {
			return big.NewInt(0), nil
		}
		return nil, fmt.Errorf("failed to get account: %v", err)
	}
	// Balance 字段以 SUN 为单位
	return big.NewInt(acct.Balance), nil
}

// Transfer 从私钥派生的地址向指定目标转账 TRX
func (t *NativeToken) Transfer(privateKey string, to string, amount *big.Int) (string, error) {
	// derive Base58 address
	fromAddr, err := web3.GetTRONAddressFromPrivateKey(privateKey)
	if err != nil {
		return "", fmt.Errorf("invalid private key: %v", err)
	}
	// build raw transaction
	txExt, err := t.client.Transfer(fromAddr, to, amount.Int64())
	if err != nil {
		return "", fmt.Errorf("failed to build transaction: %v", err)
	}
	tx := txExt.GetTransaction()
	if tx == nil {
		return "", fmt.Errorf("no transaction returned to sign")
	}
	// sign raw data
	rawData := tx.GetRawData()
	rawBytes, err := proto.Marshal(rawData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal raw data: %v", err)
	}
	hash := sha256.Sum256(rawBytes)
	privKey, err := crypto.HexToECDSA(privateKey)
	if err != nil {
		return "", fmt.Errorf("invalid private key for signing: %v", err)
	}
	sig, err := crypto.Sign(hash[:], privKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %v", err)
	}
	tx.Signature = [][]byte{sig}
	// broadcast signed transaction
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	ret, err := t.client.Client.BroadcastTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("broadcast transaction error: %v", err)
	}
	if !ret.GetResult() {
		return "", fmt.Errorf("transaction rejected: %s", string(ret.GetMessage()))
	}
	// Txid 为原始字节，需要 hex 编码
	txid := hex.EncodeToString(txExt.GetTxid())
	b, err := t.chain.(*Chain).WaitForTransaction(txid, 3*time.Minute, 5*time.Second)
	if !b || err != nil {
		return "", fmt.Errorf("NativeToken Transfer tx failed: %v", err)
	}
	return txid, nil
}

// CoinSymbol 返回原生币符号
func (t *NativeToken) CoinSymbol() pb.EnumCoinSymbol {
	return pb.TRONTRX
}

// CoinDecimals 返回最小单位换算
func (t *NativeToken) CoinDecimals() int64 {
	return 1e6
}

// Coin 返回枚举类型 TRX
func (t *NativeToken) Coin() pb.EnumCoin {
	return pb.TRX
}

// CollectionLimit 返回触发归集的最小金额
func (t *NativeToken) CollectionLimit() float64 {
	return t.collectionLimit
}

// Chain 返回所属链实例
func (t *NativeToken) Chain() stypes.IChain {
	return t.chain
}

func (t *NativeToken) RechargeOpen() bool {
	return t.rechargeOpen
}

func (t *NativeToken) WithdrawOpen() bool {
	return t.withdrawOpen
}

func (t *NativeToken) AssetConfig() pb.CoinConfig {
	return t.config
}

func (t *NativeToken) CreateOrder(arg map[string]any) (string, string, error) {
	return "", "", nil
}
