package okpay

import (
	"errors"
	"math/big"
	"strconv"

	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/jfcwrlight/core/log"
	// "github.com/xssnick/tonutils-go/ton" // TON SDK, TODO: Uncomment and use actual types
)

type OkPayCoin struct {
	coinSymbol   pb.EnumCoinSymbol
	decimals     int64
	coin         pb.EnumCoin
	chain        stypes.IChain
	rechargeOpen bool
	withdrawOpen bool
	config       pb.CoinConfig
}

func NewOkPayCoin(chain stypes.IChain, coinSymbol pb.EnumCoinSymbol, coin pb.EnumCoin, rechargeOpen bool, withdrawOpen bool, config pb.CoinConfig) *OkPayCoin {
	return &OkPayCoin{
		coinSymbol:   coinSymbol,
		decimals:     1,
		coin:         coin,
		chain:        chain,
		rechargeOpen: rechargeOpen,
		withdrawOpen: withdrawOpen,
		config:       config,
	}
}

func (t *OkPayCoin) Balance(addr string) (*big.Int, error) {
	return nil, nil
}

// Transfer
func (t *OkPayCoin) Transfer(words string, to string, amount *big.Int) (string, error) {
	return "", nil
}

// CoinSymbol 返回代币符号
func (t *OkPayCoin) CoinSymbol() pb.EnumCoinSymbol {
	return t.coinSymbol
}

// 创建订单
func (t *OkPayCoin) CreateOrder(arg map[string]any) (string, string, error) {
	chain := t.chain.(*Chain)
	data := map[string]string{
		"unique_id":  arg["orderId"].(string),
		"name":       chain.name,
		"amount":     strconv.FormatFloat(arg["amount"].(float64), 'f', -1, 64),
		"return_url": chain.return_url,
		"coin":       pb.EnumCoin_name[int32(t.coin)],
	}
	response, err := chain.client.PayLink(data)
	if err != nil {
		log.Error("通道维护，下单失败")
		return "", "", errors.New("Channel maintenance")
	}
	responseMap, ok := response.(map[string]interface{})
	if !ok {
		log.Error("response is not a map:", response)
		return "", "", errors.New("Channel maintenance")
	}
	responseData, ok := responseMap["data"].(map[string]interface{})
	if !ok {
		log.Error("response data is not a map:", responseMap)
		return "", "", errors.New("Channel maintenance")
	}
	order_id, ok := responseData["order_id"].(string)
	if !ok {
		log.Error("response data is not a map:", responseMap)
		return "", "", errors.New("Channel maintenance")
	}
	pay_url, ok := responseData["pay_url"].(string)
	if !ok {
		log.Error("response data is not a map:", responseMap)
		return "", "", errors.New("Channel maintenance")
	}
	return pay_url, order_id, nil
}

// CoinDecimals 返回小数位数
func (t *OkPayCoin) CoinDecimals() int64 {
	return t.decimals
}

// Coin 返回 EnumCoin
func (t *OkPayCoin) Coin() pb.EnumCoin {
	return t.coin
}

// CollectionLimit 返回触发归集的最小金额
func (t *OkPayCoin) CollectionLimit() float64 {
	return 0
}

// Chain 返回所属链
func (t *OkPayCoin) Chain() stypes.IChain {
	return t.chain
}

func (t *OkPayCoin) RechargeOpen() bool {
	return t.rechargeOpen
}

func (t *OkPayCoin) WithdrawOpen() bool {
	return t.withdrawOpen
}

func (t *OkPayCoin) AssetConfig() pb.CoinConfig {
	return t.config
}
