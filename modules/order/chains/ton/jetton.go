package ton

import (
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"

	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/xssnick/tonutils-go/address"
	"github.com/xssnick/tonutils-go/tlb"
	"github.com/xssnick/tonutils-go/ton"
	sdkjetton "github.com/xssnick/tonutils-go/ton/jetton"
	sdkwallet "github.com/xssnick/tonutils-go/ton/wallet"
	// "github.com/xssnick/tonutils-go/ton" // TON SDK, TODO: Uncomment and use actual types
)

// JettonToken 实现 TIP-3 代币接口
// TODO: 使用 tonutils-go SDK 完善代币查询与转账逻辑

type JettonToken struct {
	client          interface{} // TON SDK 客户端实例
	contractAddress string
	coinSymbol      pb.EnumCoinSymbol
	decimals        int64
	coin            pb.EnumCoin
	collectionLimit float64
	chain           stypes.IChain
	walletAddress   *address.Address // 对应 collectionAddress 的 TokenWallet 地址
	rechargeOpen    bool
	withdrawOpen    bool
	config          pb.CoinConfig
}

// NewJettonToken 创建 Jetton 代币实例
func NewJettonToken(client interface{}, chain stypes.IChain, contractAddress string, coinSymbol pb.EnumCoinSymbol, decimals int64, coin pb.EnumCoin, collectionLimit float64, rechargeOpen bool, withdrawOpen bool, config pb.CoinConfig) *JettonToken {
	return &JettonToken{
		client:          client,
		contractAddress: contractAddress,
		coinSymbol:      coinSymbol,
		decimals:        decimals,
		coin:            coin,
		collectionLimit: collectionLimit,
		chain:           chain,
		walletAddress:   nil,
		rechargeOpen:    rechargeOpen,
		withdrawOpen:    withdrawOpen,
		config:          config,
	}
}

// Balance 查询代币余额
func (t *JettonToken) Balance(addr string) (*big.Int, error) {
	// 将 t.client 断言为 TON API 客户端
	api, ok := t.client.(ton.APIClientWrapped)
	if !ok {
		return nil, fmt.Errorf("invalid TON client for JettonToken")
	}
	// 创建 Master 合约客户端
	masterCli := sdkjetton.NewJettonMasterClient(api, address.MustParseAddr(t.contractAddress))
	// 获取 owner 对应的钱包客户端
	wallet, err := masterCli.GetJettonWallet(context.Background(), address.MustParseAddr(addr))
	if err != nil {
		return nil, err
	}
	// 查询余额
	return wallet.GetBalance(context.Background())
}

// Transfer 转账代币，使用助记词签名
func (t *JettonToken) Transfer(words string, to string, amount *big.Int) (string, error) {
	api, ok := t.client.(ton.APIClientWrapped)
	if !ok {
		return "", fmt.Errorf("invalid TON client for JettonToken")
	}
	// 从助记词派生钱包
	seedWords := strings.Split(words, " ")
	w, err := sdkwallet.FromSeed(api, seedWords, sdkwallet.Unknown)
	if err != nil {
		return "", fmt.Errorf("invalid mnemonic: %w", err)
	}
	ownerAddr := w.WalletAddress()
	// 构造 Jetton Master 客户端并获取发送者的 TokenWallet
	masterCli := sdkjetton.NewJettonMasterClient(api, address.MustParseAddr(t.contractAddress))
	jettonWallet, err := masterCli.GetJettonWallet(context.Background(), ownerAddr)
	if err != nil {
		return "", fmt.Errorf("failed to get jetton wallet: %w", err)
	}
	// 目标地址
	toAddr := address.MustParseAddr(to)
	// 金额转换
	amountCoins, err := tlb.FromNano(amount, int(t.decimals))
	if err != nil {
		return "", fmt.Errorf("invalid amount: %w", err)
	}
	// 构建 Jetton 转账 payload
	payload, err := jettonWallet.BuildTransferPayloadV2(toAddr, toAddr, amountCoins, tlb.ZeroCoins, nil, nil)
	if err != nil {
		return "", fmt.Errorf("failed to build transfer payload: %w", err)
	}
	// your TON balance must be > 0.05 to send
	// 使用 TON 钱包发送内部消息
	msg := &sdkwallet.Message{
		Mode: sdkwallet.PayGasSeparately + sdkwallet.IgnoreErrors,
		InternalMessage: &tlb.InternalMessage{
			IHRDisabled: true,
			Bounce:      false,
			DstAddr:     jettonWallet.Address(),
			Amount:      tlb.ZeroCoins,
			Body:        payload,
		},
	}
	// 签名并发送交易
	tx, _, err := w.SendWaitTransaction(context.Background(), msg)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}
	return hex.EncodeToString(tx.Hash), nil
}

// CoinSymbol 返回代币符号
func (t *JettonToken) CoinSymbol() pb.EnumCoinSymbol {
	return t.coinSymbol
}

// CoinDecimals 返回小数位数
func (t *JettonToken) CoinDecimals() int64 {
	return t.decimals
}

// Coin 返回 EnumCoin
func (t *JettonToken) Coin() pb.EnumCoin {
	return t.coin
}

// CollectionLimit 返回触发归集的最小金额
func (t *JettonToken) CollectionLimit() float64 {
	return t.collectionLimit
}

// Chain 返回所属链
func (t *JettonToken) Chain() stypes.IChain {
	return t.chain
}

func (t *JettonToken) RechargeOpen() bool {
	return t.rechargeOpen
}

func (t *JettonToken) WithdrawOpen() bool {
	return t.withdrawOpen
}

func (t *JettonToken) AssetConfig() pb.CoinConfig {
	return t.config
}

func (t *JettonToken) CreateOrder(arg map[string]any) (string, string, error) {
	return "", "", nil
}
