package ton

import (
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"

	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/jfcwrlight/core/conf"
	"github.com/xssnick/tonutils-go/address"
	"github.com/xssnick/tonutils-go/tlb"
	"github.com/xssnick/tonutils-go/ton"
	sdkwallet "github.com/xssnick/tonutils-go/ton/wallet"
)

// NativeToken 实现 TON 原生代币接口
// TODO: 使用 tonutils-go SDK 实现查询和转账逻辑

type NativeToken struct {
	client       interface{} // TON SDK 客户端实例
	chain        stypes.IChain
	rechargeOpen bool
	withdrawOpen bool
	config       pb.CoinConfig
}

// NewNativeToken 创建原生 TON 代币实例
func NewNativeToken(client interface{}, chain stypes.IChain) *NativeToken {
	return &NativeToken{
		client:       client,
		chain:        chain,
		rechargeOpen: conf.Bool("chain.ton.Native.rechargeOpen", false),
		withdrawOpen: conf.Bool("chain.ton.Native.withdrawOpen", false),
		config: pb.CoinConfig{
			AssetId:      int64(conf.Num[int]("chain.ton.Native.AssetId", 0)),
			ExchangeRate: conf.Num[float64]("chain.ton.Native.AssetExchangeRate", 0),
			Fees:         conf.Num[float64]("chain.ton.Native.Fees", 0),
			MinWithdraw:  conf.Num[float64]("chain.ton.Native.MinWithdraw", 0),
			MinRecharge:  conf.Num[float64]("chain.ton.Native.MinRecharge", 0),
		},
	}
}

// Balance 查询地址余额，单位为 nanoton
func (t *NativeToken) Balance(addressStr string) (*big.Int, error) {
	api, ok := t.client.(ton.APIClientWrapped)
	if !ok {
		return nil, fmt.Errorf("invalid TON client for NativeToken")
	}
	ctx := context.Background()
	master, err := api.CurrentMasterchainInfo(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get masterchain info: %w", err)
	}
	account, err := api.WaitForBlock(master.SeqNo).GetAccount(ctx, master, address.MustParseAddr(addressStr))
	if err != nil {
		return nil, fmt.Errorf("failed to get account state: %w", err)
	}
	return account.State.Balance.Nano(), nil
}

// Transfer 转账原生 TON，words 为发送方助记词
func (t *NativeToken) Transfer(words string, toStr string, amount *big.Int) (string, error) {
	api, ok := t.client.(ton.APIClientWrapped)
	if !ok {
		return "", fmt.Errorf("invalid TON client for NativeToken")
	}
	// 从助记词派生钱包（使用 V3R2 版本）
	seedWords := strings.Split(words, " ")
	w, err := sdkwallet.FromSeed(api, seedWords, sdkwallet.V3R2)
	if err != nil {
		return "", fmt.Errorf("invalid mnemonic: %w", err)
	}
	// 目标地址
	toAddr := address.MustParseAddr(toStr)
	// 金额转换到 Coins
	coins := tlb.FromNanoTON(amount)
	// 构建转账消息
	msg, err := w.BuildTransfer(toAddr, coins, false, "")
	if err != nil {
		return "", fmt.Errorf("failed to build transfer message: %w", err)
	}
	// 发送并等待交易
	tx, _, err := w.SendWaitTransaction(context.Background(), msg)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}
	return hex.EncodeToString(tx.Hash), nil
}

// CoinSymbol 返回原生币符号
func (t *NativeToken) CoinSymbol() pb.EnumCoinSymbol {
	return pb.TONTON
}

// CoinDecimals 返回原生币小数位
func (t *NativeToken) CoinDecimals() int64 {
	return 1e9
}

// Coin 返回 EnumCoin
func (t *NativeToken) Coin() pb.EnumCoin {
	return pb.TON
}

// CollectionLimit 返回触发归集的最小金额
func (t *NativeToken) CollectionLimit() float64 {
	return 0
}

// Chain 返回所属链
func (t *NativeToken) Chain() stypes.IChain {
	return t.chain
}

func (t *NativeToken) RechargeOpen() bool {
	return t.rechargeOpen
}

func (t *NativeToken) WithdrawOpen() bool {
	return t.withdrawOpen
}

func (t *NativeToken) AssetConfig() pb.CoinConfig {
	return t.config
}

func (t *NativeToken) CreateOrder(arg map[string]any) (string, string, error) {
	return "", "", nil
}
