package stypes

import (
	"math/big"
	"s2/pb"
)

type ICoin interface {
	CoinSymbol() pb.EnumCoinSymbol
	Coin() pb.EnumCoin
	CoinDecimals() int64
	Balance(address string) (*big.Int, error)
	// 归集的触发界限
	CollectionLimit() float64
	Chain() IChain
	RechargeOpen() bool // 是否开启充值
	WithdrawOpen() bool // 是否开启提现
	// 获取资源ID和兑换比例
	AssetConfig() pb.CoinConfig
	Transfer(privateKey string, to string, amount *big.Int) (string, error)
	CreateOrder(arg map[string]any) (string, string, error)
}
