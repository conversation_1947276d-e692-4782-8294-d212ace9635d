package stypes

import (
	"math/big"
	"s2/pb"
)

type EventTransfer struct {
	TxHash    string
	Coin      pb.EnumCoinSymbol
	Chain     pb.EnumChain
	From      string
	To        string
	Value     *big.Int
	Timestamp uint64
}

type TONEventTransfer struct {
	TxHash    string
	Coin      pb.EnumCoinSymbol
	Chain     pb.EnumChain
	From      string
	Uid       int64
	Value     *big.Int
	Timestamp uint64
}

func (EventTransfer) Name() string {
	return "transfer"
}
func (TONEventTransfer) Name() string {
	return "ton_transfer"
}
