package stypes

import (
	"context"
	"math/big"
	"s2/pb"
)

// IChain 定义了一个区块链的核心接口
type IChain interface {
	Type() pb.EnumChain
	// StartScan 启动区块链扫描
	// ctx 用于控制扫描的生命周期，blockNumber 指定起始区块号（如果为 0，则从最新区块开始）
	StartScan(ctx context.Context) error
	// Close 关闭链连接和清理资源
	Close() error
	// 归集
	Collection(coin ICoin, private string) (*big.Int, error)
	// 提款
	WithdrawOrder(amount float64, coin ICoin, address string) error
	GetUserRechargeAddress(userID int64) (address string, extInfo string, err error)
	GetUserRechargePrivate(userID int64) (private string, err error)
	//官方地址，在扫描的时候，from 和 to 过滤这些地址
	IsOfficialAddress(address string) bool
}
