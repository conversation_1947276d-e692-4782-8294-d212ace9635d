package order

import (
	"fmt"
	"sync"
	"time"

	"s2/common/cache"
	"s2/modules/order/stypes"
	"s2/pb"

	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/shopspring/decimal"
	"gorm.io/gorm/clause"
)

// 全局索引，Lua 里对应 gameOrderIdx
var (
	gameOrderIdx      int64
	gameOrderIdxMutex sync.Mutex
)

// CreateOrderId 把 Lua 版本的 CreateOrderId 翻译成 Go。
// uid 对应 Lua 的 uid，lobbyId 暂时没用到，和原代码里一样固定 “AAA”。
func CreateOrderId(uid int64) string {
	gameOrderIdxMutex.Lock()
	defer gameOrderIdxMutex.Unlock()
	// 1. 固定的游戏 ID 前缀
	strGameid := "AAA"
	// 2. 把 uid 格式化成 11 位，不足前面补 0
	strUid := fmt.Sprintf("%011d", uid)
	// 3. 当前秒数，格式化成 10 位
	strTimer := fmt.Sprintf("%010d", time.Now().Unix())
	// 4. 当前索引，格式化成 5 位
	strIdx := fmt.Sprintf("%05d", gameOrderIdx)
	// 5. 拼接出最终的 orderID
	orderID := strGameid + strUid + strTimer + strIdx
	// 6. 索引自增，下一次调用就能用到新值
	gameOrderIdx++
	return orderID
}
func ChangeRechargeOrderPending(orderId string) (*RechargeOrder, error) {
	orders := []*RechargeOrder{}
	tx := mdb.Default().TxReadCommit()
	defer mdb.RecoverWithRollback(tx)
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("tx_hash = ?", orderId).Find(&orders).Error
	if err != nil {
		tx.Rollback()
		return nil, err
	}
	if len(orders) == 0 {
		tx.Rollback()
		return nil, fmt.Errorf("order %s not found", orderId)
	}
	order := orders[0]
	// 检查订单状态
	if order.Status != int32(pb.OrderStatus_CHECKING) {
		tx.Rollback()
		return nil, fmt.Errorf("ChangeRechargeOrderPending order %s is already processed with status %s", orderId, order.Status)
	}
	order.Status = int32(pb.OrderStatus_PENDING)
	err = tx.Table(confirmedRechargeTableName(time.Unix(int64(order.Timestamp), 0))).Create(&order).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}
	err = tx.Delete(order).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to delete order: %w", err)
	}
	tx.Commit()
	uc, err := cache.QueryUserBasicInfo(order.UserID)
	if err == nil {
		message.Stream.Cast(uc.ServerID, &pb.UserRechargeMsg{
			UserID: order.UserID,
			TxHash: order.TxHash,
		})
	}
	return order, nil
}
func NewRechargeOrderChecking(orderId string, uid int64, chain stypes.IChain, coin stypes.ICoin, amount string, platOrderId string) (*RechargeOrder, error) {
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		return nil, fmt.Errorf("NewRechargeOrderChecking failed to query user basic info:uid: %d, txHash: %s, %w", uid, orderId, err)
	}
	timestamp := uint64(time.Now().Unix())
	order := RechargeOrder{
		TxHash:      orderId,
		UserID:      uid,
		Amount:      amount, // 转换为字符串存储
		Decimals:    coin.CoinDecimals(),
		CoinType:    coin.CoinSymbol().String(),
		CoinId:      int32(coin.Coin()),
		Status:      int32(pb.OrderStatus_CHECKING),
		Chain:       chain.Type().String(),
		PlatOrderId: platOrderId,
		Timestamp:   timestamp,
		ParentID:    bc.ParentID,
		Channel:     bc.Channel,
		CreatedAt:   time.Unix(int64(timestamp), 0),
	}
	config := coin.AssetConfig()
	amountdec, err := decimal.NewFromString(order.Amount)
	if err != nil {
		log.Error(err)
		return nil, fmt.Errorf("failed to parse amount: %w", err)
	}
	amountdec = amountdec.Div(decimal.NewFromInt(order.Decimals))
	amountdec = amountdec.Mul(decimal.NewFromFloat(config.ExchangeRate))
	order.AssetAmount = amountdec.InexactFloat64()
	order.AssetId = int32(config.AssetId)
	if err := mdb.Default().Create(&order).Error; err != nil {
		return nil, fmt.Errorf("failed to create recharge order: %w", err)
	}
	return &order, nil
}

// NewRechargeOrder 创建一个新订单，通知业务服务器，等待结算
// 参数 uid 是用户 ID，amount 是充值金额（*big.Int），coinType 是货币类型
// 返回订单 ID 和错误
func NewRechargeOrder(uid int64, chain stypes.IChain, coin stypes.ICoin, address string, amount string, txHash string, timestamp uint64) error {
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		return fmt.Errorf("NewRechargeOrder failed to query user basic info:uid: %d, txHash: %s, %w", uid, txHash, err)
	}
	// 创建订单记录
	order := RechargeOrder{
		TxHash:    txHash,
		UserID:    uid,
		Amount:    amount, // 转换为字符串存储
		Address:   address,
		Decimals:  coin.CoinDecimals(),
		CoinType:  coin.CoinSymbol().String(),
		CoinId:    int32(coin.Coin()),
		Status:    int32(pb.OrderStatus_PENDING),
		Chain:     chain.Type().String(),
		Timestamp: timestamp,
		ParentID:  bc.ParentID,
		Channel:   bc.Channel,
		CreatedAt: time.Unix(int64(timestamp), 0),
	}
	config := coin.AssetConfig()
	amountdec, err := decimal.NewFromString(order.Amount)
	if err != nil {
		log.Error(err)
		return fmt.Errorf("failed to parse amount: %w", err)
	}
	amountdec = amountdec.Div(decimal.NewFromInt(order.Decimals))
	amountdec = amountdec.Mul(decimal.NewFromFloat(config.ExchangeRate))
	order.AssetAmount = amountdec.InexactFloat64()
	order.AssetId = int32(config.AssetId)
	log.Infof("newRechargeOrder created: UserID=%d, CoinType=%s, Address=%s, Amount=%s, TxHash=%s", uid, coin.CoinSymbol().String(), address, amount, txHash)
	// 使用 mdb.Default() 保存订单到数据库
	if err := mdb.Default().Create(&order).Error; err != nil {
		return fmt.Errorf("failed to create recharge order: %w", err)
	}
	return nil
}

// CheckAndSubmitOrder 业务服务器 RPC 请求核对并提交订单
func CheckAndSubmitRechargeOrder(txHash string) (*RechargeOrder, error) {
	// 查询订单
	orders := []*RechargeOrder{}
	tx := mdb.Default().TxReadCommit()
	defer mdb.RecoverWithRollback(tx)
	err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("tx_hash = ?", txHash).Find(&orders).Error
	if err != nil {
		tx.Rollback()
		return nil, err
	}
	if len(orders) == 0 {
		tx.Rollback()
		return nil, fmt.Errorf("order %s not found", txHash)
	}
	order := orders[0]
	// 检查订单状态
	if order.Status != int32(pb.OrderStatus_PENDING) {
		tx.Rollback()
		return nil, fmt.Errorf("order %s is already processed with status %s", txHash, order.Status)
	}
	// 更新订单状态为 Confirmed
	order.Status = int32(pb.OrderStatus_CONFIRMED)
	err = mdb.Default().Table(confirmedRechargeTableName(time.Unix(int64(order.Timestamp), 0))).Create(&order).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}
	err = tx.Delete(order).Error //如果delete可能导致重复下单
	// err = tx.Updates(order).Error
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to delete order: %w", err)
	}
	tx.Commit()
	log.Infof("Order %s has been confirmed and moved to the confirmed orders table", txHash)
	return order, nil
}

// 启动的补发未确认的订单
func ResendRechargeOrder() {
	var orders []*RechargeOrder
	query := mdb.Default().Model(&RechargeOrder{})
	query = query.Where("status = ?", pb.OrderStatus_PENDING)
	query = query.Where("created_at >= ?", time.Now().Add(-time.Hour*24*7))
	if err := query.Find(&orders).Error; err != nil {
		return
	}
	for _, order := range orders {
		uc, err := cache.QueryUserBasicInfo(order.UserID)
		if err != nil {
			return
		}
		message.Stream.Cast(uc.ServerID, &pb.UserRechargeMsg{
			UserID: uc.ID,
			TxHash: order.TxHash,
		})
	}
}
