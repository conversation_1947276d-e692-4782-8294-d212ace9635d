package blockhash

import (
	"s2/modules/blockhash/scan"
	"s2/pb"

	"github.com/jfcwrlight/core/message"
)

// onPullBlockHashMsg 处理 PullBlockHashMsg 消息，向指定服务器发送最新的区块哈希更新
func (m *module) onPullBlockHashMsg(body *pb.PullBlockHashMsg) {
	// 构造 UpdateBlockHashMsg 消息，使用 blockhash.NewestALL() 获取最新区块哈希
	updateMsg := &pb.UpdateBlockHashMsg{
		Updates: scan.NewestALL(),
	}

	// 向指定服务器发送消息
	message.Cast(body.ServerID, updateMsg)
}

// onQueryHashByIDReq 处理 QueryHashByIDReq 请求，根据 ID 查询哈希并返回结果
func (m *module) onQueryHashByIDReq(body *pb.QueryHashByIDReq, response func(*pb.QueryHashByIDResp, error)) {
	// 根据 Name 和 ID 查询哈希
	hash := scan.QueryByID(body.Chain, body.ID)

	// 构造响应消息
	resp := &pb.QueryHashByIDResp{Hash: hash}

	// 返回响应
	response(resp, nil)
}
