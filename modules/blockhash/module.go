package blockhash

import (
	"fmt"

	"s2/define"
	"s2/modules/blockhash/scan"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

// module 是 scan 模块的实现，实现了 types.IModule 接口
type module struct {
	iface.IModule
}

// New 创建并返回一个新的 scan 模块实例
func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	return m
}

// Name 返回模块的名称
func (m module) Name() string {
	return define.ModuleName.BlockHash
}

// Init 初始化 scan 模块，设置依赖、注册消息处理器并启动监听
func (m *module) Init() error {
	if err := scan.Init(); err != nil {
		log.Error(err)
		return fmt.Errorf("failed to initialize blockhash: %v", err)
	}
	message.Handle(m, m.onPullBlockHashMsg)
	message.Response(m, m.onQueryHashByIDReq)
	return nil
}

func (m *module) Run() error {
	return m.IModule.Run()
}
