package scan

import (
	"s2/define"
	"s2/pb"
	"sync"
	"time"

	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
	"golang.org/x/exp/maps"
)

type module interface {
	Type() pb.EnumChain
	Latest() (int64, string, error) // 区块号, 区块hash, error
}

type manage struct {
	mods   map[pb.EnumChain]module
	wait   map[pb.EnumChain]time.Duration
	newest map[pb.EnumChain]*pb.BlockHash
	mtx    sync.Mutex
}

var manager = &manage{
	mods:   map[pb.EnumChain]module{},
	wait:   map[pb.EnumChain]time.Duration{},
	newest: map[pb.EnumChain]*pb.BlockHash{},
}

func (m *manage) reg(mod module, wait time.Duration) bool {
	typ := mod.Type()
	m.mods[typ] = mod
	m.wait[typ] = wait
	return true
}

func Init() error {
	last, err := GetLast(pb.CHAIN_OPBNB)
	if err != nil && last != nil {
		message.Broadcast(define.ModuleName.Game, pb.UpdateBlockHashMsg{
			Updates: []*pb.BlockHash{&pb.BlockHash{Chain: pb.CHAIN_OPBNB, Number: last.ID, Hash: last.Hash}},
		})
	}
	for typ := range manager.mods {
		err := mdb.Default().Table(TableName(typ)).AutoMigrate(&model{})
		if err != nil {
			return err
		}
	}
	for name := range manager.mods {
		conc.Go(func() {
			ticker := time.NewTicker(manager.wait[name])
			defer ticker.Stop()
			for {
				select {
				case <-system.RootCtx().Done():
					return
				case <-ticker.C:
					manager.doUpdate(name)
				}
			}
		})
	}
	return nil
}

func (m *manage) doUpdate(chain pb.EnumChain) {
	defer utils.RecoverPanic()
	mod := m.mods[chain]
	num, hash, err := mod.Latest()
	if err != nil {
		log.Error(err)
		return
	}
	newest := &pb.BlockHash{Chain: chain, Number: num, Hash: hash, UpdateAt: time.Now().Unix()}
	manager.mtx.Lock()
	old := manager.newest[chain]
	if old == nil || old.Number != newest.Number {
		manager.newest[chain] = newest
	}
	manager.mtx.Unlock()
	if old != nil && old.Number == newest.Number {
		return
	}
	err = mdb.Default().Table(TableName(chain)).Create(&model{ID: num, Hash: hash}).Error
	if err != nil {
		log.Error(err)
		return
	}
	message.Broadcast(define.ModuleName.Game, pb.UpdateBlockHashMsg{
		Updates: []*pb.BlockHash{newest},
	})
}

func Newest(chain pb.EnumChain) (*pb.BlockHash, bool) {
	manager.mtx.Lock()
	defer manager.mtx.Unlock()
	hash, ok := manager.newest[chain]
	return hash, ok
}

func NewestALL() []*pb.BlockHash {
	manager.mtx.Lock()
	defer manager.mtx.Unlock()
	return maps.Values(manager.newest)
}

func QueryByID(chain pb.EnumChain, id int64) string {
	m := model{}
	mdb.Default().Table(TableName(chain)).First(&m)
	return m.Hash
}

func GetLast(chain pb.EnumChain) (*model, error) {
	m := &model{}
	err := mdb.Default().Table(TableName(chain)).Last(m).Error
	if err != nil {
		return nil, err
	}
	return m, nil
}
