package scan

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"s2/pb"
	"strconv"
	"strings"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/utils/hs"
)

var OPBNB = newOPBNBScan()

type opBNB struct {
	APIKey_BSC        string
	APIKey_Chainstack string
	index             int
}

func newOPBNBScan() *opBNB {
	scan := &opBNB{APIKey_BSC: conf.Str("opbnb.APIKey_BSC", ""), APIKey_Chainstack: conf.Str("opbnb.APIKey_Chainstack", "")}
	return scan
}

func (scan *opBNB) Type() pb.EnumChain {
	return pb.CHAIN_OPBNB
}

// https://console.chainstack.com/
func (scan *opBNB) Latest_Chainstack() (int64, string, error) {
	url := fmt.Sprintf("https://opbnb-mainnet.core.chainstack.com/%s", scan.APIKey_Chainstack)
	payload := strings.NewReader("{\n  \"id\": 1,\n  \"jsonrpc\": \"2.0\",\n  \"method\": \"eth_getBlockByNumber\",\n  \"params\": [\n    \"latest\",\n    false\n  ]\n}")
	req, _ := http.NewRequest("POST", url, payload)
	req.Header.Add("Content-Type", "application/json")
	res, _ := http.DefaultClient.Do(req)
	defer res.Body.Close()
	body, _ := io.ReadAll(res.Body)
	resp := struct {
		Result struct {
			Number string `json:"number"`
			Hash   string `json:"hash"`
		} `json:"result"`
	}{}
	err := json.Unmarshal(body, &resp)
	if err != nil {
		return 0, "", fmt.Errorf("unmarshal %s error: %s", string(body), err.Error())
	}
	blockNumber, err := strconv.ParseInt(resp.Result.Number[2:], 16, 64)
	if err != nil {
		return 0, "", err
	}
	return blockNumber, resp.Result.Hash[2:], nil
}
func (scan *opBNB) Latest() (int64, string, error) {
	scan.index++
	if scan.index%2 == 0 {
		if scan.APIKey_Chainstack != "" {
			return scan.Latest_Chainstack()
		}
	}
	if scan.APIKey_BSC != "" {
		return scan.Latest_BSC()
	}
	return scan.Latest_Chainstack()
}

// https://docs.etherscan.io/etherscan-v2/api-endpoints/geth-parity-proxy#eth_getblockbynumber
func (scan *opBNB) Latest_BSC() (int64, string, error) {
	url := fmt.Sprintf("https://api.etherscan.io/v2/api?chainid=204&module=proxy&action=eth_getBlockByNumber&tag=latest&boolean=false&apikey=%s", scan.APIKey_BSC)
	data, err := hs.HttpRequest("GET", url, nil, nil)
	if err != nil {
		return 0, "", err
	}
	resp := struct {
		Result struct {
			Number string `json:"number"`
			Hash   string `json:"hash"`
		} `json:"result"`
	}{}
	err = json.Unmarshal(data, &resp)
	if err != nil {
		return 0, "", fmt.Errorf("unmarshal %s error: %s", string(data), err.Error())
	}
	blockNumber, err := strconv.ParseInt(resp.Result.Number[2:], 16, 64)
	if err != nil {
		return 0, "", err
	}
	return blockNumber, resp.Result.Hash[2:], nil
}
