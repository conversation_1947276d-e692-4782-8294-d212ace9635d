package game

import (
	basicKing "igameKing/common/basic"
	"s2/define"
	"s2/modules/game/domain"
	"s2/modules/game/domain/impl"
	"s2/modules/game/userops"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
)

type module struct {
	iface.IModule
	domain *domain.Domain
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewEventLoop(basic.DefaultMQLen),
	}
	basicKing.ApiBaseURL = conf.Str("king.baseURL", "")
	basicKing.GameBaseURL = conf.Str("king.gameBaseURL", "")
	m.domain = domain.New(m)
	userops.Init(m)
	impl.Init(m.domain)
	return m
}

func (m module) Name() string {
	return define.ModuleName.Game
}

func (m *module) Exit() error {
	m.IModule.Exit()
	userops.SaveALL()
	return nil
}
