package userops

import (
	"reflect"
	"s2/common"
	"s2/modules/game/userops/userdata"
	"time"

	"github.com/bluele/gcache"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/message/codec"
	"github.com/jfcwrlight/core/utils"
)

var manager *userManager

type userManager struct {
	iface.IModule
	cache gcache.Cache
}

func Init(m iface.IModule) {
	manager = &userManager{
		IModule: m,
		cache:   gcache.New(5000).LRU().Build(),
	}
	message.Response(m, manager.onC2SPackageGameReq, message.WithoutLog())
}

var (
	msgHandle   = map[reflect.Type]func(user *userdata.M, msg any){}
	msgResponse = map[reflect.Type]func(user *userdata.M, msg any, response func(any, error)){}
)

func Response[T1 any, T2 any](m iface.IModule, h func(user *userdata.M, req *T1, response func(*T2, error))) {
	// 如果是带UserID的请求是服务器发送的消息
	// 如果不带则是客户端请求
	if _, ok := any(new(T1)).(interface{ GetUserID() int64 }); ok {
		message.Response(m, wrapResponse(h), message.WithoutLog())
		return
	}
	mType := reflect.TypeFor[*T1]()
	if _, ok := msgResponse[mType]; ok {
		panic("msg handler already exists")
	}
	codec.Register[T1]()
	// 注册消息处理函数
	msgResponse[mType] = func(user *userdata.M, msg any, response func(any, error)) {
		since := time.Now()
		h(user, msg.(*T1), func(resp *T2, err error) {
			log.Infof("Response C2S UserID:%d, %s, %s, %s", user.ID, utils.FormatMsg(msg), common.FormatMsgIgnoreByCode(resp), time.Since(since))
			response(resp, err)
		})
	}
}

func Handle[T any](m iface.IModule, h func(user *userdata.M, req *T)) {
	// 如果是带UserID的请求是服务器发送的消息
	// 如果不带则是客户端请求
	if _, ok := any(new(T)).(interface{ GetUserID() int64 }); ok {
		message.Handle(m, wrapHandle(h))
		return
	}
	mType := reflect.TypeFor[*T]()
	if _, ok := msgHandle[mType]; ok {
		panic("msg handler already exists")
	}
	codec.Register[T]()
	// 注册消息处理函数
	msgHandle[mType] = func(user *userdata.M, msg any) {
		since := time.Now()
		h(user, msg.(*T))
		log.Infof("Handle C2S UserID:%d, %s, %s", user.ID, utils.FormatMsg(msg), time.Since(since))
	}
}

func wrapHandle[T any](h func(user *userdata.M, req *T)) func(msg *T) {
	return func(msg *T) {
		i := any(msg).(interface{ GetUserID() int64 })
		uid := i.GetUserID()
		conc.Go(func() {
			user, err := getOrLoadFromDB(uid)
			if err != nil {
				log.Error(err)
				return
			}
			user.ResetEventID()
			h(user, msg)
			saveDB(user)
		}, uid)
	}
}

func wrapResponse[T1 any, T2 any](h func(user *userdata.M, req *T1, response func(*T2, error))) func(msg *T1, response func(*T2, error)) {
	return func(msg *T1, response func(*T2, error)) {
		i := any(msg).(interface{ GetUserID() int64 })
		uid := i.GetUserID()
		conc.Go(func() {
			user, err := getOrLoadFromDB(uid)
			if err != nil {
				log.Error(err)
				return
			}
			user.ResetEventID()
			since := time.Now()
			h(user, msg, func(resp *T2, err error) {
				log.Infof("Response S2S UserID:%d, %s, %s, %s", user.ID, utils.FormatMsg(msg), common.FormatMsgIgnoreByCode(resp), time.Since(since))
				response(resp, err)
			})
			saveDB(user)
		}, uid)
	}
}
