package userops

import (
	"encoding/json"
	"errors"
	"os"
	"reflect"
	"runtime/debug"
	"s2/pb"

	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message/codec"
)

func (m *userManager) onC2SPackageGameReq(body *pb.C2SPackageGameReq, response func(*pb.C2SPackageGameResp, error)) {
	msg, err := codec.Decode(body.Body)
	if err != nil {
		log.Errorf("onC2SPackage decode error %s", err)
		return
	}
	conc.Go(func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("panic %v: %s", r, debug.Stack())
				response(nil, errors.New("panic"))
				if conf.<PERSON><PERSON>("debug.open", false) {
					os.Exit(1)
				}
			}
		}()
		user, err := getOrLoadFromDB(body.UserID)
		if err != nil {
			log.Error(err)
			response(nil, err)
			return
		}
		user.ResetEventID()
		h := msgResponse[reflect.TypeOf(msg)]
		h(user, msg, func(resp any, err error) {
			b, _ := json.Marshal(resp)
			response(&pb.C2SPackageGameResp{Body: b}, nil)
		})
		saveDB(user)
	}, body.UserID)
}
