package userdata

import (
	"github.com/jfcwrlight/core/infra/mgdb/orm"
	"github.com/jfcwrlight/core/utils"
	"github.com/jfcwrlight/core/utils/idgen"
)

type M struct {
	ID  int64 `bson:"_id"`
	Tag struct {
		EventID uint64
		SN      uint64
	} `bson:"-"`
	Spin struct {
		LastSpinInfo map[int32]*SpinInfo
		LastWin      map[int32]float64
		Step         map[int32]string
		IbetsChange  map[int32]string
		orm.DirtyTag `bson:"-"`
	}
	KingGame struct {
		Account      string
		Password     string
		Token        string
		orm.DirtyTag `bson:"-"`
	}
	SmallGame struct {
		Status       map[int32]string
		SpinData     map[int32]string
		CurWin       map[int32]float64
		Card         map[int32][]float64
		orm.DirtyTag `bson:"-"`
	}
	Settings struct {
		Setting      map[int32]string
		orm.DirtyTag `bson:"-"`
	}
	RegTime      int64
	orm.DirtyTag `bson:"-"`
}

func New() *M {
	return utils.DeepNew[*M]()
}

func (m M) MongoID() any {
	return m.ID
}

func (m *M) ResetEventID() {
	m.Tag.EventID = idgen.NewUUID()
}

func (m *M) EventID() uint64 {
	return m.Tag.EventID
}

type SpinInfo struct {
	Seed         int64
	Index        int32
	IndexMax     int32
	Input        float64
	Private      string
	Policy       string
	BlockHash    string
	BlockNumber  int64
	OracleKey    string
	Mode         int32
	Currency     int32
	orm.DirtyTag `bson:"-"`
}
