package domain

import (
	"s2/modules/game/domain/api"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/iface"
)

type Domain struct {
	iface.IModule
	domainops.IRoot
}

func New(m iface.IModule) *Domain {
	d := &Domain{
		IRoot:   domainops.New(m, caseMaxIndex),
		IModule: m,
	}
	return d
}

const (
	caseMinIndex = iota
	SpinIndex
	caseMaxIndex
)

func (d *Domain) SpinCase() api.ISpin {
	return d.GetCase(SpinIndex).(api.ISpin)
}
