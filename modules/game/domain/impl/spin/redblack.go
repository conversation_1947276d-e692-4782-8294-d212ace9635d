package spin

import (
	"math/rand"
	"s2/pb"
)

// RedBlackHandler RedBlack 小游戏处理器
type RedBlackHandler struct{}

// NewRedBlackHandler 创建 RedBlack 处理器
func NewRedBlackHandler() *RedBlackHandler {
	return &RedBlackHandler{}
}

// GetGameType 获取游戏类型
func (h *RedBlackHandler) GetGameType() SmallGameType {
	return SmallGameTypeRedBlack
}

// ValidateChoice 验证选择
func (h *RedBlackHandler) ValidateChoice(choice Choice) error {
	switch choice {
	case ChoiceAskDouble, ChoiceAskDoubleBackGame,
		ChoiceRed, ChoiceBlack, ChoiceSpade, ChoiceClub, ChoiceHeart, ChoiceDiamond:
		return nil
	default:
		return NewSmallGameError(pb.PARAM_ERROR, "无效的选择", string(choice))
	}
}

// InitGame 初始化游戏
func (h *RedBlackHandler) InitGame(ctx *SmallGameContext) (*SmallGameResult, error) {
	logGameAction(ctx, "InitRedBlackGame", map[string]interface{}{
		"choice":  ctx.Choice,
		"lastWin": ctx.LastWin,
	})

	// 创建子游戏信息
	subGameInfo := map[string]any{
		"category":      "Double",
		"type":          "redblack",
		"startWin":      ctx.LastWin,
		"prevWin":       ctx.LastWin,
		"curWin":        ctx.LastWin,
		"paidWin":       -1,
		"attempt":       0,
		"attemptResult": 0,
		"winLevel":      0,
		"rule":          "colorsuit",
		"add":           map[string]any{},
		"onlyToBD":      nil,
		"set":           []any{"redblack", 5, 100000},
		"dblhalf":       0,
		"halfWin":       0,
		"userChoice":    "",
		"sendRestore":   nil,
		"av":            []int{},
	}

	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		gsData = make(map[string]any)
		ctx.SpinData["gs"] = gsData
	}

	// 生成最后5张卡牌
	gsData["last5cards"] = generateLast5Cards(ctx.UserData.SmallGame.Card[ctx.GameID])

	// 更新游戏状态
	gsData["phaseCur"] = string(PhaseDoubleRedBlack)
	gsData["phaseNext"] = string(PhaseToDouble)

	// 添加或更新子游戏信息
	if subGameInfoList, ok := gsData["subGameInfo"].([]any); ok && len(subGameInfoList) > 0 {
		gsData["subGameInfo"] = append(subGameInfoList, subGameInfo)
	} else {
		gsData["subGameInfo"] = []any{subGameInfo}
	}

	return &SmallGameResult{
		Status:     GameStatusInit,
		Win:        0,
		GameData:   ctx.SpinData,
		NeedUpdate: true,
		Multiplier: 0,
	}, nil
}

// HandleChoice 处理选择
func (h *RedBlackHandler) HandleChoice(ctx *SmallGameContext) (*SmallGameResult, error) {
	switch ctx.Choice {
	case ChoiceAskDouble:
		return h.InitGame(ctx)
	case ChoiceAskDoubleBackGame:
		return h.handleBackToGame(ctx)
	default:
		return h.handleColorChoice(ctx)
	}
}

// handleBackToGame 处理返回游戏
func (h *RedBlackHandler) handleBackToGame(ctx *SmallGameContext) (*SmallGameResult, error) {
	logGameAction(ctx, "RedBlackBackToGame", nil)

	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		gsData = make(map[string]any)
		ctx.SpinData["gs"] = gsData
	}

	gsData["phaseCur"] = string(PhaseDoubleRedBlack)
	gsData["phaseNext"] = string(PhaseToDouble)

	return &SmallGameResult{
		Status:     GameStatusBack,
		Win:        ctx.CurrentWin,
		GameData:   ctx.SpinData,
		NeedUpdate: true,
		Multiplier: 0,
	}, nil
}

// handleColorChoice 处理颜色/花色选择
func (h *RedBlackHandler) handleColorChoice(ctx *SmallGameContext) (*SmallGameResult, error) {
	logGameAction(ctx, "RedBlackColorChoice", map[string]interface{}{
		"choice":     ctx.Choice,
		"currentWin": ctx.CurrentWin,
	})

	// 计算游戏结果
	status, multiplier := h.calculateGameResult(ctx.Choice)
	win := ctx.CurrentWin * float64(multiplier)

	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "gs 数据不存在", "")
	}

	// 获取最后一个子游戏信息
	subGameInfoList, ok := gsData["subGameInfo"].([]any)
	if !ok || len(subGameInfoList) == 0 {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "子游戏信息不存在", "")
	}

	lastSubGameIndex := len(subGameInfoList) - 1
	subGameInfo, ok := subGameInfoList[lastSubGameIndex].(map[string]any)
	if !ok {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "子游戏信息格式错误", "")
	}

	// 使用 BuildSubGameRedBlackInfo 函数更新子游戏信息
	updatedSubGameInfo := BuildSubGameRedBlackInfo(win, string(status), string(ctx.Choice), subGameInfo)

	// 更新子游戏信息
	subGameInfoList[lastSubGameIndex] = updatedSubGameInfo
	gsData["subGameInfo"] = subGameInfoList

	ctx.UserData.SmallGame.Card[ctx.GameID] = append(ctx.UserData.SmallGame.Card[ctx.GameID], float64(updatedSubGameInfo["av"].([]int)[0]))
	ctx.UserData.SmallGame.MarkDirty()

	// 重新生成最后5张卡牌
	gsData["last5cards"] = generateLast5Cards(ctx.UserData.SmallGame.Card[ctx.GameID])

	// 更新游戏状态
	gsData["curWin"] = win

	if status == GameStatusWin {
		gsData["phaseCur"] = string(PhaseDoubleRedBlack)
		gsData["phaseNext"] = string(PhaseToDouble)
	} else {
		gsData["phaseCur"] = string(PhaseDoubleDealerFinished)
		gsData["phaseNext"] = string(PhaseToPaid)
	}

	// 检查是否达到最大尝试次数
	if attempt, ok := updatedSubGameInfo["attempt"].(float64); ok && int(attempt) >= MaxAttempts {
		gsData["phaseCur"] = string(PhaseDoubleDealerFinished)
		gsData["phaseNext"] = string(PhaseToPaid)
	}

	needUpdate := true
	if status == GameStatusLose {
		// 删除旋转数据
		delete(ctx.UserData.SmallGame.SpinData, ctx.GameID)
		needUpdate = false
	}

	return &SmallGameResult{
		Status:     status,
		Win:        win,
		GameData:   ctx.SpinData,
		NeedUpdate: needUpdate,
		Multiplier: multiplier,
	}, nil
}

// calculateGameResult 计算游戏结果
func (h *RedBlackHandler) calculateGameResult(choice Choice) (GameStatus, int) {
	r := rand.Float64()

	switch choice {
	case ChoiceRed, ChoiceBlack:
		if r < 0.25 {
			return GameStatusWin, RedBlackWinMultiple
		}
		return GameStatusLose, 0
	case ChoiceSpade, ChoiceClub, ChoiceHeart, ChoiceDiamond:
		if r < 0.125 {
			return GameStatusWin, SuitWinMultiple
		}
		return GameStatusLose, 0
	default:
		return GameStatusLose, 0
	}
}
