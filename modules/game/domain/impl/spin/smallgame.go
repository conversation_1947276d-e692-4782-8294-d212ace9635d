// Package spin 实现了游戏旋转相关的业务逻辑
// 本文件包含了重构后的小游戏处理架构，采用了现代化的 Go 设计模式
package spin

import (
	"encoding/json"
	"s2/modules/game/userops/userdata"
	"s2/pb"

	"github.com/jfcwrlight/core/log"
)

// SmallGameType 小游戏类型
type SmallGameType string

const (
	SmallGameTypeDealer   SmallGameType = "dealer"
	SmallGameTypeRedBlack SmallGameType = "redblack"
	SmallGameTypeOff      SmallGameType = "off"
)

// GameStatus 游戏状态
type GameStatus string

const (
	GameStatusWin  GameStatus = "win"
	GameStatusLose GameStatus = "lose"
	GameStatusInit GameStatus = "init"
	GameStatusBack GameStatus = "back"
)

// Choice 选择类型
type Choice string

const (
	ChoiceAskDouble         Choice = "askDouble"
	ChoiceAskDoubleBackGame Choice = "askDoubleBackToGame"
	ChoiceRed               Choice = "Red"
	ChoiceBlack             Choice = "Black"
	ChoiceSpade             Choice = "Spade"
	ChoiceClub              Choice = "Club"
	ChoiceHeart             Choice = "Heart"
	ChoiceDiamond           Choice = "Diamond"
)

// GamePhase 游戏阶段
type GamePhase string

const (
	PhaseDoubleDealer         GamePhase = "Doubledealer"
	PhaseDoubleDealerFinished GamePhase = "Doubledealer_finished"
	PhaseDoubleRedBlack       GamePhase = "Doubleredblack"
	PhaseBaseDeal             GamePhase = "basedeal"
	PhaseToDouble             GamePhase = "toDouble"
	PhaseToPaid               GamePhase = "toPaid"
)

// 游戏常量
const (
	MaxAttempts         = 5
	DealerWinMultiple   = 2
	RedBlackWinMultiple = 2
	SuitWinMultiple     = 4
	DealerCardRange     = 38
	StandardDeckSize    = 52
	CardsPerSuit        = 13
	SuitsCount          = 4
)

// 卡牌花色
const (
	SuitSpade   = 0 // 黑桃
	SuitClub    = 1 // 梅花
	SuitHeart   = 2 // 红桃
	SuitDiamond = 3 // 方块
)

// SmallGameError 统一错误类型
type SmallGameError struct {
	Code    pb.ErrCode
	Message string
	Details string
}

func (e *SmallGameError) Error() string {
	return e.Message
}

// NewSmallGameError 创建小游戏错误
func NewSmallGameError(code pb.ErrCode, message, details string) *SmallGameError {
	return &SmallGameError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// SmallGameContext 小游戏上下文
type SmallGameContext struct {
	UserID     int64
	GameID     int32
	GameType   SmallGameType
	Choice     Choice
	LastWin    float64
	CurrentWin float64
	SpinData   map[string]any
	UserData   *userdata.M
}

// SmallGameResult 小游戏结果
type SmallGameResult struct {
	Status     GameStatus
	Win        float64
	GameData   map[string]any
	NeedUpdate bool
	Multiplier int
}

// SmallGameHandler 小游戏处理器接口
type SmallGameHandler interface {
	HandleChoice(ctx *SmallGameContext) (*SmallGameResult, error)
	InitGame(ctx *SmallGameContext) (*SmallGameResult, error)
	ValidateChoice(choice Choice) error
	GetGameType() SmallGameType
}

// SmallGameManager 小游戏管理器
type SmallGameManager struct {
	handlers map[SmallGameType]SmallGameHandler
}

// NewSmallGameManager 创建小游戏管理器
func NewSmallGameManager() *SmallGameManager {
	manager := &SmallGameManager{
		handlers: make(map[SmallGameType]SmallGameHandler),
	}

	// 注册处理器
	manager.RegisterHandler(NewDealerHandler())
	manager.RegisterHandler(NewRedBlackHandler())

	return manager
}

// RegisterHandler 注册处理器
func (m *SmallGameManager) RegisterHandler(handler SmallGameHandler) {
	m.handlers[handler.GetGameType()] = handler
}

// GetHandler 获取处理器
func (m *SmallGameManager) GetHandler(gameType SmallGameType) (SmallGameHandler, error) {
	handler, exists := m.handlers[gameType]
	if !exists {
		return nil, NewSmallGameError(pb.PARAM_ERROR, "不支持的小游戏类型", string(gameType))
	}
	return handler, nil
}

// parseSpinData 解析旋转数据
func parseSpinData(data string) (map[string]any, error) {
	if data == "" {
		return nil, NewSmallGameError(pb.PARAM_ERROR, "旋转数据为空", "")
	}

	var spinData map[string]any
	if err := json.Unmarshal([]byte(data), &spinData); err != nil {
		return nil, NewSmallGameError(pb.PARAM_ERROR, "解析旋转数据失败", err.Error())
	}

	return spinData, nil
}

// serializeSpinData 序列化旋转数据
func serializeSpinData(data map[string]any) (string, error) {
	bytes, err := json.Marshal(data)
	if err != nil {
		return "", NewSmallGameError(pb.SERVER_ERROR, "序列化旋转数据失败", err.Error())
	}
	return string(bytes), nil
}

// validateContext 验证上下文
func validateContext(ctx *SmallGameContext) error {
	if ctx == nil {
		return NewSmallGameError(pb.PARAM_ERROR, "上下文为空", "")
	}
	if ctx.UserData == nil || ctx.UserData.ID <= 0 {
		return NewSmallGameError(pb.ACCOUNT_NOT_FOUND, "用户非法", "")
	}
	if ctx.UserData.SmallGame.SpinData == nil {
		return NewSmallGameError(pb.PARAM_ERROR, "用户数据非法", "")
	}
	if ctx.LastWin <= 0 {
		return NewSmallGameError(pb.PARAM_ERROR, "lastwin非法", "")
	}
	if ctx.UserData.SmallGame.Status == nil {
		ctx.UserData.SmallGame.Status[ctx.GameID] = ""
	}
	return nil
}

// updateUserData 更新用户数据
func updateUserData(ctx *SmallGameContext, result *SmallGameResult) error {
	if !result.NeedUpdate {
		return nil
	}

	// 序列化游戏数据
	serializedData, err := serializeSpinData(result.GameData)
	if err != nil {
		return err
	}

	// 更新用户数据
	ctx.UserData.SmallGame.CurWin[ctx.GameID] = result.Win
	ctx.UserData.SmallGame.SpinData[ctx.GameID] = serializedData
	ctx.UserData.SmallGame.MarkDirty()

	log.Infof("更新用户数据: UserID=%d, GameID=%d, Win=%.2f, Status=%s",
		ctx.UserID, ctx.GameID, result.Win, result.Status)

	return nil
}

// generateLast5Cards 生成最后5张卡牌
func generateLast5Cards(cards []float64) []float64 {
	n := len(cards)
	if n == 0 {
		return make([]float64, 5)
	}

	result := make([]float64, 5)
	if n <= 5 {
		copy(result, cards)
		// 反转数组
		for i, j := 0, n-1; i < j; i, j = i+1, j-1 {
			result[i], result[j] = result[j], result[i]
		}
		return result
	}

	// 获取最后5个元素并反转
	for i := 0; i < 5; i++ {
		result[i] = cards[n-5+i]
	}
	for i, j := 0, 4; i < j; i, j = i+1, j-1 {
		result[i], result[j] = result[j], result[i]
	}

	return result
}

// logGameAction 记录游戏操作
func logGameAction(ctx *SmallGameContext, action string, details interface{}) {
	log.Infof("小游戏操作: UserID=%d, GameID=%d, GameType=%s, Action=%s, Details=%+v",
		ctx.UserID, ctx.GameID, ctx.GameType, action, details)
}

// SmallGameService 小游戏服务
type SmallGameService struct {
	manager *SmallGameManager
}

// NewSmallGameService 创建小游戏服务
func NewSmallGameService() *SmallGameService {
	return &SmallGameService{
		manager: NewSmallGameManager(),
	}
}

// ProcessSmallGameRequest 处理小游戏请求
func (s *SmallGameService) ProcessSmallGameRequest(user *userdata.M, body *pb.SmallGameBtaReq) (*pb.SmallGameBtaResp, error) {
	// 1. 基础验证
	if err := s.validateBasicRequest(user, body); err != nil {
		if sgErr, ok := err.(*SmallGameError); ok {
			return &pb.SmallGameBtaResp{Code: sgErr.Code, Detail: sgErr.Details}, nil
		}
		return &pb.SmallGameBtaResp{Code: pb.SERVER_ERROR, Detail: err.Error()}, nil
	}

	// 2. 获取小游戏类型和配置
	gameType, lastWin, currentWin, err := s.getGameConfiguration(user, body.GameID)
	if err != nil {
		if sgErr, ok := err.(*SmallGameError); ok {
			return &pb.SmallGameBtaResp{Code: sgErr.Code, Detail: sgErr.Details}, nil
		}
		return &pb.SmallGameBtaResp{Code: pb.SERVER_ERROR, Detail: err.Error()}, nil
	}

	// 3. 解析旋转数据
	spinData, err := parseSpinData(user.SmallGame.SpinData[body.GameID])
	if err != nil {
		if sgErr, ok := err.(*SmallGameError); ok {
			return &pb.SmallGameBtaResp{Code: sgErr.Code, Detail: sgErr.Details}, nil
		}
		return &pb.SmallGameBtaResp{Code: pb.SERVER_ERROR, Detail: err.Error()}, nil
	}

	// 4. 创建游戏上下文
	ctx := &SmallGameContext{
		UserID:     user.ID,
		GameID:     body.GameID,
		GameType:   gameType,
		Choice:     Choice(body.Choice),
		LastWin:    lastWin,
		CurrentWin: currentWin,
		SpinData:   spinData,
		UserData:   user,
	}

	// 5. 验证上下文
	if err := validateContext(ctx); err != nil {
		if sgErr, ok := err.(*SmallGameError); ok {
			return &pb.SmallGameBtaResp{Code: sgErr.Code, Detail: sgErr.Details}, nil
		}
		return &pb.SmallGameBtaResp{Code: pb.SERVER_ERROR, Detail: err.Error()}, nil
	}

	// 6. 处理游戏逻辑
	result, err := s.processGameLogic(ctx)
	if err != nil {
		if sgErr, ok := err.(*SmallGameError); ok {
			return &pb.SmallGameBtaResp{Code: sgErr.Code, Detail: sgErr.Details}, nil
		}
		return &pb.SmallGameBtaResp{Code: pb.SERVER_ERROR, Detail: err.Error()}, nil
	}

	// 7. 更新用户数据
	if err := updateUserData(ctx, result); err != nil {
		if sgErr, ok := err.(*SmallGameError); ok {
			return &pb.SmallGameBtaResp{Code: sgErr.Code, Detail: sgErr.Details}, nil
		}
		return &pb.SmallGameBtaResp{Code: pb.SERVER_ERROR, Detail: err.Error()}, nil
	}

	// 8. 构建响应
	return s.buildResponse(result, gameType)
}

// validateBasicRequest 验证基础请求
func (s *SmallGameService) validateBasicRequest(user *userdata.M, body *pb.SmallGameBtaReq) error {
	if user == nil || user.ID <= 0 {
		return NewSmallGameError(pb.ACCOUNT_NOT_FOUND, "用户非法", "")
	}
	if user.SmallGame.SpinData == nil {
		return NewSmallGameError(pb.PARAM_ERROR, "用户数据非法", "")
	}
	if body == nil {
		return NewSmallGameError(pb.PARAM_ERROR, "请求体为空", "")
	}
	return nil
}

// getGameConfiguration 获取游戏配置
func (s *SmallGameService) getGameConfiguration(user *userdata.M, gameID int32) (SmallGameType, float64, float64, error) {
	// 获取小游戏类型
	var gameType SmallGameType
	if user.Settings.Setting != nil {
		if v, ok := user.Settings.Setting[gameID]; ok && v != "" {
			setting := map[string]any{}
			if err := json.Unmarshal([]byte(v), &setting); err == nil {
				if t, ok := setting["smallGameType"].(string); ok {
					gameType = SmallGameType(t)
				}
			}
		}
	}

	if gameType == "" {
		return "", 0, 0, NewSmallGameError(pb.PARAM_ERROR, "未设置小游戏类型", "")
	}

	// 获取 lastwin 和 curwin
	lastWin := 0.0
	currentWin := user.SmallGame.CurWin[gameID]
	if user.Spin.LastWin != nil {
		lastWin = user.Spin.LastWin[gameID]
	}
	if user.SmallGame.CurWin[gameID] == 0 {
		currentWin = lastWin
	}

	if lastWin <= 0 {
		return "", 0, 0, NewSmallGameError(pb.PARAM_ERROR, "lastwin非法", "")
	}

	return gameType, lastWin, currentWin, nil
}

// processGameLogic 处理游戏逻辑
func (s *SmallGameService) processGameLogic(ctx *SmallGameContext) (*SmallGameResult, error) {
	// 获取处理器
	handler, err := s.manager.GetHandler(ctx.GameType)
	if err != nil {
		return nil, err
	}

	// 验证选择
	if err := handler.ValidateChoice(ctx.Choice); err != nil {
		return nil, err
	}

	// 处理选择
	result, err := handler.HandleChoice(ctx)
	if err != nil {
		return nil, err
	}

	status := []any{string(result.Status), result.Multiplier}
	bs, _ := json.Marshal(status)
	ctx.UserData.SmallGame.Status[ctx.GameID] = string(bs)
	ctx.UserData.SmallGame.MarkDirty()

	// 处理默认情况
	if ctx.GameType == SmallGameTypeOff || result == nil {
		return s.handleDefaultCase(ctx)
	}

	return result, nil
}

// handleDefaultCase 处理默认情况
func (s *SmallGameService) handleDefaultCase(ctx *SmallGameContext) (*SmallGameResult, error) {
	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		gsData = make(map[string]any)
		ctx.SpinData["gs"] = gsData
	}

	gsData["phaseCur"] = string(PhaseBaseDeal)
	gsData["phaseNext"] = string(PhaseToPaid)

	return &SmallGameResult{
		Status:     GameStatusBack,
		Win:        ctx.CurrentWin,
		GameData:   ctx.SpinData,
		NeedUpdate: true,
		Multiplier: 0,
	}, nil
}

// buildResponse 构建响应
func (s *SmallGameService) buildResponse(result *SmallGameResult, gameType SmallGameType) (*pb.SmallGameBtaResp, error) {
	// 序列化游戏数据
	serializedData, err := serializeSpinData(result.GameData)
	if err != nil {
		return nil, err
	}

	// 构建响应详情
	respDetail := map[string]any{
		"type":    string(gameType),
		"lastwin": result.Win,
		"result":  serializedData,
	}

	detailBytes, err := json.Marshal(respDetail)
	if err != nil {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "序列化响应失败", err.Error())
	}

	return &pb.SmallGameBtaResp{
		Code:   pb.SUCCESS,
		Detail: string(detailBytes),
	}, nil
}
