package spin

import (
	"math/rand"
	"s2/pb"
	"strconv"
	"time"
)

// DealerHandler Dealer 小游戏处理器
type DealerHandler struct{}

// NewDealerHandler 创建 Dealer 处理器
func NewDealerHandler() *DealerHandler {
	return &DealerHandler{}
}

// GetGameType 获取游戏类型
func (h *DealerHandler) GetGameType() SmallGameType {
	return SmallGameTypeDealer
}

// ValidateChoice 验证选择
func (h *DealerHandler) ValidateChoice(choice Choice) error {
	switch choice {
	case ChoiceAskDouble, ChoiceAskDoubleBackGame:
		return nil
	default:
		// 数字选择 (1-4)
		if choiceIndex, err := strconv.Atoi(string(choice)); err == nil && choiceIndex >= 1 && choiceIndex <= 4 {
			return nil
		}
		return NewSmallGameError(pb.PARAM_ERROR, "无效的选择", string(choice))
	}
}

// InitGame 初始化游戏
func (h *DealerHandler) InitGame(ctx *SmallGameContext) (*SmallGameResult, error) {
	logGameAction(ctx, "InitDealerGame", map[string]interface{}{
		"choice":  ctx.Choice,
		"lastWin": ctx.LastWin,
	})

	// 生成庄家牌
	dealerCard := rand.New(rand.NewSource(time.Now().UnixNano())).Intn(DealerCardRange) + 1

	// 创建子游戏信息
	subGameInfo := map[string]any{
		"category":       "Double",
		"type":           "dealer",
		"startWin":       ctx.LastWin,
		"prevWin":        ctx.LastWin,
		"curWin":         ctx.LastWin,
		"paidWin":        -1,
		"attempt":        0,
		"attemptResult":  0,
		"winLevel":       0,
		"rule":           "color",
		"userChoice":     "",
		"dblhalf":        0,
		"halfWin":        0,
		"openDealerCard": 1,
		"av":             []int{0, 0, 0, 0, 0},
		"add": map[string]any{
			"carddealer": dealerCard,
			"openDealer": 1,
		},
		"set": []any{
			"dealer",
			5,
			100000,
			[]string{"openCardYes"},
		},
		"onlyToBD":    nil,
		"sendRestore": nil,
	}

	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		gsData = make(map[string]any)
		ctx.SpinData["gs"] = gsData
	}

	// 更新游戏状态
	gsData["phaseCur"] = string(PhaseDoubleDealer)
	gsData["phaseNext"] = string(PhaseToDouble)

	// 添加或更新子游戏信息
	if subGameInfoList, ok := gsData["subGameInfo"].([]any); ok && len(subGameInfoList) > 0 {
		gsData["subGameInfo"] = append(subGameInfoList, subGameInfo)
	} else {
		gsData["subGameInfo"] = []any{subGameInfo}
	}

	return &SmallGameResult{
		Status:     GameStatusWin,
		Win:        ctx.LastWin,
		GameData:   ctx.SpinData,
		NeedUpdate: true,
		Multiplier: DealerWinMultiple,
	}, nil
}

// HandleChoice 处理选择
func (h *DealerHandler) HandleChoice(ctx *SmallGameContext) (*SmallGameResult, error) {
	switch ctx.Choice {
	case ChoiceAskDouble:
		return h.InitGame(ctx)
	case ChoiceAskDoubleBackGame:
		return h.handleBackToGame(ctx)
	default:
		return h.handleCardChoice(ctx)
	}
}

// handleBackToGame 处理返回游戏
func (h *DealerHandler) handleBackToGame(ctx *SmallGameContext) (*SmallGameResult, error) {
	logGameAction(ctx, "DealerBackToGame", nil)

	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		gsData = make(map[string]any)
		ctx.SpinData["gs"] = gsData
	}

	gsData["phaseCur"] = string(PhaseDoubleRedBlack)
	gsData["phaseNext"] = string(PhaseToDouble)

	return &SmallGameResult{
		Status:     GameStatusBack,
		Win:        ctx.CurrentWin,
		GameData:   ctx.SpinData,
		NeedUpdate: true,
		Multiplier: 0,
	}, nil
}

// handleCardChoice 处理卡牌选择
func (h *DealerHandler) handleCardChoice(ctx *SmallGameContext) (*SmallGameResult, error) {
	choiceIndex, err := strconv.Atoi(string(ctx.Choice))
	if err != nil {
		return nil, NewSmallGameError(pb.PARAM_ERROR, "无效的卡牌选择", string(ctx.Choice))
	}

	logGameAction(ctx, "DealerCardChoice", map[string]interface{}{
		"choice":     choiceIndex,
		"currentWin": ctx.CurrentWin,
	})

	// 计算游戏结果
	status, multiplier := h.calculateGameResult()
	win := ctx.CurrentWin * float64(multiplier)

	// 获取 gs 数据
	gsData, ok := ctx.SpinData["gs"].(map[string]any)
	if !ok {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "gs 数据不存在", "")
	}

	// 获取最后一个子游戏信息
	subGameInfoList, ok := gsData["subGameInfo"].([]any)
	if !ok || len(subGameInfoList) == 0 {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "子游戏信息不存在", "")
	}

	lastSubGameIndex := len(subGameInfoList) - 1
	subGameInfo, ok := subGameInfoList[lastSubGameIndex].(map[string]any)
	if !ok {
		return nil, NewSmallGameError(pb.SERVER_ERROR, "子游戏信息格式错误", "")
	}

	// 使用 BuildSubGameDealerInfo 函数更新子游戏信息
	updatedSubGameInfo := BuildSubGameDealerInfo(win, string(status), choiceIndex, subGameInfo)

	// 更新子游戏信息
	subGameInfoList[lastSubGameIndex] = updatedSubGameInfo
	gsData["subGameInfo"] = subGameInfoList

	// 更新游戏状态
	gsData["curWin"] = win

	if status == GameStatusWin {
		gsData["phaseCur"] = string(PhaseDoubleDealer)
		gsData["phaseNext"] = string(PhaseToDouble)
	} else {
		gsData["phaseCur"] = string(PhaseDoubleDealerFinished)
		gsData["phaseNext"] = string(PhaseToPaid)
	}

	// 检查是否达到最大尝试次数
	if attempt, ok := updatedSubGameInfo["attempt"].(float64); ok && int(attempt) >= MaxAttempts {
		gsData["phaseCur"] = string(PhaseDoubleDealerFinished)
		gsData["phaseNext"] = string(PhaseToPaid)
	}

	needUpdate := true
	if status == GameStatusLose {
		// 删除旋转数据
		delete(ctx.UserData.SmallGame.SpinData, ctx.GameID)
		needUpdate = false
	}

	return &SmallGameResult{
		Status:     status,
		Win:        win,
		GameData:   ctx.SpinData,
		NeedUpdate: needUpdate,
		Multiplier: multiplier,
	}, nil
}

// calculateGameResult 计算游戏结果
func (h *DealerHandler) calculateGameResult() (GameStatus, int) {
	r := rand.Float64()

	if r < 0.5 {
		return GameStatusWin, DealerWinMultiple
	}
	return GameStatusLose, 0
}
