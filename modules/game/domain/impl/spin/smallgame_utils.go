package spin

import (
	"math/rand"
	"strconv"
	"time"
)

// CardUtils 卡牌工具类
type CardUtils struct{}

// NewCardUtils 创建卡牌工具实例
func NewCardUtils() *CardUtils {
	return &CardUtils{}
}

// GenerateRandomCard 生成随机卡牌
func (c *CardUtils) GenerateRandomCard() int {
	return rand.Intn(StandardDeckSize)
}

// GenerateDealerCard 生成庄家卡牌
func (c *CardUtils) GenerateDealerCard() int {
	return rand.New(rand.NewSource(time.Now().UnixNano())).Intn(DealerCardRange) + 1
}

// GetCardValue 获取卡牌点数 (2-14, A=14)
func (c *CardUtils) GetCardValue(cardID int) int {
	return (cardID / SuitsCount) + 2
}

// GetCardSuit 获取卡牌花色 (0=黑桃, 1=梅花, 2=红桃, 3=方块)
func (c *CardUtils) GetCardSuit(cardID int) int {
	return cardID % SuitsCount
}

// IsRedCard 判断是否为红色卡牌
func (c *CardUtils) IsRedCard(cardID int) bool {
	suit := c.GetCardSuit(cardID)
	return suit == SuitHeart || suit == SuitDiamond
}

// IsBlackCard 判断是否为黑色卡牌
func (c *CardUtils) IsBlackCard(cardID int) bool {
	return !c.IsRedCard(cardID)
}

// FilterCardsByValue 根据点数过滤卡牌
func (c *CardUtils) FilterCardsByValue(excludeValue int) []int {
	var cards []int
	excludeBaseID := (excludeValue - 2) * SuitsCount

	for i := 0; i < StandardDeckSize; i++ {
		// 排除与指定点数相同的所有4张牌
		if i < excludeBaseID || i >= excludeBaseID+SuitsCount {
			cards = append(cards, i)
		}
	}
	return cards
}

// SeparateCardsByValue 根据点数分离卡牌
func (c *CardUtils) SeparateCardsByValue(cards []int, compareValue int) (bigger, smaller []int) {
	for _, cardID := range cards {
		cardValue := c.GetCardValue(cardID)
		if cardValue > compareValue {
			bigger = append(bigger, cardID)
		} else if cardValue < compareValue {
			smaller = append(smaller, cardID)
		}
	}
	return bigger, smaller
}

// ShuffleCards 洗牌
func (c *CardUtils) ShuffleCards(cards []int) {
	rand.Shuffle(len(cards), func(i, j int) {
		cards[i], cards[j] = cards[j], cards[i]
	})
}

// GenerateCardsByChoice 根据选择生成符合条件的卡牌
func (c *CardUtils) GenerateCardsByChoice(choice Choice, shouldWin bool) int {
	if shouldWin {
		return c.generateWinningCard(choice)
	}
	return c.generateLosingCard(choice)
}

// generateWinningCard 生成获胜卡牌
func (c *CardUtils) generateWinningCard(choice Choice) int {
	switch choice {
	case ChoiceRed:
		// 红色：红桃或方块
		suits := []int{SuitHeart, SuitDiamond}
		suit := suits[rand.Intn(len(suits))]
		rank := rand.Intn(CardsPerSuit)
		return rank*SuitsCount + suit
	case ChoiceBlack:
		// 黑色：黑桃或梅花
		suits := []int{SuitSpade, SuitClub}
		suit := suits[rand.Intn(len(suits))]
		rank := rand.Intn(CardsPerSuit)
		return rank*SuitsCount + suit
	case ChoiceSpade:
		rank := rand.Intn(CardsPerSuit)
		return rank*SuitsCount + SuitSpade
	case ChoiceClub:
		rank := rand.Intn(CardsPerSuit)
		return rank*SuitsCount + SuitClub
	case ChoiceHeart:
		rank := rand.Intn(CardsPerSuit)
		return rank*SuitsCount + SuitHeart
	case ChoiceDiamond:
		rank := rand.Intn(CardsPerSuit)
		return rank*SuitsCount + SuitDiamond
	default:
		return c.GenerateRandomCard()
	}
}

// generateLosingCard 生成失败卡牌
func (c *CardUtils) generateLosingCard(choice Choice) int {
	var excludeCards []int

	switch choice {
	case ChoiceRed:
		// 排除红色牌，只选择黑色牌
		for rank := 0; rank < CardsPerSuit; rank++ {
			excludeCards = append(excludeCards, rank*SuitsCount+SuitSpade)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitClub)
		}
	case ChoiceBlack:
		// 排除黑色牌，只选择红色牌
		for rank := 0; rank < CardsPerSuit; rank++ {
			excludeCards = append(excludeCards, rank*SuitsCount+SuitHeart)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitDiamond)
		}
	case ChoiceSpade:
		// 排除黑桃，选择其他花色
		for rank := 0; rank < CardsPerSuit; rank++ {
			excludeCards = append(excludeCards, rank*SuitsCount+SuitClub)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitHeart)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitDiamond)
		}
	case ChoiceClub:
		// 排除梅花，选择其他花色
		for rank := 0; rank < CardsPerSuit; rank++ {
			excludeCards = append(excludeCards, rank*SuitsCount+SuitSpade)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitHeart)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitDiamond)
		}
	case ChoiceHeart:
		// 排除红桃，选择其他花色
		for rank := 0; rank < CardsPerSuit; rank++ {
			excludeCards = append(excludeCards, rank*SuitsCount+SuitSpade)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitClub)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitDiamond)
		}
	case ChoiceDiamond:
		// 排除方块，选择其他花色
		for rank := 0; rank < CardsPerSuit; rank++ {
			excludeCards = append(excludeCards, rank*SuitsCount+SuitSpade)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitClub)
			excludeCards = append(excludeCards, rank*SuitsCount+SuitHeart)
		}
	default:
		return c.GenerateRandomCard()
	}

	if len(excludeCards) > 0 {
		return excludeCards[rand.Intn(len(excludeCards))]
	}
	return c.GenerateRandomCard()
}

// ProbabilityCalculator 概率计算器
type ProbabilityCalculator struct{}

// NewProbabilityCalculator 创建概率计算器
func NewProbabilityCalculator() *ProbabilityCalculator {
	return &ProbabilityCalculator{}
}

// CalculateWinProbability 计算获胜概率
func (p *ProbabilityCalculator) CalculateWinProbability(choice Choice) float64 {
	switch choice {
	case ChoiceRed, ChoiceBlack:
		return 0.25 // 25% 获胜概率
	case ChoiceSpade, ChoiceClub, ChoiceHeart, ChoiceDiamond:
		return 0.125 // 12.5% 获胜概率
	default:
		return 0.0
	}
}

// ShouldWin 根据概率判断是否应该获胜
func (p *ProbabilityCalculator) ShouldWin(choice Choice) bool {
	probability := p.CalculateWinProbability(choice)
	return rand.Float64() < probability
}

// GameStateValidator 游戏状态验证器
type GameStateValidator struct{}

// NewGameStateValidator 创建游戏状态验证器
func NewGameStateValidator() *GameStateValidator {
	return &GameStateValidator{}
}

// ValidateAttempts 验证尝试次数
func (v *GameStateValidator) ValidateAttempts(attempts int) bool {
	return attempts >= 0 && attempts <= MaxAttempts
}

// ValidateWinAmount 验证获胜金额
func (v *GameStateValidator) ValidateWinAmount(amount float64) bool {
	return amount >= 0
}

// ValidateGamePhase 验证游戏阶段
func (v *GameStateValidator) ValidateGamePhase(phase GamePhase) bool {
	switch phase {
	case PhaseDoubleDealer, PhaseDoubleDealerFinished, PhaseDoubleRedBlack, PhaseBaseDeal, PhaseToDouble, PhaseToPaid:
		return true
	default:
		return false
	}
}

// ShouldEndGame 判断是否应该结束游戏
func (v *GameStateValidator) ShouldEndGame(attempts int, status GameStatus) bool {
	return attempts >= MaxAttempts || status == GameStatusLose
}

// DataConverter 数据转换器
type DataConverter struct{}

// NewDataConverter 创建数据转换器
func NewDataConverter() *DataConverter {
	return &DataConverter{}
}

// ConvertToFloatSlice 转换为 float64 切片
func (d *DataConverter) ConvertToFloatSlice(intSlice []int) []float64 {
	result := make([]float64, len(intSlice))
	for i, v := range intSlice {
		result[i] = float64(v)
	}
	return result
}

// ConvertToIntSlice 转换为 int 切片
func (d *DataConverter) ConvertToIntSlice(floatSlice []float64) []int {
	result := make([]int, len(floatSlice))
	for i, v := range floatSlice {
		result[i] = int(v)
	}
	return result
}

// ConvertToAnySlice 转换为 any 切片
func (d *DataConverter) ConvertToAnySlice(intSlice []int) []any {
	result := make([]any, len(intSlice))
	for i, v := range intSlice {
		result[i] = float64(v)
	}
	return result
}

func BuildSubGameDealerInfo(curwin float64, status string, choiceIdx int, subGameInfo map[string]any) map[string]any {
	av := make([]int, 5)
	switch subGameInfo["av"].(type) {
	case []int:
		av = subGameInfo["av"].([]int)
	case []any:
		for i, v := range subGameInfo["av"].([]any) {
			av[i] = int(v.(float64))
		}
	}
	cmpCard := int(subGameInfo["add"].(map[string]any)["carddealer"].(float64))
	av[0] = cmpCard
	// 计算庄家牌的点数，点数从2开始（2=2, 3=3, ..., A=14）
	cmpCardValue := (cmpCard / 4) + 2

	// 生成0~51的牌，排除与cmpCard点数相同的牌（4张）
	allCards := make([]int, 0, 48)          // 52 - 4 = 48张
	cmpCardBaseID := (cmpCardValue - 2) * 4 // 相同点数的起始ID
	for i := 0; i < 52; i++ {
		// 排除与庄家牌点数相同的所有4张牌
		if i < cmpCardBaseID || i >= cmpCardBaseID+4 {
			allCards = append(allCards, i)
		}
	}

	// 分别收集比cmpCard点数大和小的牌（不包含等于）
	var bigger, smaller []int
	for _, cardID := range allCards {
		cardValue := (cardID / 4) + 2
		if cardValue > cmpCardValue {
			bigger = append(bigger, cardID)
		} else if cardValue < cmpCardValue {
			smaller = append(smaller, cardID)
		}
	}

	// 洗牌
	rand.Shuffle(len(bigger), func(i, j int) { bigger[i], bigger[j] = bigger[j], bigger[i] })
	rand.Shuffle(len(smaller), func(i, j int) { smaller[i], smaller[j] = smaller[j], smaller[i] })

	if status == "win" {
		// 赢的情况：用户选择位置的牌面点数必须大于庄家牌
		if len(bigger) > 0 {
			av[choiceIdx] = bigger[0]
			bigger = bigger[1:]
		} else {
			// 如果没有更大的牌，使用庄家牌（这种情况下游戏逻辑可能需要调整）
			av[choiceIdx] = cmpCard
		}

		// 其他位置的牌面点数应该小于庄家牌
		j := 0
		for i := 1; i < 5; i++ {
			if i == choiceIdx {
				continue
			}
			if j < len(smaller) {
				av[i] = smaller[j]
				j++
			} else {
				// 如果没有足够的更小的牌，使用庄家牌
				av[i] = cmpCard
			}
		}
		subGameInfo["curWin"] = curwin
	} else {
		// 输的情况：用户选择位置的牌面点数必须小于庄家牌
		if len(smaller) > 0 {
			av[choiceIdx] = smaller[0]
			smaller = smaller[1:]
		} else {
			// 如果没有更小的牌，使用庄家牌（这种情况下游戏逻辑可能需要调整）
			av[choiceIdx] = cmpCard
		}

		// 其他位置的牌面点数应该大于庄家牌
		j := 0
		for i := 1; i < 5; i++ {
			if i == choiceIdx {
				continue
			}
			if j < len(bigger) {
				av[i] = bigger[j]
				j++
			} else {
				// 如果没有足够的更大的牌，使用庄家牌
				av[i] = cmpCard
			}
		}
		subGameInfo["curWin"] = 0
	}
	subGameInfo["attempt"] = subGameInfo["attempt"].(float64) + 1
	subGameInfo["av"] = av
	subGameInfo["userChoice"] = strconv.Itoa(choiceIdx)
	// 重新生成庄家牌
	carddealer := rand.New(rand.NewSource(time.Now().UnixNano())).Intn(38) + 1
	subGameInfo["add"] = map[string]any{
		"carddealer": carddealer,
		"openDealer": 1,
	}
	return subGameInfo
}

func BuildSubGameRedBlackInfo(curwin float64, status string, choice string, subGameInfo map[string]any) map[string]any {
	// 生成一个随机的图标ID (0-51)
	selectedCardID := rand.Intn(52)

	if status == "win" {
		// 如果是赢的状态，需要生成符合用户选择的图标ID
		switch choice {
		case "Red":
			// 红色：红桃(heart)和方块(diamond)，花色索引为2和3
			// 随机选择红桃或方块的牌
			suit := []int{2, 3}[rand.Intn(2)] // 2=红桃, 3=方块
			rank := rand.Intn(13)             // 0-12 对应 2-A
			selectedCardID = rank*4 + suit
			subGameInfo["curWin"] = curwin
		case "Black":
			// 黑色：黑桃(spade)和梅花(club)，花色索引为0和1
			// 随机选择黑桃或梅花的牌
			suit := []int{0, 1}[rand.Intn(2)] // 0=黑桃, 1=梅花
			rank := rand.Intn(13)             // 0-12 对应 2-A
			selectedCardID = rank*4 + suit
			subGameInfo["curWin"] = curwin
		case "Spade":
			// 黑桃：花色索引为0
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 0
			subGameInfo["curWin"] = curwin
		case "Club":
			// 梅花：花色索引为1
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 1
			subGameInfo["curWin"] = curwin
		case "Heart":
			// 红桃：花色索引为2
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 2
			subGameInfo["curWin"] = curwin
		case "Diamond":
			// 方块：花色索引为3
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 3
			subGameInfo["curWin"] = curwin
		}
		subGameInfo["rule"] = "colorsuit"
	} else {
		// 如果是输的状态，需要生成不符合用户选择的图标ID
		var excludeCards []int

		switch choice {
		case "Red":
			// 排除红色牌（红桃和方块），只选择黑色牌
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+1) // 梅花
			}
		case "Black":
			// 排除黑色牌（黑桃和梅花），只选择红色牌
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+2) // 红桃
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Spade":
			// 排除黑桃，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+1) // 梅花
				excludeCards = append(excludeCards, rank*4+2) // 红桃
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Club":
			// 排除梅花，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+2) // 红桃
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Heart":
			// 排除红桃，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+1) // 梅花
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Diamond":
			// 排除方块，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+1) // 梅花
				excludeCards = append(excludeCards, rank*4+2) // 红桃
			}
		}

		// 从排除列表中随机选择一张牌
		if len(excludeCards) > 0 {
			selectedCardID = excludeCards[rand.Intn(len(excludeCards))]
		}

		subGameInfo["curWin"] = 0
		subGameInfo["rule"] = "noatt"
	}

	// 将选中的图标ID存储到subGameInfo中
	subGameInfo["av"] = []int{selectedCardID}
	subGameInfo["userChoice"] = choice
	subGameInfo["attempt"] = subGameInfo["attempt"].(float64) + 1
	return subGameInfo
}
