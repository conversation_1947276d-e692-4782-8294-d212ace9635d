package spin

import (
	"s2/modules/game/userops/userdata"
	"strconv"
	"strings"
	"testing"

	"github.com/jfcwrlight/core/infra/mgdb/orm"
)

// TestStatusParsing 测试状态解析逻辑
func TestStatusParsing(t *testing.T) {
	// 创建测试用户数据
	userData := &userdata.M{
		ID: 12345,
		SmallGame: struct {
			Status       map[int32]string
			SpinData     map[int32]string
			CurWin       map[int32]float64
			Card         map[int32][]float64
			orm.DirtyTag `bson:"-"`
		}{
			Status: make(map[int32]string),
			CurWin: make(map[int32]float64),
		},
	}

	gameID := int32(400128)

	// 测试不同的游戏状态
	testCases := []struct {
		name           string
		gameStatus     GameStatus
		multiplier     int
		expectedStatus string
		expectedAsset  string // "win", "lose", "back"
	}{
		{
			name:           "Win Status",
			gameStatus:     GameStatusWin,
			multiplier:     2,
			expectedStatus: "win,2",
			expectedAsset:  "win",
		},
		{
			name:           "Lose Status",
			gameStatus:     GameStatusLose,
			multiplier:     0,
			expectedStatus: "lose,0",
			expectedAsset:  "lose",
		},
		{
			name:           "Back Status",
			gameStatus:     GameStatusBack,
			multiplier:     0,
			expectedStatus: "back,0",
			expectedAsset:  "back",
		},
		{
			name:           "Init Status",
			gameStatus:     GameStatusInit,
			multiplier:     0,
			expectedStatus: "init,0",
			expectedAsset:  "other",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟 updateUserData 中的状态设置逻辑
			statusParts := []string{string(tc.gameStatus), "2"} // 固定倍数为测试
			if tc.multiplier == 0 {
				statusParts[1] = "0"
			}
			userData.SmallGame.Status[gameID] = strings.Join(statusParts, ",")

			// 模拟 handle.go 中的状态解析逻辑
			status := strings.Split(userData.SmallGame.Status[gameID], ",")

			// 验证状态解析
			if len(status) < 1 {
				t.Errorf("Status array is empty")
				return
			}

			actualStatus := status[0]
			if actualStatus != string(tc.gameStatus) {
				t.Errorf("Expected status[0] to be '%s', but got '%s'", tc.gameStatus, actualStatus)
			}

			// 验证资产变更逻辑
			var assetChangeType string
			if status[0] == "win" || status[0] == "back" {
				assetChangeType = "positive" // curwin - lastwin
			} else {
				assetChangeType = "negative" // -lastwin
			}

			expectedAssetChange := "positive"
			if tc.expectedAsset == "lose" {
				expectedAssetChange = "negative"
			} else if tc.expectedAsset == "win" || tc.expectedAsset == "back" {
				expectedAssetChange = "positive"
			} else {
				expectedAssetChange = "negative" // 其他情况默认为负
			}

			if assetChangeType != expectedAssetChange {
				t.Errorf("Expected asset change type '%s', but got '%s' for status '%s'",
					expectedAssetChange, assetChangeType, tc.gameStatus)
			}

			t.Logf("Status: %s -> Parsed: %s -> Asset Change: %s",
				tc.expectedStatus, actualStatus, assetChangeType)
		})
	}
}

// TestDealerGameStatusFlow 测试 dealer 游戏状态流程
func TestDealerGameStatusFlow(t *testing.T) {
	handler := NewDealerHandler()

	// 测试多次游戏结果
	winCount := 0
	loseCount := 0
	totalTests := 100

	for i := 0; i < totalTests; i++ {
		status, multiplier := handler.calculateGameResult()

		// 验证状态和倍数的一致性
		if status == GameStatusWin && multiplier != DealerWinMultiple {
			t.Errorf("Win status should have multiplier %d, but got %d", DealerWinMultiple, multiplier)
		}
		if status == GameStatusLose && multiplier != 0 {
			t.Errorf("Lose status should have multiplier 0, but got %d", multiplier)
		}

		switch status {
		case GameStatusWin:
			winCount++
		case GameStatusLose:
			loseCount++
		default:
			t.Errorf("Unexpected status from dealer game: %v", status)
		}
	}

	t.Logf("Dealer game results: %d wins, %d losses out of %d tests", winCount, loseCount, totalTests)

	// 验证有胜有负（概率测试）
	if winCount == 0 || loseCount == 0 {
		t.Errorf("Expected both wins and losses, but got %d wins and %d losses", winCount, loseCount)
	}
}

// TestAssetChangeLogic 测试资产变更逻辑
func TestAssetChangeLogic(t *testing.T) {
	// 创建测试用户数据
	userData := &userdata.M{
		ID: 12345,
		SmallGame: struct {
			Status       map[int32]string
			SpinData     map[int32]string
			CurWin       map[int32]float64
			Card         map[int32][]float64
			orm.DirtyTag `bson:"-"`
		}{
			Status: make(map[int32]string),
			CurWin: make(map[int32]float64),
		},
		Spin: struct {
			LastSpinInfo map[int32]*userdata.SpinInfo
			LastWin      map[int32]float64
			Step         map[int32]string
			IbetsChange  map[int32]string
			orm.DirtyTag `bson:"-"`
		}{
			LastWin: make(map[int32]float64),
		},
	}

	gameID := int32(400128)
	lastwin := 100.0
	userData.Spin.LastWin[gameID] = lastwin

	// 测试不同状态的资产变更逻辑
	testCases := []struct {
		name           string
		status         string
		curwin         float64
		expectedChange float64
		description    string
	}{
		{
			name:           "Win Status",
			status:         "win,2",
			curwin:         200.0,
			expectedChange: 100.0, // curwin - lastwin = 200 - 100
			description:    "获胜时应该增加 curwin - lastwin",
		},
		{
			name:           "Lose Status",
			status:         "lose,0",
			curwin:         0.0,
			expectedChange: -100.0, // -lastwin = -100
			description:    "失败时应该扣除 lastwin",
		},
		{
			name:           "Back Status",
			status:         "back,0",
			curwin:         100.0,
			expectedChange: 0.0, // curwin - lastwin = 100 - 100
			description:    "返回时应该增加 curwin - lastwin",
		},
		{
			name:           "Init Status",
			status:         "init,0",
			curwin:         0.0,
			expectedChange: -100.0, // -lastwin = -100 (按照当前逻辑，非win/back都扣除lastwin)
			description:    "初始状态按照当前逻辑应该扣除 lastwin",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置用户数据
			userData.SmallGame.Status[gameID] = tc.status
			userData.SmallGame.CurWin[gameID] = tc.curwin

			// 模拟 onGetSmallGameBtaFinishReq 中的资产变更逻辑
			status := strings.Split(userData.SmallGame.Status[gameID], ",")
			curwin := userData.SmallGame.CurWin[gameID]

			var actualChange float64
			if status[0] == "win" || status[0] == "back" {
				actualChange = curwin - lastwin
			} else {
				actualChange = -lastwin
			}

			t.Logf("%s: status=%s, curwin=%.2f, lastwin=%.2f, change=%.2f",
				tc.description, status[0], curwin, lastwin, actualChange)

			if actualChange != tc.expectedChange {
				t.Errorf("Expected asset change %.2f, but got %.2f", tc.expectedChange, actualChange)
			}
		})
	}
}

// TestStatusUpdateTiming 测试状态更新时序
func TestStatusUpdateTiming(t *testing.T) {
	// 模拟一个完整的小游戏流程
	userData := &userdata.M{
		ID: 12345,
		SmallGame: struct {
			Status       map[int32]string
			SpinData     map[int32]string
			CurWin       map[int32]float64
			Card         map[int32][]float64
			orm.DirtyTag `bson:"-"`
		}{
			Status: make(map[int32]string),
			CurWin: make(map[int32]float64),
		},
	}

	gameID := int32(400128)

	// 模拟 updateUserData 函数的行为
	updateStatus := func(status GameStatus, multiplier int, win float64) {
		statusParts := []string{string(status), strconv.Itoa(multiplier)}
		userData.SmallGame.Status[gameID] = strings.Join(statusParts, ",")
		userData.SmallGame.CurWin[gameID] = win
	}

	// 测试序列：win -> win -> lose
	t.Run("Win-Win-Lose Sequence", func(t *testing.T) {
		// 第一次获胜
		updateStatus(GameStatusWin, 2, 200.0)
		status1 := strings.Split(userData.SmallGame.Status[gameID], ",")
		t.Logf("Round 1: Status=%s, CurWin=%.2f", status1[0], userData.SmallGame.CurWin[gameID])

		// 第二次获胜
		updateStatus(GameStatusWin, 2, 400.0)
		status2 := strings.Split(userData.SmallGame.Status[gameID], ",")
		t.Logf("Round 2: Status=%s, CurWin=%.2f", status2[0], userData.SmallGame.CurWin[gameID])

		// 第三次失败
		updateStatus(GameStatusLose, 0, 0.0)
		status3 := strings.Split(userData.SmallGame.Status[gameID], ",")
		t.Logf("Round 3: Status=%s, CurWin=%.2f", status3[0], userData.SmallGame.CurWin[gameID])

		// 验证最终状态是 lose
		if status3[0] != "lose" {
			t.Errorf("Expected final status to be 'lose', but got '%s'", status3[0])
		}

		// 验证 CurWin 被正确设置为 0
		if userData.SmallGame.CurWin[gameID] != 0.0 {
			t.Errorf("Expected CurWin to be 0.0 for lose status, but got %.2f", userData.SmallGame.CurWin[gameID])
		}
	})
}
