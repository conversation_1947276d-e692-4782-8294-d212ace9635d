package defi

import (
	"s2/define"
	"s2/modules/defi/jackpot"
	"s2/pb"
	"sync"
	"time"

	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"gorm.io/gorm"
)

func ticker() {
	go func() {
		ticker := time.NewTicker(time.Second)
		i := 0
		for range ticker.C {
			i++
			PoolMgr.CacheClear()
			if i%60 == 0 {
				PoolMgr.Devidend()
			}
		}
	}()
}

var PoolMgr = poolMgr{Id: 1, tableName: "defi_profitpool"}

type poolMgr struct {
	Id          int
	tableName   string
	cacheInput  float64
	cacheOutput float64
	mu          sync.Mutex
}

func (m *poolMgr) Cache(input, output float64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.cacheInput += input
	m.cacheOutput += output
}

// 缓存写入数据库
func (m *poolMgr) CacheClear() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.cacheInput == 0 && m.cacheOutput == 0 {
		return nil
	}
	data := map[string]interface{}{}
	if m.cacheInput != 0 {
		data["input"] = gorm.Expr("input + ?", m.cacheInput)
	}
	if m.cacheOutput != 0 {
		data["output"] = gorm.Expr("output + ?", m.cacheOutput)
	}
	pool := m.cacheInput - m.cacheOutput
	if pool != 0 {
		data["pool"] = gorm.Expr("pool + ?", pool)
	}
	if len(data) == 0 {
		return nil
	}
	if err := mdb.Default().Table(m.tableName).Where("id = ?", m.Id).Updates(data).Error; err != nil {
		log.Error("Clear cacheProfit Error:", err, " data:", data)
		return err
	}
	log.Info("defi CacheClear:", data)
	m.cacheInput = 0
	m.cacheOutput = 0
	return nil
}
func (m *poolMgr) GetData() *ProfitPool {
	var data ProfitPool
	err := mdb.Default().Table(m.tableName).Where("id = ?", m.Id).First(&data).Error
	if err != nil {
		return nil
	}
	return &data
}
func (m *poolMgr) Init() error {
	defer ticker()
	err := mdb.Default().Table(m.tableName).AutoMigrate(&ProfitPool{})
	if err != nil {
		return err
	}
	d := m.GetData()
	if d != nil {
		return nil
	}
	data := ProfitPool{
		Id:                m.Id,
		Fund:              500 * 10000,
		MinUp:             10 * 10000,
		MaxUp:             10000 * 10000,
		UpRate:            0.5,
		BalancedPrizePool: 500 * 10000,
		UpTimeGap:         4,
		UpNow:             false,
		Open:              true,
		BuyPanbitSlippage: 5,
	}
	if err := mdb.Default().Table(m.tableName).Create(data).Error; err != nil {
		return err
	}
	return nil

}

// 每隔4小时上报一次利润并且回购
func (m *poolMgr) Devidend() {
	var data = m.GetData()
	if data == nil {
		return
	}
	var safetyProfit = data.Pool + data.Fund - data.BalancedPrizePool //安全利润
	var waitUpProfit float64 = 0
	if safetyProfit > 0 {
		waitUpProfit = safetyProfit * data.UpRate
	}
	if waitUpProfit < data.MinUp {
		waitUpProfit = data.MinUp
	}
	if waitUpProfit > data.MaxUp {
		waitUpProfit = data.MaxUp
	}
	log.Warnf("上报利润,当前池子: %f, 基金: %f, 平衡: %f, 最低上报: %f", data.Pool, data.Fund, data.BalancedPrizePool, data.MinUp)
	log.Warnf("上报利润,等待上报: %f, 安全利润: %f", waitUpProfit, safetyProfit)
	if waitUpProfit < 0 {
		return
	}

	//TODO 调用合约回购合约
	m.UpdateDividend(waitUpProfit)      //更新利润池子
	var buyToken float64 = 1000         //假如回购到代币
	jackpot.Module.Accumulate(buyToken) //部分分配给jackpot
	//TODO 广播给用户jackpot变化

	jackpotInfo, err := jackpot.Module.Query()
	if err == nil {
		/*
			s2c.Broadcast(&pb.JackpotNtf{
				Grand:      jackpotInfo.Grand,
				Major:      jackpotInfo.Major,
				Minor:      jackpotInfo.Minor,
				Mini:       jackpotInfo.Mini,
				UpdateTime: jackpotInfo.Time,
			})
		*/
		err = message.Anycast(define.ModuleName.Lobby, &pb.JackpotChangeToLobbyReq{
			Data: &pb.JackpotNtf{
				Grand:      jackpotInfo.Grand,
				Major:      jackpotInfo.Major,
				Minor:      jackpotInfo.Minor,
				Mini:       jackpotInfo.Mini,
				UpdateTime: jackpotInfo.Time,
			},
		})
	}
	if err != nil {
		log.Info("JackpotTriggerResp Error:", err)
	}
}

func (m *poolMgr) UpdateDividend(upProfit float64) error {
	data := map[string]interface{}{}
	if upProfit != 0 {
		data["dividend"] = gorm.Expr("dividend + ?", upProfit)
		data["pool"] = gorm.Expr("pool + ?", -upProfit)
		deductRatio := 0.1
		if deductRatio > 0 {
			a := float64(int64(upProfit * deductRatio))
			data["balancedPrizePool"] = gorm.Expr("balancedPrizePool + ?", a)
			log.Warnf("拦截利润 %f:", a)
		}
	}
	if len(data) > 0 {
		if err_ := mdb.Default().Table(m.tableName).Where("id = ?", m.Id).Updates(data).Error; err_ != nil {
			return err_
		}
	}
	return nil
}
