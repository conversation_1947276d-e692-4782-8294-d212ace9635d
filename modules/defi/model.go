package defi

type Account struct {
	Address     string  `gorm:"column:address;primary_key;" json:"address"` // address
	UserID      int64   `gorm:"column:userId;index" json:"userId"`
	Hp          float64 `gorm:"column:hp;"`
	TokenAmount float64 `gorm:"column:tokenAmount;"`
}

type ProfitPool struct {
	Id                int     `gorm:"column:id;primary_key;index" json:"id"`
	Fund              float64 `gorm:"column:fund" json:"fund"`                           // config 项目方注入奖池基金
	MinUp             float64 `gorm:"column:minUp" json:"minUp"`                         // config 每次最少上报
	MaxUp             float64 `gorm:"column:maxUp" json:"maxUp"`                         // config 每次最多上报
	UpRate            float64 `gorm:"column:upRate" json:"upRate"`                       // config 单次上报占本次奖池比例 0.5
	BalancedPrizePool float64 `gorm:"column:balancedPrizePool" json:"balancedPrizePool"` // config 平衡奖池 上报金额=(总利润超过-平衡奖池)*Rate 1000 * 10000, //1000cake
	BuyPanbitSlippage int     `gorm:"column:BuyPanbitSlippage" json:"BuyPanbitSlippage"` // 购买滑点 5
	Input             float64 `gorm:"column:input" json:"input"`                         // 当前用户累计投注
	Output            float64 `gorm:"column:output" json:"output"`                       // 当前用户累计奖励
	Pool              float64 `gorm:"column:pool" json:"pool"`                           // 当前累计奖池 等待上报的金额
	Dividend          float64 `gorm:"column:dividend" json:"dividend"`                   // 已经上报的利润
	UpTimeGap         int     `gorm:"column:upTimeGap" json:"upTimeGap"`                 // 上报时间间隔小时
	UpNow             bool    `gorm:"column:upNow" json:"upNow"`                         // 立即上报
	Open              bool    `gorm:"column:open" json:"open"`                           // 上报开启
}
