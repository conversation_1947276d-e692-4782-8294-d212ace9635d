package jackpot

import "s2/pb"

// PayoutResult 保存触发中奖结果，包括中奖类型和中奖金额
type PayoutResult struct {
	Prize string // 中奖类型（"Mini"、"Minor"、"Major"、"Grand"）
	Award int64  // 中奖金额
}
type IJackpot interface {
	// Accumulate 累计接口：入口参数只需要 systemProfit，比例从配置中读取
	// 将系统利润中 jackpotRatio 部分分配到各子奖池，根据各子奖池比例累加；另外剩余资金归入备用奖池
	Accumulate(systemProfit float64)
	// TriggerPayout 触发接口，无需任何入参，根据当前奖池状态及配置随机确定中奖类型，返回中奖类型和金额。
	// 对应规则：玩家中奖后获得对应子奖池内全部奖金，然后从备用奖池中扣除固定初始金额恢复子奖池余额。
	TriggerPayout() (PayoutResult, error)
	// Query 查询接口，返回当前奖池状态
	Query() (JackpotModel, error)
	// GetSpinDetail 根据中奖结果生成一次完整的旋转记录详情，结果报错一致，无关数据保证随机性
	// 包括每次旋转的页面状态（JackpotPage 数组）以及最终中奖结果（JackpotWinResult）。
	// 完整实现思路
	// 旋转页面（JackpotPage）生成逻辑
	// Grids 数组
	// 每一页的 Grids 数组按照固定的 5×3 网格顺序生成。
	// 当某一页触发合成（即某一竖列中三个格子全部为“1”并合并），这一合并列会在当前页面中保持原样；但在下一页开始时，合并列会展示为全 1 状态（反映合成转轴移动到最右侧后应显示全1）。
	// Texts 数组
	// 只有在触发合并后（即合并条件满足时）才重新生成。
	// 新生成的 Texts 数组包含滚轴上展示的奖励文本，具有随机性：例如可随机显示数字奖励，也可能为 MINI、MINOR、MAJOR 或 GRAND，具体范围依据当前合并的列数。
	// LeftCount 数值
	// 每一页都应显示当前状态下剩余的免费旋转次数。
	// 如果在旋转过程中出现新的指定图标（"1"），但尚未触发合并，则剩余的免费旋转次数保持原值（不重置为 3）；只有在合并触发时，才将免费旋转次数重置为 3。
	// 免费旋转及开奖流程模拟
	// 模拟整个免费旋转过程。
	// 每次旋转（即每一页）都记录当时的格子状态、展示文本以及当前剩余的免费旋转次数。
	// 在整个过程中，当触发合并时立即重置免费旋转次数为 3，并更新下次页面中的状态（例如合成转轴对应列在下一页显示为全1）。
	// 当所有免费旋转次数耗尽，或者没有新指定图标触发新的免费旋转时，即进入开奖阶段。
	// 开奖页面与最终奖励处理
	// 最后一页必须作为开奖旋转页面：
	// 这一页的 Texts 数组应展示最终中奖结果，且与传入的 PayoutResult 保持一致。
	// 根据 PayoutResult 的中奖类型，展示数字奖励或者对应的 Jackpot 奖励（MINI、MINOR、MAJOR、GRAND），同时金额也需匹配。
	// JackpotSpin 对象的 Reward 字段直接根据 PayoutResult 进行映射，确保整个旋转记录和最终结果完全一致。
	// 总结
	// 页面数据：每页按照5×3顺序生成，合并发生后的列在当前页面保持原样，但在下一页显示时变为全1。
	// Texts 更新：仅在每次触发合并后重新生成，且会包含一定随机性。
	// 免费旋转次数：每页均体现剩余免费旋转次数，只有触发合并时才重置为3，不因出现新“1”而立即重置。
	// 最终结果：最后一页为开奖页面，其显示内容（Texts 和 Reward）与传入的 PayoutResult 保持一致。
	// 全局模拟：需要完整模拟整个免费旋转过程，包括每次旋转的状态和最终的开奖结果。
	GetSpinDetail(result PayoutResult) (pb.JackpotSpin, error)
}
