package jackpot

// JackpotModel 是奖池在数据库中的存储模型
type JackpotModel struct {
	Id         int     `gorm:"column:id;primary_key;index" json:"id"`
	Mini       float64 `gorm:"column:mini" json:"mini"`             // Mini奖池
	Minor      float64 `gorm:"column:minor" json:"minor"`           // Minor奖池
	Major      float64 `gorm:"column:major" json:"major"`           // Major奖池
	Grand      float64 `gorm:"column:grand" json:"grand"`           // Grand奖池
	BackupPool float64 `gorm:"column:backupPool" json:"backupPool"` // 备用奖池金额，中奖后从此扣除用于补充子奖池
	Time       int64   `gorm:"column:time" json:"time"`             // 更新时间
}

// Config 定义累计和触发的相关配置（可按实际情况改成从配置文件加载）
type Config struct {
	// 系统利润中分配到 Jackpot 奖池的比例（例如 0.1 表示 10% 的利润进入 Jackpot 奖池）
	JackpotRatio float64

	// 各子奖池在 Jackpot 奖池中的占比
	MiniRatio  float64
	MinorRatio float64
	MajorRatio float64
	GrandRatio float64

	// 每个子奖池中奖后重置时从备用奖池扣除的固定初始金额
	MiniInit  float64
	MinorInit float64
	MajorInit float64
	GrandInit float64

	// 奖项中奖概率（注意：键必须和下面 TriggerPayout 中用到的奖项名称保持一致）
	PrizeProbabilities map[string]float64
}

// 全局配置信息（在实际应用中可以从外部加载）
var config = Config{
	JackpotRatio: 0.1,
	MiniRatio:    0.1,
	MinorRatio:   0.2,
	MajorRatio:   0.3,
	GrandRatio:   0.4,
	MiniInit:     100.0,
	MinorInit:    200.0,
	MajorInit:    500.0,
	GrandInit:    1000.0,
	PrizeProbabilities: map[string]float64{
		"Mini":  0.4,
		"Minor": 0.3,
		"Major": 0.2,
		"Grand": 0.1,
	},
}
