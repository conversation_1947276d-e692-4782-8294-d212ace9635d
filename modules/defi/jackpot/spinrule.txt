游戏规则整理

界面和格子排列
游戏界面为一个固定的 5×3 网格，总计 15 个格子。
每个格子显示的图标只有两种状态：0 或 1。
起始时，通常中心一列的三个格子均显示“1”，确保玩家获得最初的免费旋转机会。
指定图标和触发合并
“1”图标是触发合并的指定图标，只有“1”才可参与合并。
当任一竖列中三个格子全部为“1”时，该列满足合并条件，将触发合并操作。
合并后的竖列会移动到游戏界面的最右侧，构成所谓的“合成转轴”。但在触发合并的当前页面中，相关列仍保持原样；到了下一次旋转页面，该列则全部展示为 1。
滚轴文本（Texts）的生成
只有在触发合并时，才会重新生成一次新的滚轴文本数组。
该数组展示的文本既可以是数字奖励，也可能是特殊奖励标识（MINI、MINOR、MAJOR、GRAND）。
生成时会引入随机性，确保每次触发合并后展示的文字具有随机性和多样性。
免费旋转次数的处理
游戏一开始固定给予 3 次免费旋转机会。
每个旋转页面（JackpotPage）中，都需要显示当前状态下剩余的免费旋转次数（LeftCount）。
当旋转过程中出现新的“1”图标但未触发合并时，免费旋转次数不会立即重置；只有在发生合并时才会将免费旋转次数重置为 3。
最终中奖结果
整场旋转过程模拟多个旋转页面，最终的开奖页面即最后一个页面。
最后一页代表开奖阶段，其 Texts 数组展示的文本必须与传入的 PayoutResult 完全一致。
若 PayoutResult 表示数字奖励，则 JackpotWinResult 中的 Reward 字段保存具体数字，Jackpot 字段可能为空；若是 Jackpot 奖励（MINI、MINOR、MAJOR、GRAND），则对应的文字显示和中奖金额都要与传入的 PayoutResult 保持一致。


实现过程说明

1. 模拟整个免费旋转过程
初始状态
启动时设定初始免费旋转次数（LeftCount）为 3。
根据初始状态生成第一张旋转页面（JackpotPage）的 Grids 数组（按照固定 5×3 顺序），以及初步的 Texts 数组（一般由中心列预设的“1”图标得到）。
每一页的生成
Grids 数组：
每次旋转都重新生成一个固定顺序的 5×3 网格。
对于当前触发合并的列：在当前页面显示时保持原样；但到下一页时，这些列必须展现为全 1，反映合成转轴向右移动的结果。
Texts 数组：
当页面中发生合并（即某列三个格子都为 1）时，触发新的合并，此时重新生成滚轴文本数组。
该数组会随机包含数字奖励或特殊奖励标识（MINI、MINOR、MAJOR、GRAND），并根据合并的列数量可能控制可出现的文本范围。
免费旋转次数（LeftCount）：
每一页都会展示当前剩余的免费旋转次数。
如果在旋转过程中虽然出现了新的“1”图标但未触发合并，则免费旋转次数保持不变。
只有在页面发生合并时，免费旋转次数才会被重置为 3，反映一次合并奖励操作后的状态更新。
2. 生成开奖页面
最后一页的确定
模拟过程持续进行，直至满足进入开奖阶段的条件（即没有新指定图标触发或免费旋转次数耗尽）。
最后一页为开奖页面，此时的 Texts 数组必须展示最终中奖结果，其显示内容（数字或者特殊标识）应与传入的 PayoutResult 完全一致。
同时，JackpotSpin 中的 Reward 字段将通过映射 PayoutResult 得到，确保返回的中奖数值和类型与之前模拟过程一致。
3. 组装返回结果
JackpotSpin 对象组装
将整个旋转过程中的每个页面（JackpotPage 数组）收集到最终结果中。
最终结果字段 JackpotSpin.Reward 将直接从 PayoutResult 映射，保证最终的中奖结果与预设一致。
返回的 JackpotSpin 对象即包含了整个免费旋转过程的详细页面记录与最终的中奖数据。
