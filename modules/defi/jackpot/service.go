package jackpot

import (
	"errors"
	"math"
	"math/rand"
	"s2/common/c2s"
	"s2/pb"
	"strconv"
	"time"

	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"gorm.io/gorm/clause"
)

var Module module = module{tableName: "defi_jackpot", id: 1}

type module struct {
	tableName string
	id        int
}

func (m *module) Init() error {
	c2s.Response(onJackpotReq)

	err := mdb.Default().Table(m.tableName).AutoMigrate(&JackpotModel{})
	if err != nil {
		return err
	}
	_, err = m.Query()
	if err != nil {
		data := JackpotModel{
			Id:         m.id,
			Mini:       0,
			Minor:      0,
			Major:      0,
			Grand:      0,
			BackupPool: 0,
			Time:       time.Now().Unix(),
		}
		if err := mdb.Default().Table(m.tableName).Create(data).Error; err != nil {
			return err
		}
	}
	return err
}

// Accumulate implements the Accumulate method of the IJackpot interface
// It accumulates the jackpot pools by reading the systemProfit and distributing it according to the configured ratios.
func (m *module) Accumulate(systemProfit float64) {
	// tx := j.Db.Begin()Query
	tx := mdb.Default().TxReadCommit()
	// Read the current Jackpot model from the database
	var jackpot JackpotModel
	err := tx.Table(m.tableName).Clauses(clause.Locking{Strength: "UPDATE"}).Where("id = ?", m.id).First(&jackpot).Error // Assuming we are working with the first jackpot entry
	if err != nil {
		tx.Rollback()
		log.Error("Failed to read JackpotModel:", err)
		return
	}

	// Calculate the amount to be added to each pool based on systemProfit
	miniAmount := systemProfit * config.MiniRatio
	minorAmount := systemProfit * config.MinorRatio
	majorAmount := systemProfit * config.MajorRatio
	grandAmount := systemProfit * config.GrandRatio

	// Update Jackpot model in the database
	jackpot.Mini += miniAmount
	jackpot.Minor += minorAmount
	jackpot.Major += majorAmount
	jackpot.Grand += grandAmount
	jackpot.BackupPool += systemProfit * config.JackpotRatio // Add remaining to backup pool
	jackpot.Time = time.Now().Unix()
	err = tx.Table(m.tableName).Save(&jackpot).Error
	if err != nil {
		tx.Rollback()
		log.Error("Failed to update JackpotModel:", err)
		return
	}
	tx.Commit()
	log.Info("Jackpot pools updated successfully.")
}

// TriggerPayout implements the TriggerPayout method of the IJackpot interface
// It randomly triggers a payout from the jackpot pools and returns the prize type and amount.
func (m *module) TriggerPayout(debug bool) (PayoutResult, error) {
	if true {
		if debug || rand.Int31n(10000) == 0 {
			l4 := []string{"", "Mini", "Minor", "Major", "Grand", "Mini", "Minor", "Major", "Grand", "Grand"}
			return PayoutResult{Prize: l4[rand.Int31n(int32(len(l4)))], Award: rand.Int63n(100000)}, nil
		}
		return PayoutResult{Award: 0}, nil
	}
	tx := mdb.Default().TxReadCommit()
	// Read the current Jackpot model from the database
	var jackpot JackpotModel
	err := tx.Table(m.tableName).Clauses(clause.Locking{Strength: "UPDATE"}).Where("id = ?", m.id).First(&jackpot).Error
	if err != nil {
		tx.Rollback()
		log.Error("Failed to read JackpotModel:", err)
		return PayoutResult{}, err
	}
	// Calculate which prize to award based on the configured probabilities
	prizeType := m.randomPrizeType()
	var payoutAmount float64

	// Award the full amount from the corresponding prize pool
	switch prizeType {
	case "Mini":
		payoutAmount = jackpot.Mini
		jackpot.Mini = 0
	case "Minor":
		payoutAmount = jackpot.Minor
		jackpot.Minor = 0
	case "Major":
		payoutAmount = jackpot.Major
		jackpot.Major = 0
	case "Grand":
		payoutAmount = jackpot.Grand
		jackpot.Grand = 0
	default:
		tx.Rollback()
		log.Error("Unknown prize type:", prizeType)
		return PayoutResult{}, errors.New("Unknown prize type")
	}

	// Deduct the fixed amount from the backup pool to replenish the jackpot pool
	jackpot.BackupPool -= config.MiniInit // You can adjust based on the prize type
	jackpot.Time = time.Now().Unix()
	if jackpot.BackupPool < 0 {
		tx.Rollback()
		log.Error("Insufficient backup pool funds:", jackpot.BackupPool)
		return PayoutResult{}, errors.New("Insufficient backup pool funds")
	}

	// Save updated JackpotModel
	err = tx.Table(m.tableName).Save(&jackpot).Error
	if err != nil {
		tx.Rollback()
		log.Error("Failed to update JackpotModel after payout:", err)
		return PayoutResult{}, err
	}

	tx.Commit()

	// Return the result of the payout
	return PayoutResult{Prize: prizeType, Award: int64(payoutAmount)}, nil
}

// Query implements the Query method of the IJackpot interface
// It queries and returns the current state of the jackpot pools.
func (m *module) Query() (JackpotModel, error) {
	var jackpot JackpotModel
	err := mdb.Default().Table(m.tableName).Where("id = ?", m.id).First(&jackpot).Error
	if err != nil {
		log.Error("Failed to retrieve JackpotModel:", err)
		return JackpotModel{}, err
	}
	return jackpot, nil
}

// randomPrizeType selects a random prize based on configured probabilities
func (j *module) randomPrizeType() string {
	rand.Seed(time.Now().UnixNano())
	randomValue := rand.Float64()

	// Use cumulative probability to select a prize
	if randomValue < config.PrizeProbabilities["Mini"] {
		return "Mini"
	} else if randomValue < config.PrizeProbabilities["Mini"]+config.PrizeProbabilities["Minor"] {
		return "Minor"
	} else if randomValue < config.PrizeProbabilities["Mini"]+config.PrizeProbabilities["Minor"]+config.PrizeProbabilities["Major"] {
		return "Major"
	}
	return "Grand"
}

// 定义常量
const (
	TotalRows    = 5
	TotalColumns = 3
)

func (j *module) NextGrids(grids []int32) []int32 {
	return nil
}
func (j *module) RandTexts(rount int) []string {
	s := []string{}
	l1 := []string{"Mini"}
	l2 := []string{"Mini", "Minor"}
	l3 := []string{"Mini", "Minor", "Major"}
	l4 := []string{"Mini", "Minor", "Major", "Grand"}
	var max int64 = 10000
	var min int64 = 100
	for i := 0; i < 6; i++ {
		multiplier := int(math.Pow(10, float64(rount)))
		num := int(rand.Int63n(max-min)+min) * multiplier
		s = append(s, strconv.Itoa(num))
		if rount == 2 {
			s = append(s, l1...)
		} else if rount == 3 {
			s = append(s, l2...)
		} else if rount == 4 {
			s = append(s, l3...)
		} else if rount >= 5 {
			s = append(s, l4...)
		}
	}
	return s
}

func (j *module) ZeroGrids() []int32 {
	grids := make([]int32, TotalRows*TotalColumns)
	for i := 0; i < len(grids); i++ {
		grids[i] = 0 //rand.Int31n(2) // 随机生成0或1
	}
	return grids
}

// GetSpinDetail 根据中奖结果生成一次完整的旋转记录详情
func (j *module) GetSpinDetail(result PayoutResult) (pb.JackpotSpin, error) {
	// 生成随机种子
	rand.Seed(time.Now().UnixNano())
	// 定义最终的 JackpotSpin 结构
	var mergeRound int32 = 0 //触发合并的有几轮
	var spinDetail pb.JackpotSpin
	var pages []*pb.JackpotPage
	// 第一次必触发合并的第1轮
	grids := j.ZeroGrids()
	grids[6] = 1
	grids[7] = 1
	grids[8] = 1
	page := &pb.JackpotPage{
		Grids:      grids,
		MergeRound: mergeRound,
		MergeRow:   2,
		LeftCount:  3,
		RoundEnd:   true,
		Texts:      j.RandTexts(0),
	}
	pages = append(pages, page)
	spinDetail.Reward = &pb.JackpotWinResult{
		Reward:  int32(result.Award),
		Jackpot: result.Prize,
	}
	switch result.Prize {
	case "Mini":
		mergeRound = rand.Int31n(4) + 2
	case "Minor":
		mergeRound = rand.Int31n(3) + 3
	case "Major":
		mergeRound = rand.Int31n(2) + 4
	case "Grand":
		mergeRound = 0 + 5
	default:
		mergeRound = 1
	}
	for i := 1; i <= int(mergeRound); i++ {
		texts := j.RandTexts(i)
		var mergeRow int32 = 0 //合并的竖列下标
		if i >= 5 {            //超过5轮后面不需要转了
			mergeRow = -1
			break
		} else if int(mergeRound) == i { //最后一轮
			mergeRow = -1
			if spinDetail.Reward.Jackpot != "" {
				texts = append(texts, spinDetail.Reward.Jackpot)
			} else {
				texts = append(texts, strconv.Itoa(int(spinDetail.Reward.Reward)))
			}
		} else {
			mergeRow = rand.Int31n(TotalRows - int32(i))
		}
		var leftCount int32 = 2
		no := 0
		for {
			no++
			grids := j.ZeroGrids()
			if len(pages) > 1 { //继承
				lastItem := pages[len(pages)-1]
				if no == 1 { //继承上一轮
					s := int(lastItem.MergeRow) * TotalColumns
					for z := 0; z < len(lastItem.Grids); z++ { //上一次合并列的右边向左移动
						if lastItem.Grids[z] == 1 {
							if z < s {
								grids[z] = lastItem.Grids[z]
							} else if z > s+2 {
								grids[z-3] = lastItem.Grids[z]
							}
						}
					}
				} else { //继承本轮上一次
					for z := 0; z < len(grids); z++ {
						grids[z] = lastItem.Grids[z]
					}
				}
			}
			sIndex := TotalRows*TotalColumns - i*TotalColumns
			for index := sIndex; index < len(grids); index++ {
				grids[index] = -1 //已经合并的图标
			}
			page := &pb.JackpotPage{
				MergeRound: int32(i),
				MergeRow:   mergeRow,
				LeftCount:  leftCount,
				Texts:      texts,
				RoundEnd:   false,
			}
			rowSelectMap := map[int]int{}
			for z := 0; z < len(grids); z++ { //统计竖列有几个
				if grids[z] == 1 {
					rowIndex := z / TotalColumns
					rowSelectMap[rowIndex]++
				}
			}
			var newDrop = false
			for z := 0; z < len(grids); z++ {
				if grids[z] == -1 {
					continue
				}
				if grids[z] == 1 {
					continue
				}
				rowIndex := z / TotalColumns
				if mergeRow == int32(rowIndex) { //要合并的列
					if rowSelectMap[rowIndex] < TotalColumns {
						if leftCount == 0 { //最后一次要保证合并的竖列填满
							rowSelectMap[rowIndex]++
							grids[z] = 1
							newDrop = true
						} else if rand.Int31n(2) == 0 {
							rowSelectMap[rowIndex]++
							grids[z] = 1
							newDrop = true
						}
					}
				} else { //不能完成合并的列
					if rowSelectMap[rowIndex] < TotalColumns-1 && rand.Int31n(4) == 0 {
						rowSelectMap[rowIndex]++
						grids[z] = 1
						newDrop = true
					}
				}
				page.Grids = grids
			}
			page.NewDrop = newDrop
			pages = append(pages, page)
			if newDrop {
				leftCount = 3
			}
			if (leftCount == 0) || rowSelectMap[int(mergeRow)] >= TotalColumns {
				page.RoundEnd = true
				break
			}
			leftCount--
		}
	}
	spinDetail.Pages = pages
	// spinDetail = j.Salting(spinDetail)
	jp, _ := j.Query()
	spinDetail.Grand = jp.Grand
	spinDetail.Major = jp.Major
	spinDetail.Minor = jp.Minor
	spinDetail.Mini = jp.Mini
	return spinDetail, nil
}
