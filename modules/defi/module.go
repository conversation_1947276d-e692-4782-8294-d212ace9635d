package defi

import (
	"s2/common/c2s"
	"s2/define"
	"s2/modules/defi/jackpot"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/message"
)

type module struct {
	iface.IModule
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	return m
}

func (m module) Name() string {
	return define.ModuleName.Defi
}

func (m *module) Init() error {
	c2s.Enable(m)
	c2s.Response(onDefiRankReq)
	//cs2/message 客户端/服务器内部
	message.Response(m, onDefiQueryJackpotReq)
	message.Handle(m, onDefiInputReq)
	message.Response(m, onDefiTryJackpotReq)
	jackpot.Module.Init()
	// err := mdb.Default().Table("defi_account").AutoMigrate(&Account{})
	err := PoolMgr.Init()
	return err
}

func (m *module) Run() error {
	return nil
}

func (m *module) Exit() error {
	return nil
}
