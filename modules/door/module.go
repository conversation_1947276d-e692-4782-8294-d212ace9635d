package door

import (
	"fmt"
	"s2/define"
	"time"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/utils/hs"
)

type module struct {
	iface.IModule
	*hs.HttpService
	wsa *wsAgent
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewEventLoop(basic.DefaultMQLen),
		wsa:     newAgent(),
	}
	m.HttpService = hs.NewHttpService()
	return m
}

func (m module) Name() string {
	return define.ModuleName.Door
}

func (m *module) Init() error {
	m.POST("/", defaultHandler)
	m.GET("/ws", m.wsa.Accept)
	message.Handle(m, m.onS2CPackageMsg, message.WithoutLog())
	message.Handle(m, m.onS2CBroadcastMsg, message.WithoutLog())
	message.Handle(m, m.onS2CMulticastMsg, message.WithoutLog())
	return nil
}

func (m *module) Run() error {
	return m.ListenAndServe(fmt.Sprintf(":%d", conf.Num[int]("door.port")))
}

func (m *module) Exit() error {
	m.IModule.Exit()
	m.wsa.closeALL()
	return m.Stop(time.Second * 10)
}
