package door

import (
	"s2/pb"
)

func (m *module) onS2CPackageMsg(body *pb.S2CPackageMsg) {
	conn := m.wsa.getConn(body.UserID)
	if conn != nil {
		conn.writeMessage(body.Body)
	}
}

func (m *module) onS2CBroadcastMsg(body *pb.S2CBroadcastMsg) {
	m.wsa.rw.RLock()
	defer m.wsa.rw.RUnlock()
	for _, conn := range m.wsa.conns {
		conn.writeMessage(body.Body)
	}
}

func (m *module) onS2CMulticastMsg(body *pb.S2CMulticastMsg) {
	for _, uid := range body.UserIDs {
		conn := m.wsa.getConn(uid)
		if conn != nil {
			conn.writeMessage(body.Body)
		}
	}
}
