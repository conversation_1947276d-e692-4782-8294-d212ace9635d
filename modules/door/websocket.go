package door

import (
	"context"
	"math/rand"
	"net/http"
	"s2/common/cache"
	"s2/define"
	"s2/pb"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/message/codec"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
)

type wsAgent struct {
	conns    map[int64]*wsConn
	rw       sync.RWMutex
	upgrader *websocket.Upgrader
}

func newAgent() *wsAgent {
	wsa := &wsAgent{}
	wsa.conns = map[int64]*wsConn{}
	wsa.upgrader = &websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // 跨域
		},
	}
	conc.Go(wsa.ticker)
	return wsa
}

func (wsa *wsAgent) Accept(ctx *gin.Context) {
	isLimit := checkSpeedLimit(ctx)
	if isLimit {
		ctx.String(http.StatusBadRequest, "")
		log.Warnf("checkSpeedLimit %s", ctx.ClientIP())
		return
	}
	token := ctx.Query("token")
	if len(token) == 0 {
		ctx.String(http.StatusBadRequest, "")
		log.Warn("ws conn token empty")
		return
	}
	var wsc *wsConn
	if token == "guest" {
		wsc = wsa.newConn(-rand.Int63())
		wsc.writeChan = make(chan []byte, 16)
		wsa.Upgrade(wsc, ctx)
		return
	}
	isNew := len(ctx.Query("recover")) == 0
	bc, err := cache.QueryUserBasicInfoByToken(token)
	if err != nil {
		log.Error(err)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
		return
	}
	if isNew {
		wsc = wsa.newConn(bc.ID)
		wsc.userID = bc.ID
		wsc.serverID = bc.ServerID
		wsc.writeChan = make(chan []byte, 256)
	} else {
		wsc = wsa.getConn(bc.ID)
		if wsc == nil {
			ctx.String(http.StatusGatewayTimeout, "")
			return
		}
		wsc.Close(pb.SUCCESS)
	}
	resp, err := message.Request[pb.UserLoginResp](bc.ServerID, &pb.UserLoginReq{
		UserID: bc.ID,
		GateID: conf.ServerID,
	})
	if err != nil {
		log.Error(err)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	if resp.Code != pb.SUCCESS {
		log.Error("UserLoginResp Code %d", resp.Code)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	wsa.Upgrade(wsc, ctx)
}

func (wsa *wsAgent) getConn(uid int64) *wsConn {
	wsa.rw.RLock()
	defer wsa.rw.RUnlock()
	return wsa.conns[uid]
}

func (wsa *wsAgent) newConn(uid int64) *wsConn {
	if wsc := wsa.getConn(uid); wsc != nil {
		wsc.Close(pb.LOG_IN_OTHER_DEVICE)
	}
	wsa.rw.Lock()
	defer wsa.rw.Unlock()
	wsa.conns[uid] = &wsConn{}
	wsa.conns[uid].pausing.Store(true)
	return wsa.conns[uid]
}

func (wsa *wsAgent) ticker() {
	tick := time.NewTicker(time.Second * 30)
	for {
		select {
		case <-system.RootCtx().Done():
			return
		case <-tick.C:
			wsa.doClean()
			wsa.onlineCount()
		}
	}
}

func (wsa *wsAgent) doClean() {
	defer utils.RecoverPanic()
	wsa.rw.Lock()
	defer wsa.rw.Unlock()
	for uid, conn := range wsa.conns {
		if !conn.pausing.Load() {
			continue
		}
		if conn.lastTime.Load()+60 < time.Now().Unix() {
			delete(wsa.conns, uid)
			message.Cast(conn.serverID, &pb.UserOfflineMsg{UserID: uid})
		}
	}
}

func (wsa *wsAgent) onlineCount() {
	defer utils.RecoverPanic()
	wsa.rw.Lock()
	defer wsa.rw.Unlock()
	message.Stream.Anycast(define.ModuleName.Statistics, &pb.StatisticsUserEventReq{
		EventType:   pb.USER_ONLINE,
		Timestamp:   uint32(time.Now().Unix()),
		OnlineCount: int32(len(wsa.conns)),
	})
	log.Infof("onlineCount %d", len(wsa.conns))
}

func (wsa *wsAgent) closeALL() {
	wsa.rw.Lock()
	defer wsa.rw.Unlock()
	for _, wsc := range wsa.conns {
		wsc.Close(pb.SERVER_SHUTDOWN)
	}
}

type wsConn struct {
	ctx       context.Context
	ctxCancel func()
	userID    int64           // 用户标识
	conn      *websocket.Conn // ws连接
	serverID  uint32          // user对应serverID
	writeChan chan []byte     // 写缓冲
	cacheMsg  []byte          // 缓存写失败消息等待重连
	lastTime  atomic.Int64    // 上次消息时间
	pausing   atomic.Bool     // 连接丢失
}

func (wsc *wsConn) writeMessage(b []byte) {
	select {
	case wsc.writeChan <- b:
	default:
		log.Errorf("wsConn writeMessage chan full %d", wsc.userID)
	}
}

func (wsc *wsConn) readLoop() {
	var (
		countPerSecond int32
		lastTime       int64
	)
	for {
		if utils.ContextDone(wsc.ctx) {
			return
		}
		//客户端请求
		_, b, err := wsc.conn.ReadMessage()
		now := time.Now().Unix()
		if err != nil {
			wsc.Close(pb.SERVER_ERROR)
			log.Infof("readLoop ReadMessage err %v", err)
			return
		}
		if now != lastTime {
			countPerSecond = 0
		}
		countPerSecond++
		if countPerSecond > 64 {
			log.Errorf("wsConn readLoop too fast, uid: %d, addr: %s", wsc.userID, wsc.conn.RemoteAddr())
			time.Sleep(time.Second) // 降速
		}
		wsc.lastTime.Store(time.Now().Unix())
		if len(b) == 0 { // 心跳包
			wsc.conn.WriteMessage(websocket.BinaryMessage, nil)
			continue
		}
		if wsc.serverID == 0 {
			continue
		}
		//转发给user
		message.Cast(wsc.serverID, &pb.C2SPackageMsg{
			UserID: wsc.userID,
			Body:   b,
			GateID: conf.ServerID,
		})
	}
}

func (wsc *wsConn) writeLoop() {
	for {
		select {
		case <-wsc.ctx.Done():
			return
		case b := <-wsc.writeChan:
			err := wsc.conn.WriteMessage(websocket.BinaryMessage, b)
			if err == nil {
				continue
			}
			wsc.cacheMsg = b
			wsc.Close(pb.SERVER_ERROR)
		}
	}
}

func (wsc *wsConn) Close(errCode pb.ErrCode) {
	if wsc.pausing.CompareAndSwap(false, true) {
		wsc.conn.WriteMessage(websocket.BinaryMessage, codec.Encode(&pb.KickingMsg{
			Code: errCode,
		}))
		wsc.conn.Close()
		wsc.ctxCancel()
	}
}

func (wsa *wsAgent) Upgrade(wsc *wsConn, ctx *gin.Context) {
	conn, err := wsa.upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		log.Error(err)
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	wsc.conn = conn
	wsc.ctx, wsc.ctxCancel = context.WithCancel(system.RootCtx())
	wsc.lastTime.Store(time.Now().Unix())
	wsc.pausing.Store(false)
	conc.Go(wsc.readLoop)
	conc.Go(wsc.writeLoop)
}
