package gm

import (
	"fmt"
	"s2/define"
	"s2/modules/gm/domain"
	"s2/modules/gm/domain/adminops"
	"s2/modules/gm/domain/impl"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils/hs"
)

type module struct {
	iface.IModule
	domain *domain.Domain
	*hs.HttpService
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	m.domain = domain.New(m)
	return m
}

func (m module) Name() string {
	return define.ModuleName.GM
}

func (m *module) Init() error {
	m.HttpService = hs.NewHttpService()
	impl.Init(m.domain)
	adminops.Init(m.HttpService, m.domain.AccountCase().Auth)
	return nil
}

func (m *module) Run() error {
	return m.ListenAndServe(fmt.Sprintf(":%d", conf.Num[int]("gm.port")))
}

func (m *module) Exit() error {
	err := m.Stop()
	if err != nil {
		log.Error(err)
	}
	m.IModule.Exit()
	return nil
}
