package domain

import (
	"s2/modules/gm/domain/api"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/iface"
)

type Domain struct {
	iface.IModule
	domainops.IRoot
}

func New(m iface.IModule) *Domain {
	d := &Domain{
		IRoot:   domainops.New(m, caseMaxIndex),
		IModule: m,
	}
	return d
}

const (
	caseMinIndex = iota
	AccountIndex
	BIIndex
	ConfigIndex
	caseMaxIndex
)

func (d *Domain) AccountCase() api.IAccount {
	return d.GetCase(AccountIndex).(api.IAccount)
}

func (d *Domain) BICase() api.IBI {
	return d.GetCase(BIIndex).(api.IBI)
}

func (d *Domain) ConfigCase() api.IConfig {
	return d.GetCase(ConfigIndex).(api.IConfig)
}
