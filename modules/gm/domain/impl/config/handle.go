package config

import (
	"encoding/json"
	"reflect"
	"s2/pb"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/conf/form"
	"github.com/jfcwrlight/core/infra/mgdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils/hs"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func (uc *useCase) onConfigListReq(ctx *gin.Context, _ *pb.FormListReq) {
	resp := &pb.FormListResp{
		Code: pb.SUCCESS,
		List: map[string]*pb.FormSave{},
	}
	for k, v := range formDesc {
		obj := reflect.New(v.typeOf)
		err := mgdb.Default().Collection("configs").FindOne(ctx, bson.M{"_id": k}).Decode(obj)
		if err != nil && err != mongo.ErrNoDocuments {
			hs.Err(ctx, pb.Error{
				Code: pb.SERVER_ERROR,
			})
			return
		}
		b, _ := json.Marshal(obj)
		resp.List[k] = &pb.FormSave{
			Key:  k,
			Desc: v.desc,
			Data: string(b),
		}
	}
	hs.OK(ctx, resp)
}

func (uc *useCase) onConfigSaveReq(ctx *gin.Context, body *pb.FormSaveReq) {
	desc, ok := formDesc[body.Key]
	if !ok {
		hs.Err(ctx, pb.Error{
			Code: pb.PARAM_ERROR,
			Msg:  "form key not found",
		})
		return
	}
	obj := reflect.New(desc.typeOf).Interface()
	err := json.Unmarshal([]byte(body.Data), obj)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	if err = obj.(form.IForm).Check(); err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: err.Error()})
		return
	}
	_, err = mgdb.Default().Collection("configs").ReplaceOne(ctx, bson.M{"_id": body.Key}, obj)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{
			Code: pb.SERVER_ERROR,
		})
		return
	}
	hs.OK(ctx, pb.FormSaveResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onConfigUpdateReq(ctx *gin.Context, body *pb.ConfigUpdateReq) {
	hs.OK(ctx, pb.ConfigUpdateResp{
		Code: pb.SUCCESS,
	})
}
