package config

import (
	"encoding/json"
	"reflect"
	"s2/modules/gm/domain"
	"s2/modules/gm/domain/adminops"
	"s2/modules/gm/domain/api"
	"s2/pb"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/conf/form"
	"github.com/jfcwrlight/core/utils"
)

var uc *useCase

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IConfig](d, domain.ConfigIndex, uc)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onConfigListReq)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onConfigSaveReq)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onConfigUpdateReq)
}

var formDesc = map[string]struct {
	desc   string
	typeOf reflect.Type
}{}

func RegForm(config form.IForm) {
	name := config.FormName()
	node := &formDescNode{}
	parseToFormDesc(reflect.TypeOf(config), node)
	b, _ := json.Marshal(node)
	formDesc[name] = struct {
		desc   string
		typeOf reflect.Type
	}{
		desc:   string(b),
		typeOf: reflect.TypeOf(config),
	}
}

var enumFormDescNodeType = utils.NewEnum[pb.EnumFormDescNodeType]()

type formDescNode struct {
	Field  string
	Type   string
	Name   string
	Desc   string
	Childs []*formDescNode
}

func parseToFormDesc(typeOf reflect.Type, node *formDescNode) {
	for typeOf.Kind() == reflect.Pointer {
		typeOf = typeOf.Elem()
	}
	switch typeOf.Kind() {
	case reflect.Float32, reflect.Float64:
		node.Type = enumFormDescNodeType.Float
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		node.Type = enumFormDescNodeType.Integer
	case reflect.Bool:
		node.Type = enumFormDescNodeType.Boolean
	case reflect.String:
		node.Type = enumFormDescNodeType.Chars
	case reflect.Map:
		node.Type = enumFormDescNodeType.Map
		keyNode := &formDescNode{}
		parseToFormDesc(typeOf.Key(), keyNode)
		valueNode := &formDescNode{}
		parseToFormDesc(typeOf.Elem(), valueNode)
		keyNode.Childs = append(keyNode.Childs, valueNode)
		node.Childs = append(node.Childs, keyNode)
	case reflect.Slice:
		node.Type = enumFormDescNodeType.Array
		newNode := &formDescNode{}
		parseToFormDesc(typeOf.Elem(), newNode)
		node.Childs = append(node.Childs, newNode)
	case reflect.Struct:
		node.Type = enumFormDescNodeType.Struct
		for i := 0; i < typeOf.NumField(); i++ {
			field := typeOf.Field(i)
			name := field.Tag.Get("name")
			desc := field.Tag.Get("desc")
			if len(name) == 0 {
				name = field.Name
			}
			newNode := &formDescNode{
				Field: field.Name,
				Name:  name,
				Desc:  desc,
			}
			parseToFormDesc(field.Type, newNode)
			node.Childs = append(node.Childs, newNode)
		}
	}
}
