package account

import (
	"s2/modules/gm/domain/adminops"
	"slices"
	"strings"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/infra/mdb"
)

func (uc *useCase) Auth(token string, permission string) (bool, error) {
	internalKey := conf.Str("gm.internalKey")
	if token == internalKey {
		return true, nil
	}
	value, ok := uc.tokenCache.Load(token)
	if !ok {
		return false, nil
	}
	username := value.(string)
	var data admin
	err := mdb.Default().Table(TableName).First(&data, "username = ?", username).Error
	if err != nil {
		return false, err
	}
	permissions := strings.Split(data.Permission, ",")
	if slices.Contains(permissions, adminops.EnumPermission.ADMIN) {
		return true, nil
	}
	return slices.Contains(permissions, string(permission)), nil
}
