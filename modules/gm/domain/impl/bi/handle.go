package bi

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"net/http"
	"s2/pb"
	"slices"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/infra/chdb"
	"github.com/jfcwrlight/core/infra/mgdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils/hs"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func (uc *useCase) onBIQuerySaveReq(ctx *gin.Context, body *pb.BIQuerySaveReq) {
	_, err := mgdb.Default().Collection(collection).ReplaceOne(ctx, bson.M{"_id": body.SQL.Key}, body.SQL, options.Replace().SetUpsert(true))
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.BIQuerySaveResp{
		Code: pb.SUCCESS,
	})
}

func (uc *useCase) onBIQueryListReq(ctx *gin.Context, body *pb.BIQueryListReq) {
	cursor, err := mgdb.Default().Collection(collection).Find(ctx, bson.M{})
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	var result []*pb.BISaveSQL
	err = cursor.All(ctx, &result)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.BIQueryListResp{
		Code: pb.SUCCESS,
		List: result,
	})
}

func (uc *useCase) onBIQueryReq(ctx *gin.Context, body *pb.BIQueryReq) {
	query := &pb.BISaveSQL{}
	err := mgdb.Default().Collection(collection).FindOne(ctx, bson.M{"_id": body.Key}).Decode(query)
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		log.Error(err)
		return
	}
	for k, v := range body.Args {
		query.SQL = strings.ReplaceAll(query.SQL, "@"+k, v)
	}
	var result []map[string]any
	err = chdb.Default().Raw(query.SQL).Scan(&result).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
	}
	var b []byte
	if body.UseCSV {
		b = toCSV(result, query.OrderBy, query.Chinese)
	} else {
		b, _ = json.Marshal(result)
	}
	var out bytes.Buffer
	gz := gzip.NewWriter(&out)
	gz.Write(b)
	err = gz.Close()
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	b = out.Bytes()
	ctx.Data(http.StatusOK, "application/octet-stream", b)
}

func toCSV(result []map[string]any, orderBy map[string]int32, chinese map[string]string) []byte {
	data := make([]string, 0, len(result)+1)
	item := result[0]
	keys := make([]string, 0, len(item))
	for k := range item {
		keys = append(keys, k)
	}
	if orderBy == nil {
		orderBy = map[string]int32{}
	}
	if chinese == nil {
		chinese = map[string]string{}
	}
	slices.SortFunc(keys, func(a, b string) int {
		return int(orderBy[a] - orderBy[b])
	})
	columns := make([]string, 0, len(keys))
	for _, key := range keys {
		if v, ok := chinese[key]; ok {
			columns = append(columns, v)
		} else {
			columns = append(columns, key)
		}
	}
	data = append(data, strings.Join(columns, ","))
	for _, r := range result {
		values := make([]string, 0, len(keys))
		for _, k := range keys {
			values = append(values, fmt.Sprint(r[k]))
		}
		data = append(data, strings.Join(values, ","))
	}
	text := strings.Join(data, "\n")
	return []byte(text)
}

func (uc *useCase) onBIExecSQLReq(ctx *gin.Context, body *pb.BIExecSQLReq) {
	var result []map[string]any
	err := chdb.Default().Raw(body.SQL).Scan(&result).Error
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
	}
	var b []byte
	if body.UseCSV {
		b = toCSV(result, nil, nil)
	} else {
		b, _ = json.Marshal(result)
	}
	var out bytes.Buffer
	gz := gzip.NewWriter(&out)
	gz.Write(b)
	err = gz.Close()
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	hs.OK(ctx, pb.BIQueryResp{
		Code: pb.SUCCESS,
		Data: out.Bytes(),
	})
}

func (uc *useCase) onBIQueryDeleteReq(ctx *gin.Context, body *pb.BIQueryDeleteReq) {
	_, err := mgdb.Default().Collection(collection).DeleteOne(ctx, bson.M{"_id": body.Key})
	if err != nil {
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		log.Error(err)
		return
	}
	hs.OK(ctx, pb.BIQueryDeleteResp{
		Code: pb.SUCCESS,
	})
}
