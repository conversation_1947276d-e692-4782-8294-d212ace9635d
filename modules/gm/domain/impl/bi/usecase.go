package bi

import (
	"s2/modules/gm/domain"
	"s2/modules/gm/domain/adminops"
	"s2/modules/gm/domain/api"

	"github.com/jfcwrlight/core/basic/domainops"
)

var uc *useCase

const collection = "bisave"

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IBI](d, domain.BIIndex, uc)
	adminops.Handle(adminops.EnumPermission.BIEDIT, uc.onBIQuerySaveReq)
	adminops.Handle(adminops.EnumPermission.BIEDIT, uc.onBIQueryDeleteReq)
	adminops.Handle(adminops.EnumPermission.BIQUERY, uc.onBIQueryReq)
	adminops.Handle(adminops.EnumPermission.BIQUERY, uc.onBIExecSQLReq)
	adminops.Handle(adminops.EnumPermission.BIQUERY, uc.onBIQueryListReq)
}
