package adminops

import (
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"s2/pb"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils/hs"
)

func handle(ctx *gin.Context) {
	b, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.SERVER_ERROR})
		return
	}
	var req = struct {
		Cmd  string
		Data string
	}{}
	err = json.Unmarshal(b, &req)
	if err != nil {
		log.Error(err)
		hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "json unmarshal"})
		return
	}
	since := time.Now()
	permission, ok := msgPermission[req.Cmd]
	if !ok {
		ctx.JSON(http.StatusOK, pb.Error{Code: pb.PARAM_ERROR})
		return
	}
	if permission != EnumPermission.PUBLIC {
		token := ctx.Request.Header.Get("Token")
		ok, err := auth(token, permission)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, pb.Error{Code: pb.SERVER_ERROR})
			return
		}
		if !ok {
			ctx.JSON(http.StatusOK, pb.Error{Code: pb.TOKEN_ERROR})
			return
		}
	}
	latency := time.Since(since)
	if latency > time.Millisecond*50 {
		log.Warnf("latency too high: %s, req: %#v", latency, req)
	}
	body := reflect.New(nameToType[req.Cmd]).Interface()
	if len(req.Data) > 0 {
		err = json.Unmarshal([]byte(req.Data), body)
		if err != nil {
			log.Error(err)
			hs.Err(ctx, pb.Error{Code: pb.PARAM_ERROR, Msg: "json unmarshal error"})
			return
		}
	}
	msgHandle[req.Cmd](ctx, body)
}
