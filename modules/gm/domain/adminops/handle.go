package adminops

import (
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
)

var (
	msgHandle     = map[string]func(ctx *gin.Context, msg any){}
	msgPermission = map[string]string{}
	nameToType    = map[string]reflect.Type{}
)

func Handle[T any](permission string, h func(ctx *gin.Context, req *T)) {
	typeName := reflect.TypeFor[T]().Name()
	if _, ok := msgHandle[typeName]; ok {
		panic("msg handler already exists")
	}
	msgPermission[typeName] = permission
	nameToType[typeName] = reflect.TypeFor[T]()
	// 注册消息处理函数
	msgHandle[typeName] = func(ctx *gin.Context, msg any) {
		since := time.Now()
		h(ctx, msg.(*T))
		log.Infof("Handle %s, %s", utils.FormatMsg(msg), time.Since(since))
	}
}
