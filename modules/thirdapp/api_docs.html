<!doctype html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <title data-i18n="title">Datax API Integration Documentation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 2em auto;
      line-height: 1.6;
    }
    h1,
    h2,
    h3 {
      color: #333;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 1em;
    }
    th,
    td {
      border: 1px solid #ccc;
      padding: 0.5em;
      text-align: left;
    }
    th {
      background: #f0f0f0;
    }
    pre {
      background: #f9f9f9;
      padding: 1em;
      overflow-x: auto;
      white-space: pre;
    }
    pre code {
      white-space: pre;
      display: block;
      font-family: 'Courier New', monospace;
    }
    code {
      font-family: monospace;
      background: #eee;
      padding: 0.2em 0.4em;
    }
    .lang-toggle {
      text-align: right;
      margin: 1em;
    }
    .lang-toggle button {
      margin-left: 0.5em;
      padding: 0.3em 0.8em;
      border: 1px solid #ccc;
      background: #f5f5f5;
      cursor: pointer;
    }
    .lang-toggle button:hover {
      background: #e5e5e5;
    }
    .lang-toggle button.active {
      background: #007bff;
      color: white;
      border-color: #007bff;
    }
  </style>
</head>

<body>
  <div class="lang-toggle">
    <button id="btnZh" class="active">中文</button>
    <button id="btnEn">English</button>
  </div>

  <div id="content">
    <h1 data-i18n="main_title">Datax API 集成指南</h1>
    <p data-i18n="intro_desc">
      所有请求必须携带 <code>appid</code> 请求头，并使用加密后的请求体。响应以 JSON 返回。
    </p>

    <h2 data-i18n="credentials_title">平台凭证</h2>
    <ul>
      <li>
        <strong data-i18n="appid_label">appid：</strong><span data-i18n="appid_desc">平台分配的应用 ID。必须在每次请求的 HTTP 请求头中设置
          <code>appid</code>。</span>
      </li>
      <li><strong data-i18n="key_label">Key：</strong><span data-i18n="key_desc">平台分配的 16 字节 AES 加密密钥。</span></li>
      <li><strong data-i18n="iv_label">IV：</strong><span data-i18n="iv_desc">平台分配的 16 字节 AES 初始向量。</span></li>
    </ul>
    
    <h2 data-i18n="encryption_title">加密</h2>
    <p><strong data-i18n="important_note">重要：</strong><span data-i18n="appid_requirement">每次请求务必包含 <code>appid</code>
        请求头。</span></p>
    <p>
      <strong data-i18n="encryption_process_title">加密流程：</strong><span data-i18n="encryption_process_desc">JSON 数据 → PKCS7
        填充 → 使用 Key 与 IV 进行 AES-CBC 加密 → 对加密字节进行 Base64 编码 → 在请求体中发送 Base64 编码后的密文字符串。</span>
    </p>
    <pre
      data-i18n="encryption_details">加密算法：AES-CBC\n密钥：gNCD3fxsGi2OZSRw\nIV：BguU4a7ftj0nuPzj\n填充：PKCS7（补齐到 16 字节块）\n请求 Content-Type：application/octet-stream\n请求头：appid: YOUR_APP_ID\n请求体：Base64 编码后的密文字符串</pre>
    
    <h2 data-i18n="api_endpoints_title">API 接口</h2>
    <h3 data-i18n="register_player_title">1. 注册玩家</h3>
    <p><strong data-i18n="endpoint_label">接口地址：</strong><code>POST /registerPlayer</code></p>
    <p><strong data-i18n="request_json_label">请求 JSON：</strong></p>
    <pre>{
  "Account": "string",
  "Now": **********
}</pre>
    <ul>
      <li><code>Account</code>: <span data-i18n="account_param_desc">账号唯一ID。</span></li>
      <li><code>Now</code>: <span data-i18n="now_param_desc">当前时间戳（秒）。</span></li>
    </ul>
    <p><strong data-i18n="response_json_label">响应 JSON：</strong></p>
    <pre>{
  "Code": 0,
  "UserID": 12345
}</pre>
    <ul>
      <li><code>Code</code>: <span data-i18n="error_code_desc">错误码（ThirdAppErrCode）。</span></li>
      <li><code>UserID</code>: <span data-i18n="user_id_desc">平台用户唯一ID。</span></li>
    </ul>

    <h3 data-i18n="transfer_in_title">2. 转入</h3>
    <p><strong data-i18n="endpoint_label">接口地址：</strong><code>POST /transferIn</code></p>
    <p><strong data-i18n="request_json_label">请求 JSON：</strong></p>
    <pre>{
  "Account": "string",
  "Amount": 100.0,
  "AssetID": 10000,
  "Now": **********
}</pre>
    <ul>
      <li><code>Account</code>: <span data-i18n="account_param_desc">账号唯一ID。</span></li>
      <li><code>Amount</code>: <span data-i18n="amount_in_desc">上分金额。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
      <li><code>Now</code>: <span data-i18n="now_param_desc">当前时间戳（秒）。</span></li>
    </ul>
    <p><strong data-i18n="response_json_label">响应 JSON：</strong></p>
    <pre>{
  "Code": 0,
  "Amount": 100.0,
  "Balance": 1000.0,
  "AssetID": 10000
}</pre>
    <ul>
      <li><code>Code</code>: <span data-i18n="error_code_desc">错误码。</span></li>
      <li><code>Amount</code>: <span data-i18n="amount_in_desc">上分金额。</span></li>
      <li><code>Balance</code>: <span data-i18n="balance_desc">账户余额。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
    </ul>

    <h3 data-i18n="play_game_title">3. 进入游戏</h3>
    <p><strong data-i18n="endpoint_label">接口地址：</strong><code>POST /play</code></p>
    <p><strong data-i18n="request_json_label">请求 JSON：</strong></p>
    <pre>{
                              "Account": "string",
                              "GameID": 1001,
                              "AssetID": 10000,
                              "Language": "zh",
                              "Now": **********
                            }</pre>
    <ul>
      <li><code>Account</code>: <span data-i18n="account_param_desc">账号唯一ID。</span></li>
      <li><code>GameID</code>: <span data-i18n="game_id_desc">游戏ID。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
      <li><code>Language</code>: <span data-i18n="language_desc">语言代码（如：zh, en）。</span></li>
      <li><code>Now</code>: <span data-i18n="now_param_desc">当前时间戳（秒）。</span></li>
    </ul>
    <p><strong data-i18n="response_json_label">响应 JSON：</strong></p>
    <pre>{
                              "Code": 0,
                              "Url": "https://game.example.com/play?token=abc123"
                            }</pre>
    <ul>
      <li><code>Code</code>: <span data-i18n="error_code_desc">错误码。</span></li>
      <li><code>Url</code>: <span data-i18n="game_url_desc">游戏链接地址。</span></li>
    </ul>
    
    <h3 data-i18n="transfer_out_title">4. 转出</h3>
    <p><strong data-i18n="endpoint_label">接口地址：</strong><code>POST /transferOut</code></p>
    <p><strong data-i18n="request_json_label">请求 JSON：</strong></p>
    <pre>{
  "Account": "string",
  "AssetID": 10000,
  "Now": **********
}</pre>
    <ul>
      <li><code>Account</code>: <span data-i18n="account_param_desc">账号唯一ID。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
      <li><code>Now</code>: <span data-i18n="now_param_desc">当前时间戳（秒）。</span></li>
    </ul>
    <p><strong data-i18n="response_json_label">响应 JSON：</strong></p>
    <pre>{
  "Code": 0,
  "Amount": 100.0,
  "AssetID": 10000
}</pre>
    <ul>
      <li><code>Code</code>: <span data-i18n="error_code_desc">错误码。</span></li>
      <li><code>Amount</code>: <span data-i18n="amount_out_desc">下分金额。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
    </ul>

    <h3 data-i18n="balance_title">5. 查询余额</h3>
    <p><strong data-i18n="endpoint_label">接口地址：</strong><code>POST /balance</code></p>
    <p><strong data-i18n="request_json_label">请求 JSON：</strong></p>
    <pre>{
  "Account": "string",
  "AssetID": 10000,
  "Now": **********
}</pre>
    <ul>
      <li><code>Account</code>: <span data-i18n="account_param_desc">账号唯一ID。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
      <li><code>Now</code>: <span data-i18n="now_param_desc">当前时间戳（秒）。</span></li>
    </ul>
    <p><strong data-i18n="response_json_label">响应 JSON：</strong></p>
    <pre>{
  "Code": 0,
  "Balance": 900.0,
  "AssetID": 10000
}</pre>
    <ul>
      <li><code>Code</code>: <span data-i18n="error_code_desc">错误码。</span></li>
      <li><code>Balance</code>: <span data-i18n="balance_desc">账户余额。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
    </ul>

    <h3 data-i18n="record_title">6. 获取记录</h3>
    <p><strong data-i18n="endpoint_label">接口地址：</strong><code>POST /record</code></p>
    <p><strong data-i18n="request_json_label">请求 JSON：</strong></p>
    <pre>{
  "StartTime": **********,
  "EndTime": **********,
  "Offset": 1,
  "Limit": 20,
  "Now": **********
}</pre>
    <ul>
      <li><code>StartTime</code>: <span data-i18n="start_time_desc">开始时间（UTC Unix 时间戳）。</span></li>
      <li><code>EndTime</code>: <span data-i18n="end_time_desc">结束时间（UTC Unix 时间戳）。</span></li>
      <li><code>Offset</code>: <span data-i18n="offset_desc">分页偏移量。</span></li>
      <li><code>Limit</code>: <span data-i18n="limit_desc">分页大小。</span></li>
      <li><code>Now</code>: <span data-i18n="now_param_desc">当前时间戳（秒）。</span></li>
    </ul>
    <p><strong data-i18n="response_json_label">响应 JSON：</strong></p>
    <pre>{
  "Code": 0,
  "Rows": [
    {
      "EventID": "evt_123456",
      "UserID": 123,
      "Input": 100.0,
      "Output": 200.0,
      "AssetID": 10000,
      "GameID": 1001,
      "AccountName": "app123_user456",
      "CreateAt": **********
    }
  ]
}</pre>
    <ul>
      <li><code>Code</code>: <span data-i18n="error_code_desc">错误码。</span></li>
      <li><code>Rows</code>: <span data-i18n="rows_desc">流水列表，每项为 RecordRow。</span></li>
      <li><code>EventID</code>: <span data-i18n="event_id_desc">记录ID。</span></li>
      <li><code>UserID</code>: <span data-i18n="user_id_desc">平台用户唯一ID。</span></li>
      <li><code>Input</code>: <span data-i18n="input_desc">投入金额。</span></li>
      <li><code>Output</code>: <span data-i18n="output_desc">产出金额。</span></li>
      <li><code>AssetID</code>: <span data-i18n="asset_id_desc">货币ID。</span></li>
      <li><code>GameID</code>: <span data-i18n="game_id_desc">游戏ID。</span></li>
      <li><code>AccountName</code>: <span data-i18n="account_name_desc">用户名（appid+"_"+渠道用户的唯一id）。</span></li>
      <li><code>CreateAt</code>: <span data-i18n="create_at_desc">创建时间（UTC Unix 时间戳）。</span></li>
    </ul>
    
    <h2 data-i18n="example_code_title">示例代码（Go）</h2>
    <!-- 
    // encrypt applies AES-CBC encryption with PKCS7 padding and returns a Base64-encoded ciphertext.
    // encrypt 应用 AES-CBC 和 PKCS7 填充，对密文进行 Base64 编码并返回字符串
    func encrypt(src []byte) string {
        block, err := aes.NewCipher([]byte(key))
        if err != nil {
            panic(err)
        }
        bs := block.BlockSize()
        padding := bs - len(src)%bs
        padtext := bytes.Repeat([]byte{byte(padding)}, padding)
        src = append(src, padtext...)
        dst := make([]byte, len(src))
        mode := cipher.NewCBCEncrypter(block, []byte(iv))
        mode.CryptBlocks(dst, src)
        return base64.StdEncoding.EncodeToString(dst)
    }

    // doRequest sends Base64-encoded ciphertext of JSON payload as request body and returns response body and status code
    // doRequest 发送 Base64 编码后的密文作为请求体，并返回响应内容与状态码
    func doRequest(t *testing.T, endpoint string, payload interface{}) ([]byte, int) {
        data, err := json.Marshal(payload)
        if err != nil {
            t.Fatalf("marshal payload error: %v", err)
        }
        enc := encrypt(data)
        req, err := http.NewRequest("POST", baseURL+endpoint, bytes.NewReader([]byte(enc)))
        if err != nil {
            t.Fatalf("new request error: %v", err)
        }
        req.Header.Set("appid", appID)
        req.Header.Set("Content-Type", "application/octet-stream")
        resp, err := http.DefaultClient.Do(req)
        if err != nil {
            t.Fatalf("request error: %v", err)
        }
        defer resp.Body.Close()
        body, err := ioutil.ReadAll(resp.Body)
        if err != nil {
            t.Fatalf("read body error: %v", err)
        }
        return body, resp.StatusCode
    }
    
    func TestRegisterPlayer(t *testing.T) {
        body, status := doRequest(t, "/registerPlayer", map[string]interface{}{"Account": "1"})
        if status != http.StatusOK {
            t.Fatalf("expected status 200, got %d", status)
        }
        var resp map[string]interface{}
        if err := json.Unmarshal(body, &resp); err != nil {
            t.Fatalf("unmarshal error: %v", err)
        }
        if resp["Code"] != 0 {
            t.Errorf("expected Code THIRDAPP_SUCCESS, got %v", resp["Code"])
        }
        t.Logf("RegisterPlayer userID=%v", resp["UserID"])
    }
    -->
    <pre><code id="go-code" class="language-go"></code></pre>
    <h2 data-i18n="error_codes_title">错误码</h2>
    <table>
      <thead>
        <tr>
          <th data-i18n="code_header">代码</th>
          <th data-i18n="name_header">名称</th>
          <th data-i18n="description_header">描述</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>0</td>
          <td>THIRDAPP_SUCCESS</td>
          <td data-i18n="success_desc">成功</td>
        </tr>
        <tr>
          <td>1</td>
          <td>THIRDAPP_FAILED</td>
          <td data-i18n="failed_desc">失败</td>
        </tr>
        <tr>
          <td>2</td>
          <td>THIRDAPP_PARAM_ERROR</td>
          <td data-i18n="param_error_desc">参数错误</td>
        </tr>
        <tr>
          <td>3</td>
          <td>THIRDAPP_ACCOUNT_ALREADY_EXISTS</td>
          <td data-i18n="account_exists_desc">账户已存在</td>
        </tr>
        <tr>
          <td>4</td>
          <td>THIRDAPP_ACCOUNT_NOT_FOUND</td>
          <td data-i18n="account_not_found_desc">账户未找到</td>
        </tr>
        <tr>
          <td>5</td>
          <td>THIRDAPP_BALANCE_NOT_ENOUGH</td>
          <td data-i18n="balance_not_enough_desc">余额不足</td>
        </tr>
        <tr>
          <td>6</td>
          <td>THIRDAPP_CURRENCY_LOCKED</td>
          <td data-i18n="currency_locked_desc">币种锁定</td>
        </tr>
      </tbody>
    </table>
    
    <h2 data-i18n="asset_ids_title">资产 ID（EnumAsset）</h2>
    <table>
      <thead>
        <tr>
          <th data-i18n="id_header">ID</th>
          <th data-i18n="name_header">名称</th>
          <th data-i18n="description_header">描述</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>0</td>
          <td>ASSET_NONE</td>
          <td data-i18n="asset_none_desc">无</td>
        </tr>
        <tr>
          <td>10000</td>
          <td>ASSET_GOLD</td>
          <td data-i18n="asset_gold_desc">金币</td>
        </tr>
        <tr>
          <td>10001</td>
          <td>ASSET_FREE</td>
          <td data-i18n="asset_free_desc">免费币</td>
        </tr>
        <tr>
          <td>10002</td>
          <td>ASSET_USD</td>
          <td data-i18n="asset_usd_desc">美元</td>
        </tr>
        <tr>
          <td>10003</td>
          <td>ASSET_CNY</td>
          <td data-i18n="asset_cny_desc">人民币</td>
        </tr>
        <tr>
          <td>10004</td>
          <td>ASSET_KARAT</td>
          <td data-i18n="asset_karat_desc">Karat</td>
        </tr>
        <tr>
          <td>10005</td>
          <td>ASSET_CAKE</td>
          <td data-i18n="asset_cake_desc">Cake</td>
        </tr>
        <tr>
          <td>10006</td>
          <td>ASSET_CAKEGOLD</td>
          <td data-i18n="asset_cakegold_desc">CakeGold</td>
        </tr>
      </tbody>
    </table>
    
    <h2 data-i18n="language_ids_title">语言 ID（EnumLanguage）</h2>
    <table>
      <thead>
        <tr>
          <th data-i18n="id_header">ID</th>
          <th data-i18n="name_header">名称</th>
          <th data-i18n="description_header">描述</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>0</td>
          <td>LANGUAGE_NONE</td>
          <td data-i18n="language_none_desc">未指定语言</td>
        </tr>
        <tr>
          <td>1</td>
          <td>EN</td>
          <td data-i18n="language_en_desc">英语</td>
        </tr>
        <tr>
          <td>2</td>
          <td>ZH</td>
          <td data-i18n="language_zh_desc">中文</td>
        </tr>
        <tr>
          <td>3</td>
          <td>RU</td>
          <td data-i18n="language_ru_desc">俄语</td>
        </tr>
        <tr>
          <td>4</td>
          <td>TR</td>
          <td data-i18n="language_tr_desc">土耳其语</td>
        </tr>
        <tr>
          <td>5</td>
          <td>BY</td>
          <td data-i18n="language_by_desc">白俄罗斯语</td>
        </tr>
        <tr>
          <td>6</td>
          <td>NL</td>
          <td data-i18n="language_nl_desc">荷兰语</td>
        </tr>
        <tr>
          <td>7</td>
          <td>UA</td>
          <td data-i18n="language_ua_desc">乌克兰语</td>
        </tr>
        <tr>
          <td>8</td>
          <td>EE</td>
          <td data-i18n="language_ee_desc">爱沙尼亚语</td>
        </tr>
        <tr>
          <td>9</td>
          <td>IT</td>
          <td data-i18n="language_it_desc">意大利语</td>
        </tr>
        <tr>
          <td>10</td>
          <td>RO</td>
          <td data-i18n="language_ro_desc">罗马尼亚语</td>
        </tr>
        <tr>
          <td>11</td>
          <td>HR</td>
          <td data-i18n="language_hr_desc">克罗地亚语</td>
        </tr>
        <tr>
          <td>12</td>
          <td>DE</td>
          <td data-i18n="language_de_desc">德语</td>
        </tr>
        <tr>
          <td>13</td>
          <td>FR</td>
          <td data-i18n="language_fr_desc">法语</td>
        </tr>
        <tr>
          <td>14</td>
          <td>PT</td>
          <td data-i18n="language_pt_desc">葡萄牙语</td>
        </tr>
        <tr>
          <td>15</td>
          <td>LT</td>
          <td data-i18n="language_lt_desc">立陶宛语</td>
        </tr>
        <tr>
          <td>16</td>
          <td>ES</td>
          <td data-i18n="language_es_desc">西班牙语</td>
        </tr>
        <tr>
          <td>17</td>
          <td>PL</td>
          <td data-i18n="language_pl_desc">波兰语</td>
        </tr>
      </tbody>
    </table>
    </div>
    
    <script>
      // 多语言文本数据
      const i18nData = {
        zh: {
          title: "Datax API Integration Documentation",
          main_title: "Datax API 集成指南",
          intro_desc: "所有请求必须携带 <code>appid</code> 请求头，并使用加密后的请求体。响应以 JSON 返回。",
          credentials_title: "平台凭证",
          appid_label: "appid：",
          appid_desc: "平台分配的应用 ID。必须在每次请求的 HTTP 请求头中设置 <code>appid</code>。",
          key_label: "Key：",
          key_desc: "平台分配的 16 字节 AES 加密密钥。",
          iv_label: "IV：",
          iv_desc: "平台分配的 16 字节 AES 初始向量。",
          encryption_title: "加密",
          important_note: "重要：",
          appid_requirement: "每次请求务必包含 <code>appid</code> 请求头。",
          encryption_process_title: "加密流程：",
          encryption_process_desc: "JSON 数据 → PKCS7 填充 → 使用 Key 与 IV 进行 AES-CBC 加密 → 对加密字节进行 Base64 编码 → 在请求体中发送 Base64 编码后的密文字符串。",
          encryption_details: `加密算法：AES-CBC\n密钥：gNCD3fxsGi2OZSRw\nIV：BguU4a7ftj0nuPzj\n填充：PKCS7（补齐到 16 字节块）\n请求 Content-Type：application/octet-stream\n请求头：appid: YOUR_APP_ID\n请求体：Base64 编码后的密文字符串`,
          error_codes_title: "错误码",
          code_header: "代码",
          name_header: "名称",
          description_header: "描述",
          success_desc: "成功",
          failed_desc: "失败",
          param_error_desc: "参数错误",
          account_exists_desc: "账户已存在",
          account_not_found_desc: "账户未找到",
          balance_not_enough_desc: "余额不足",
          currency_locked_desc: "币种锁定",
          asset_ids_title: "资产 ID（EnumAsset）",
          id_header: "ID",
          asset_none_desc: "无",
          asset_gold_desc: "金币",
          asset_free_desc: "免费币",
          asset_usd_desc: "美元",
          asset_cny_desc: "人民币",
          asset_karat_desc: "Karat",
          asset_cake_desc: "Cake",
          asset_cakegold_desc: "CakeGold",
          language_ids_title: "语言 ID（EnumLanguage）",
          language_none_desc: "未指定语言",
          language_en_desc: "英语",
          language_zh_desc: "中文",
          language_ru_desc: "俄语",
          language_tr_desc: "土耳其语",
          language_by_desc: "白俄罗斯语",
          language_nl_desc: "荷兰语",
          language_ua_desc: "乌克兰语",
          language_ee_desc: "爱沙尼亚语",
          language_it_desc: "意大利语",
          language_ro_desc: "罗马尼亚语",
          language_hr_desc: "克罗地亚语",
          language_de_desc: "德语",
          language_fr_desc: "法语",
          language_pt_desc: "葡萄牙语",
          language_lt_desc: "立陶宛语",
          language_es_desc: "西班牙语",
          language_pl_desc: "波兰语",
          api_endpoints_title: "API 接口",
          register_player_title: "1. 注册玩家",
          endpoint_label: "接口地址：",
          request_json_label: "请求 JSON：",
          response_json_label: "响应 JSON：",
          account_param_desc: "账号唯一ID。",
          error_code_desc: "错误码（ThirdAppErrCode）。",
          user_id_desc: "平台用户唯一ID。",
          transfer_in_title: "2. 转入",
          amount_in_desc: "上分金额。",
          asset_id_desc: "货币ID。",
          balance_desc: "账户余额。",
          play_game_title: "3. 进入游戏",
          game_id_desc: "游戏ID。",
          language_desc: "语言代码（如：zh, en）。",
          game_url_desc: "游戏链接地址。",
          transfer_out_title: "4. 转出",
          amount_out_desc: "下分金额。",
          balance_title: "5. 查询余额",
          record_title: "6. 获取记录",
          start_time_desc: "开始时间（UTC Unix 时间戳）。",
          end_time_desc: "结束时间（UTC Unix 时间戳）。",
          offset_desc: "分页偏移量。",
          limit_desc: "分页大小。",
          rows_desc: "流水列表，每项为 RecordRow。",
          now_param_desc: "当前时间戳（秒）。",
          event_id_desc: "记录ID。",
          input_desc: "投入金额。",
          output_desc: "产出金额。",
          account_name_desc: "用户名（appid+\"_\"+渠道用户的唯一id）。",
          create_at_desc: "创建时间（UTC Unix 时间戳）。",
          example_code_title: "示例代码（Go）"
        },
        en: {
          title: "Datax API Integration Documentation",
          main_title: "Datax API Integration Guide",
          intro_desc: "All requests must include the <code>appid</code> header and an encrypted body. Responses are returned as JSON.",
          credentials_title: "Platform Credentials",
          appid_label: "appid:",
          appid_desc: "Platform-assigned application ID. Must be set as the HTTP header <code>appid</code> on every request.",
          key_label: "Key:",
          key_desc: "16-byte AES encryption key assigned by the platform.",
          iv_label: "IV:",
          iv_desc: "16-byte AES initialization vector assigned by the platform.",
          encryption_title: "Encryption",
          important_note: "Important:",
          appid_requirement: "Always include the <code>appid</code> header in every request.",
          encryption_process_title: "Encryption Process:",
          encryption_process_desc: "JSON payload → PKCS7 padding → AES-CBC encrypt with Key & IV → Base64 encode ciphertext and send Base64 string in request body.",
          encryption_details: `Algorithm: AES-CBC\nKey: gNCD3fxsGi2OZSRw\nIV:  BguU4a7ftj0nuPzj\nPadding: PKCS7 (pad to 16-byte blocks)\nRequest Content-Type: application/octet-stream\nHeader: appid: YOUR_APP_ID\nBody: Base64 encoded ciphertext of JSON payload`,
          error_codes_title: "Error Codes",
          code_header: "Code",
          name_header: "Name",
          description_header: "Description",
          success_desc: "Success",
          failed_desc: "Failed",
          param_error_desc: "Parameter error",
          account_exists_desc: "Account already exists",
          account_not_found_desc: "Account not found",
          balance_not_enough_desc: "Balance not enough",
          currency_locked_desc: "Currency locked",
          asset_ids_title: "Asset IDs (EnumAsset)",
          id_header: "ID",
          asset_none_desc: "None",
          asset_gold_desc: "Gold",
          asset_free_desc: "Free Coins",
          asset_usd_desc: "USD",
          asset_cny_desc: "CNY",
          asset_karat_desc: "Karat",
          asset_cake_desc: "Cake",
          asset_cakegold_desc: "CakeGold",
          language_ids_title: "Language IDs (EnumLanguage)",
          language_none_desc: "No language specified",
          language_en_desc: "English",
          language_zh_desc: "Chinese",
          language_ru_desc: "Russian",
          language_tr_desc: "Turkish",
          language_by_desc: "Belarusian",
          language_nl_desc: "Dutch",
          language_ua_desc: "Ukrainian",
          language_ee_desc: "Estonian",
          language_it_desc: "Italian",
          language_ro_desc: "Romanian",
          language_hr_desc: "Croatian",
          language_de_desc: "German",
          language_fr_desc: "French",
          language_pt_desc: "Portuguese",
          language_lt_desc: "Lithuanian",
          language_es_desc: "Spanish",
          language_pl_desc: "Polish",
          api_endpoints_title: "API Endpoints",
          register_player_title: "1. Register Player",
          endpoint_label: "URL:",
          request_json_label: "Request JSON:",
          response_json_label: "Response JSON:",
          account_param_desc: "Unique account ID.",
          error_code_desc: "Error code (ThirdAppErrCode).",
          user_id_desc: "Unique user ID.",
          transfer_in_title: "2. Transfer In",
          amount_in_desc: "Amount to deposit.",
          asset_id_desc: "Currency ID.",
          balance_desc: "Account balance.",
          play_game_title: "3. Enter Game",
          game_id_desc: "Game ID.",
          language_desc: "Language code (e.g., zh, en).",
          game_url_desc: "Game URL address.",
          transfer_out_title: "4. Transfer Out",
          amount_out_desc: "Withdrawal amount.",
          balance_title: "5. Balance",
          record_title: "6. Record",
          start_time_desc: "Start time (UTC Unix timestamp).",
          end_time_desc: "End time (UTC Unix timestamp).",
          offset_desc: "Pagination offset.",
          limit_desc: "Pagination size.",
          rows_desc: "Transaction history, each item is a RecordRow.",
          now_param_desc: "Current timestamp (seconds).",
          event_id_desc: "Record ID.",
          input_desc: "Input amount.",
          output_desc: "Output amount.",
          account_name_desc: "Account name (appid+\"_\"+channel user unique id).",
          create_at_desc: "Create time (UTC Unix timestamp).",
          example_code_title: "Go Client Example"
        }
      };

      // 当前语言
      let currentLang = 'zh';

      // 更新文本内容
      function updateText() {
        const texts = i18nData[currentLang];
        document.querySelectorAll('[data-i18n]').forEach(element => {
          const key = element.getAttribute('data-i18n');
          if (texts[key]) {
            element.innerHTML = texts[key];
          }
        });
      }

      // 设置语言
      function setLang(lang) {
        currentLang = lang;
        updateText();

        // 更新按钮状态
        document.querySelectorAll('.lang-toggle button').forEach(btn => {
          btn.classList.remove('active');
        });
        document.getElementById(lang === 'zh' ? 'btnZh' : 'btnEn').classList.add('active');
      }

      // 绑定事件
      document.getElementById('btnZh').onclick = () => setLang('zh');
      document.getElementById('btnEn').onclick = () => setLang('en');

      // 设置Go代码内容
      function setGoCode() {
        // 从HTML注释中提取代码内容
        const htmlSource = document.documentElement.innerHTML;
        const commentStart = htmlSource.indexOf('<!--') + 4;
        const commentEnd = htmlSource.indexOf('-->');
        const commentContent = htmlSource.substring(commentStart, commentEnd);

        // 清理注释内容，移除多余的空白行和缩进
        const goCode = commentContent
          .split('\n')
          .filter(line => line.trim().length > 0)
          .map(line => line.replace(/^\s{4}/, ''))
          .join('\n');

        document.getElementById('go-code').textContent = goCode;
      }

                            // 初始化
                            const params = new URLSearchParams(window.location.search);
                            const initLang = params.get('lang') === 'en' ? 'en' : 'zh';
                            setLang(initLang);
                            setGoCode();
                          </script>
</body>
</html>
