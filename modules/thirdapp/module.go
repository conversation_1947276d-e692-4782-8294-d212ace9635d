package thirdapp

import (
	"errors"
	"fmt"
	"s2/define"
	"s2/pb"

	"time"

	"github.com/bluele/gcache"
	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/utils/hs"
)

var (
	appCache = gcache.New(5000).LRU().Build()
	uidCache = gcache.New(5000).LRU().Build()
)

type module struct {
	iface.IModule
	*hs.HttpService
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	// initialize HTTP service for routing
	m.HttpService = hs.NewHttpService()
	return m
}

func (m module) Name() string {
	return define.ModuleName.ThirdApp
}

func (m *module) Init() error {
	message.Response(m, onAddThirdAppToThirdReq)
	message.Response(m, onUpdateThirdAppToThirdReq)
	message.Response(m, onGetThirdAppToThirdReq)
	// m.POST("/", m.defaultHandler)
	// group := m.Group("/thirdapp")
	m.POST("/registerPlayer", m.RegisterPlayerReq)
	m.POST("/transferIn", m.TransferInReq)
	m.POST("/play", m.PlayGameReq)
	m.POST("/transferOut", m.TransferOutReq)
	m.POST("/balance", m.BalanceReq)
	m.POST("/record", m.RecordReq)

	err := mdb.Default().Table(ThirdAppTableName).AutoMigrate(&ThirdApp{})
	if err != nil {
		return err
	}
	go func() {
		m.loadAppCache()
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		for range ticker.C {
			m.loadAppCache()
		}
	}()
	return nil
}
func (m *module) Run() error {
	return m.ListenAndServe(fmt.Sprintf(":%d", conf.Num[int]("door.port")))
}

func (m *module) Exit() error {
	m.IModule.Exit()
	return m.Stop(time.Second * 10)
}

func (m *module) loadAppCache() {
	var apps []ThirdApp
	if err := mdb.Default().Table(ThirdAppTableName).Find(&apps).Error; err != nil {
		log.Errorf("loadAppCache error: %v", err)
		return
	}
	appCache.Purge()
	for _, app := range apps {
		appCache.Set(app.AppID, app)
	}
}

func GetUidByAccount(account string, appID string) (int64, error) {
	uid, err := uidCache.Get(account)
	if err == nil {
		return uid.(int64), nil
	}
	resp, err := message.RequestAny[pb.ThirdAppAuthOrCreateAccountResp](define.ModuleName.Account, &pb.ThirdAppAuthOrCreateAccountReq{
		Account: account,
		AppID:   appID,
	})
	if err != nil {
		return 0, err
	}
	if resp.Code != pb.SUCCESS {
		return 0, errors.New("third app create account failed")
	}
	if resp.UserID <= 0 {
		return 0, errors.New("third app create account failed")
	}
	uidCache.Set(account, resp.UserID)
	return resp.UserID, nil
}
