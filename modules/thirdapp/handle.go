package thirdapp

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io"
	"math"
	"net/http"
	"s2/common/cache"
	"s2/mbi"
	"s2/pb"
	"slices"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jfcwrlight/core/infra/chdb"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

// 生成16位随机字符串
func generateRandomKey() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 16)
	if _, err := rand.Read(b); err != nil {
		// 如果随机数生成失败，使用时间戳作为种子生成
		ts := time.Now().UnixNano()
		for i := 0; i < 16; i++ {
			b[i] = charset[ts%int64(len(charset))]
			ts = ts / int64(len(charset))
		}
	} else {
		for i := 0; i < 16; i++ {
			b[i] = charset[b[i]%byte(len(charset))]
		}
	}
	return string(b)
}

// appid 平台分配  eg:1
// Key: 平台分配16位字符串 eg:gNCD3fxsGi2OZSRw
// IV: 平台分配16位字符串 eg:BguU4a7ftj0nuPzj

func (m *module) checkBody(ctx *gin.Context) (string, []byte, error) {
	appid := ctx.Request.Header.Get("appid")
	if appid == "" {
		log.Errorf("appid is required")
		return "", nil, errors.New("appid is required")
	}
	appData, err := appCache.Get(appid)
	if err != nil {
		log.Errorf("appid %s not found", appid)
		return "", nil, err
	}
	thirdApp, ok := appData.(ThirdApp)
	if !ok {
		log.Errorf("appid %s not found", appid)
		return "", nil, errors.New("appid not found")
	}
	ipWhiteList := thirdApp.IPWhiteList
	if slices.Index(ipWhiteList, ctx.ClientIP()) == -1 {
		log.Errorf("ip %s not in white list", ctx.ClientIP())
		return "", nil, errors.New("ip not in white list")
	}
	encryptKey := thirdApp.EncryptKey
	encryptIV := thirdApp.EncryptIV
	b, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		log.Errorf("read body error %s", err)
		return "", nil, err
	}
	b, err = decryptAES(string(b), []byte(encryptKey), []byte(encryptIV))
	if err != nil {
		log.Errorf("decrypt body error %s", err)
		return "", nil, err
	}
	return appid, b, nil
}

func (m *module) checkTimeout(now int64) bool {
	return math.Abs(float64(now-time.Now().Unix())) < 60
}

func (m *module) RegisterPlayerReq(ctx *gin.Context) {
	resp := &pb.RegisterPlayerResp{Code: pb.THIRDAPP_SUCCESS}
	appid, b, err := m.checkBody(ctx)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var req pb.RegisterPlayerReq
	err = json.Unmarshal(b, &req)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if !m.checkTimeout(req.Now) {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	uid, err := GetUidByAccount(req.Account, appid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	resp.UserID = uid
	ctx.JSON(http.StatusOK, resp)
}

func (m *module) TransferInReq(ctx *gin.Context) {
	resp := &pb.TransferInResp{Code: pb.THIRDAPP_SUCCESS}
	appid, b, err := m.checkBody(ctx)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var req pb.TransferInReq
	err = json.Unmarshal(b, &req)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if !m.checkTimeout(req.Now) {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	uid, err := GetUidByAccount(req.Account, appid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if req.Amount < 0 {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if req.Amount == 0 {
		respBalance, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
			UserID: uid,
		})
		if err != nil {
			log.Error(err)
			resp.Code = pb.THIRDAPP_FAILED
			ctx.JSON(http.StatusOK, resp)
			return
		}
		balance := respBalance.Balance[int32(req.AssetID)]
		resp.Balance = balance
		resp.AssetID = req.AssetID
	} else if req.Amount > 0 {
		respChange, err := message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
			UserID: uid,
			Cause:  "thirdapp_transferIn:" + appid,
			Assets: []*pb.IDValFloat{{ID: int64(req.AssetID), Value: req.Amount}},
		})
		if err != nil {
			log.Error(err)
			resp.Code = pb.THIRDAPP_FAILED
			ctx.JSON(http.StatusOK, resp)
			return
		}
		if respChange.Code != pb.SUCCESS {
			resp.Code = pb.THIRDAPP_FAILED
			ctx.JSON(http.StatusOK, resp)
			return
		}
		resp.Amount = req.Amount
		resp.AssetID = req.AssetID
		resp.Balance = respChange.Balance[int32(req.AssetID)]
	}
	ctx.JSON(http.StatusOK, resp)
}

func (m *module) PlayGameReq(ctx *gin.Context) {
	resp := &pb.PlayGameResp{Code: pb.THIRDAPP_SUCCESS}
	appid, b, err := m.checkBody(ctx)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var req pb.PlayGameReq
	err = json.Unmarshal(b, &req)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if !m.checkTimeout(req.Now) {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	uid, err := GetUidByAccount(req.Account, appid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	respLobby, err := message.Request[pb.GamePlayResp](bc.ServerID, &pb.ThirdAppGamePlayReq{
		UserID:   uid,
		GameID:   req.GameID,
		Currency: req.AssetID,
		Language: req.Language,
		Device:   req.Device,
	})
	if err != nil {
		log.Error(err)
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if respLobby.Code != pb.SUCCESS {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	resp.Url = respLobby.URL
	ctx.JSON(http.StatusOK, resp)
}

func (m *module) TransferOutReq(ctx *gin.Context) {
	resp := &pb.TransferOutResp{Code: pb.THIRDAPP_SUCCESS}
	appid, b, err := m.checkBody(ctx)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var req pb.TransferOutReq
	err = json.Unmarshal(b, &req)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if !m.checkTimeout(req.Now) {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	uid, err := GetUidByAccount(req.Account, appid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	respBalance, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: uid,
	})
	if err != nil {
		log.Error(err)
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	balance := respBalance.Balance[int32(req.AssetID)]
	resp.AssetID = req.AssetID
	if balance > 0 {
		respChange, err := message.Request[pb.ChangeAssetResp](bc.ServerID, &pb.ChangeAssetReq{
			UserID: uid,
			Cause:  "thirdapp_transferOut:" + appid,
			Assets: []*pb.IDValFloat{{ID: int64(req.AssetID), Value: -balance}},
		})
		if err != nil {
			log.Error(err)
			resp.Code = pb.THIRDAPP_FAILED
			ctx.JSON(http.StatusOK, resp)
			return
		}
		if respChange.Code != pb.SUCCESS {
			resp.Code = pb.THIRDAPP_FAILED
			ctx.JSON(http.StatusOK, resp)
			return
		}
		resp.Amount = balance
		resp.AssetID = req.AssetID
	}
	ctx.JSON(http.StatusOK, resp)
}

func (m *module) BalanceReq(ctx *gin.Context) {
	resp := &pb.BalanceResp{Code: pb.THIRDAPP_SUCCESS}
	appid, b, err := m.checkBody(ctx)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var req pb.BalanceReq
	err = json.Unmarshal(b, &req)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if !m.checkTimeout(req.Now) {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	uid, err := GetUidByAccount(req.Account, appid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	bc, err := cache.QueryUserBasicInfo(uid)
	if err != nil {
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	respBalance, err := message.Request[pb.AssetBalanceResp](bc.ServerID, &pb.AssetBalanceReq{
		UserID: uid,
	})
	if err != nil {
		log.Error(err)
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	balance := respBalance.Balance[int32(req.AssetID)]
	resp.Balance = balance
	resp.AssetID = req.AssetID
	ctx.JSON(http.StatusOK, resp)
}

func (m *module) RecordReq(ctx *gin.Context) {
	resp := &pb.RecordResp{Code: pb.THIRDAPP_SUCCESS}
	appid, b, err := m.checkBody(ctx)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var req pb.RecordReq
	err = json.Unmarshal(b, &req)
	if err != nil {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	if !m.checkTimeout(req.Now) {
		resp.Code = pb.THIRDAPP_PARAM_ERROR
		ctx.JSON(http.StatusOK, resp)
		return
	}
	var records []*struct {
		EventID     uint64 // 唯一id
		UserID      int32
		AssetID     int32
		Input       float64
		Output      float64
		GameID      int32
		AccountName string
		CreateAt    int64
	}
	log.Infof("appid: %s", appid)
	err = chdb.Default().Table(mbi.TableSpinResult).
		Select("event_id", "user_id", "asset_id", "input", "output", "game_id", "account_name", "create_at").
		Where("create_at >= ? AND create_at <= ?", req.StartTime, req.EndTime).
		// Where("create_at >= ? AND create_at <= ? AND channel = ?", req.StartTime, req.EndTime, appid).
		Order("create_at DESC").
		Offset(int((req.Offset - 1) * req.Limit)).
		Limit(int(req.Limit)).
		Find(&records).Error
	if err != nil {
		log.Error(err)
		resp.Code = pb.THIRDAPP_FAILED
		ctx.JSON(http.StatusOK, resp)
		return
	}
	rows := make([]*pb.RecordRow, 0, len(records))
	for _, r := range records {
		rows = append(rows, &pb.RecordRow{
			EventID:     strconv.FormatUint(r.EventID, 10),
			UserID:      r.UserID,
			Input:       r.Input,
			Output:      r.Output,
			GameID:      r.GameID,
			AccountName: r.AccountName,
			AssetID:     pb.EnumAsset(r.AssetID),
			CreateAt:    r.CreateAt,
		})
	}
	resp.Rows = rows
	ctx.JSON(http.StatusOK, resp)
}

func unPaddingBytes(src []byte) []byte {
	l := len(src)
	n := int(src[l-1])
	return src[:l-n]
}

// decryptAES decrypts a Base64-encoded AES-CBC encrypted string.
// decryptAES decodes the Base64 ciphertext, decrypts it using AES-CBC with the provided key and IV, and removes PKCS7 padding to return the original plaintext.
// decryptAES 函数将 Base64 编码的密文解码后，使用给定的密钥和 IV 进行 AES-CBC 解密，并移除 PKCS7 填充以返回原始明文。
func decryptAES(src string, encryptKey []byte, encryptIV []byte) ([]byte, error) {
	ciphertext, err := base64.StdEncoding.DecodeString(src)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(encryptKey)
	if err != nil {
		return nil, err
	}
	cbcDecrypter := cipher.NewCBCDecrypter(block, encryptIV)
	dst := make([]byte, len(ciphertext))
	cbcDecrypter.CryptBlocks(dst, ciphertext)
	dst = unPaddingBytes(dst)
	return dst, nil
}

func onAddThirdAppToThirdReq(body *pb.AddThirdAppToThirdReq, response func(*pb.AddThirdAppToThirdResp, error)) {
	resp := &pb.AddThirdAppToThirdResp{Code: pb.SUCCESS}

	// 参数验证
	if body.AppID == "" {
		resp.Code = pb.PARAM_ERROR
		response(resp, nil)
		return
	}
	_, err := strconv.ParseInt(body.AppID, 10, 64) //AppID必须是数字
	if err != nil {
		resp.Code = pb.PARAM_ERROR
		response(resp, nil)
		return
	}

	// 自动生成或验证EncryptKey
	encryptKey := body.EncryptKey
	if len(encryptKey) != 16 {
		encryptKey = generateRandomKey()
		log.Infof("Auto generated EncryptKey for AppID %d: %s", body.AppID, encryptKey)
	}

	// 自动生成或验证EncryptIV
	encryptIV := body.EncryptIV
	if len(encryptIV) != 16 {
		encryptIV = generateRandomKey()
		log.Infof("Auto generated EncryptIV for AppID %d: %s", body.AppID, encryptIV)
	}

	// 检查AppID是否已存在
	var existingApp ThirdApp
	err = mdb.Default().Table(ThirdAppTableName).Where("appID = ?", body.AppID).First(&existingApp).Error
	if err == nil {
		resp.Code = pb.PARAM_ERROR
		response(resp, nil)
		return
	}

	// 创建新的第三方应用
	newApp := ThirdApp{
		AppID:       body.AppID,
		EncryptKey:  encryptKey,
		EncryptIV:   encryptIV,
		Open:        body.Open,
		Name:        body.Name,
		IPWhiteList: StringSlice(body.IPWhiteList),
		CreatedAt:   time.Now().Unix(),
		UpdatedAt:   time.Now().Unix(),
	}

	// 保存到数据库
	if err := mdb.Default().Table(ThirdAppTableName).Create(&newApp).Error; err != nil {
		log.Errorf("create third app error: %v", err)
		resp.Code = pb.SERVER_ERROR
		response(resp, nil)
		return
	}

	// 立即刷新缓存
	go func() {
		var apps []ThirdApp
		if err := mdb.Default().Table(ThirdAppTableName).Find(&apps).Error; err != nil {
			log.Errorf("loadAppCache error: %v", err)
			return
		}
		appCache.Purge()
		for _, app := range apps {
			appCache.Set(app.AppID, app)
		}
	}()

	log.Infof("Third app created successfully, AppID: %s", body.AppID)
	response(resp, nil)
}

func onUpdateThirdAppToThirdReq(body *pb.UpdateThirdAppToThirdReq, response func(*pb.UpdateThirdAppToThirdResp, error)) {
	resp := &pb.UpdateThirdAppToThirdResp{Code: pb.SUCCESS}

	// 参数验证
	if body.AppID == "" {
		resp.Code = pb.PARAM_ERROR
		response(resp, nil)
		return
	}
	_, err := strconv.ParseInt(body.AppID, 10, 64) //AppID必须是数字
	if err != nil {
		resp.Code = pb.PARAM_ERROR
		response(resp, nil)
		return
	}

	// 自动生成或验证EncryptKey
	encryptKey := body.EncryptKey
	if len(encryptKey) != 16 {
		encryptKey = generateRandomKey()
		log.Infof("Auto generated EncryptKey for AppID %d: %s", body.AppID, encryptKey)
	}

	// 自动生成或验证EncryptIV
	encryptIV := body.EncryptIV
	if len(encryptIV) != 16 {
		encryptIV = generateRandomKey()
		log.Infof("Auto generated EncryptIV for AppID %d: %s", body.AppID, encryptIV)
	}

	// 检查AppID是否存在
	var existingApp ThirdApp
	err = mdb.Default().Table(ThirdAppTableName).Where("appID = ?", body.AppID).First(&existingApp).Error
	if err != nil {
		resp.Code = pb.PARAM_ERROR
		response(resp, nil)
		return
	}

	// 更新数据
	updateApp := ThirdApp{
		AppID:       body.AppID,
		EncryptKey:  encryptKey,
		EncryptIV:   encryptIV,
		IPWhiteList: StringSlice(body.IPWhiteList),
		Open:        body.Open,
		Name:        body.Name,
		UpdatedAt:   time.Now().Unix(),
	}

	// 更新数据库
	if err := mdb.Default().Table(ThirdAppTableName).Where("appID = ?", body.AppID).Updates(&updateApp).Error; err != nil {
		log.Errorf("update third app error: %v", err)
		resp.Code = pb.SERVER_ERROR
		response(resp, nil)
		return
	}

	// 立即刷新缓存
	go func() {
		var apps []ThirdApp
		if err := mdb.Default().Table(ThirdAppTableName).Find(&apps).Error; err != nil {
			log.Errorf("loadAppCache error: %v", err)
			return
		}
		appCache.Purge()
		for _, app := range apps {
			appCache.Set(app.AppID, app)
		}
	}()

	log.Infof("Third app updated successfully, AppID: %s", body.AppID)
	response(resp, nil)
}

func onGetThirdAppToThirdReq(body *pb.GetThirdAppToThirdReq, response func(*pb.GetThirdAppToThirdResp, error)) {
	resp := &pb.GetThirdAppToThirdResp{Code: pb.SUCCESS}

	var apps []ThirdApp
	query := mdb.Default().Table(ThirdAppTableName)

	if err := query.Find(&apps).Error; err != nil {
		log.Errorf("get third app error: %v", err)
		resp.Code = pb.SERVER_ERROR
		response(resp, nil)
		return
	}

	log.Infof("Get third app list successfully, count: %d", len(apps))
	resp.AppList = make([]*pb.ThirdApp, 0, len(apps))
	for _, app := range apps {
		resp.AppList = append(resp.AppList, &pb.ThirdApp{
			AppID:       app.AppID,
			EncryptKey:  app.EncryptKey,
			EncryptIV:   app.EncryptIV,
			Open:        app.Open,
			Name:        app.Name,
			IPWhiteList: app.IPWhiteList,
			CreatedAt:   app.CreatedAt,
			UpdatedAt:   app.UpdatedAt,
		})
	}
	response(resp, nil)
}
