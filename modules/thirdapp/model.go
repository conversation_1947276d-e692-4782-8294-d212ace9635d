package thirdapp

import (
	"database/sql/driver"
	"encoding/json"
)

const ThirdAppTableName = "thirdapp"

type StringSlice []string

func (s StringSlice) Value() (driver.Value, error) {
	return json.Marshal(s)
}

func (s *StringSlice) Scan(src interface{}) error {
	return json.Unmarshal(src.([]byte), s)
}

type ThirdApp struct {
	AppID       string      `gorm:"column:appID;type:varchar(254);primary_key;"`
	EncryptKey  string      `gorm:"column:encryptKey;type:varchar(254);"`
	EncryptIV   string      `gorm:"column:encryptIV;type:varchar(254);"`
	Open        bool        `gorm:"column:open;type:tinyint(1);default:0;"`
	IPWhiteList StringSlice `gorm:"column:ipWhiteList;type:longtext"`
	Name        string      `gorm:"column:name;type:varchar(254);"`
	CreatedAt   int64       `gorm:"column:createdAt;type:int(11);default:0;"`
	UpdatedAt   int64       `gorm:"column:updatedAt;type:int(11);default:0;"`
}
