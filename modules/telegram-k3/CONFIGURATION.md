# K3游戏配置说明

## 📋 **配置方式**

### 🔧 **配置文件配置**

所有配置项都可以在配置文件中设置：`service/telegram/configs.yaml`

```yaml
# ==================== Telegram K3 游戏配置 ====================
telegramk3:
  # ==================== 时间配置 ====================
  BettingDuration: "30s"                    # 下注阶段时长
  RollingDuration: "25s"                    # 掷骰子阶段时长
  NextGameDelay: "1s"                      # 下一局游戏开始前的延迟
  MessageRetryDelay: "2s"                  # 消息发送失败时的重试延迟
  MessageRetryCount: "3"                   # 消息发送失败时的最大重试次数
  BotRollDelay: "3s"                       # 无人下注时机器人掷骰子的延迟
  SettlementDelay: "1s"                    # 玩家掷满3次骰子后的结算延迟
  DiceAnimationDelay: "500ms"              # 机器人发送多个骰子动画之间的间隔
  DiceAnimationCompleteDelay: "4500ms"     # 骰子动画播放完成后的等待时间

  # ==================== 消息配置 ====================
  MessageSenderID: "**********"            # 消息发送者ID

  # ==================== 网格配置 ====================
  GridWidth: "14"                          # 网格横向大小（列数）
  GridHeight: "7"                          # 网格纵向大小（行数）
  StatsNumbers: "3,18"                     # 需要在统计行显示出现次数的特定数字

  # ==================== 骰子配置 ====================
  DiceCount: "3"                           # 骰子数量
  DiceMinValue: "1"                        # 骰子最小点数
  DiceMaxValue: "6"                        # 骰子最大点数
  MaxDiceRolls: "3"                        # 最大掷骰子次数

  # ==================== 分类限额配置 ====================
  BasicBetLimit: "2000"                    # 大小单双限额
  CombinationBetLimit: "500"               # 组合玩法限额
  SpecialBetLimit: "250"                   # 特殊玩法限额

  # ==================== 总体限额配置 ====================
  TotalBetLimit: "10000"                   # 用户单期总下注限额

  # ==================== 底注配置 ====================
  BaseBet: "100"                           # 底注金额

  # ==================== 赔率配置 ====================
  odds:
    Basic: "1.97"                          # 大小单双赔率
    BigOdd: "3.4"                          # 大单赔率
    SmallOdd: "4.4"                        # 小单赔率
    BigEven: "4.4"                         # 大双赔率
    SmallEven: "3.4"                       # 小双赔率
    Leopard: "32.0"                        # 豹子赔率
    Straight: "8.0"                        # 顺子赔率
    Pair: "2.1"                            # 对子赔率
    LeopardSpecific: "166.0"               # 指定豹子赔率
    Special4: "58.0"                       # 定位胆4赔率
    Special5: "28.0"                       # 定位胆5赔率
    Special6: "16.0"                       # 定位胆6赔率
    Special7: "12.0"                       # 定位胆7赔率
    Special8: "8.0"                        # 定位胆8赔率
    Special9: "7.0"                        # 定位胆9赔率
    Special10: "7.0"                       # 定位胆10赔率
    Special11: "6.0"                       # 定位胆11赔率
    Special12: "6.0"                       # 定位胆12赔率
    Special13: "8.0"                       # 定位胆13赔率
    Special14: "12.0"                      # 定位胆14赔率
    Special15: "16.0"                      # 定位胆15赔率
    Special16: "28.0"                      # 定位胆16赔率
    Special17: "58.0"                      # 定位胆17赔率
```

### 🔧 **环境变量配置**

所有配置项也支持通过环境变量进行设置，环境变量格式为 `telegramk3.配置项名`。

### 时间配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `BETTING_BETTING_DURATION` | `5s` | 下注阶段时长 |
| `BETTING_ROLLING_DURATION` | `5s` | 掷骰子阶段时长 |
| `BETTING_NEXT_GAME_DELAY` | `1s` | 下一局游戏开始前的延迟 |
| `BETTING_MESSAGE_RETRY_DELAY` | `2s` | 消息发送失败时的重试延迟 |
| `BETTING_MESSAGE_RETRY_COUNT` | `3` | 消息发送失败时的最大重试次数 |
| `BETTING_BOT_ROLL_DELAY` | `3s` | 无人下注时机器人掷骰子的延迟 |
| `BETTING_SETTLEMENT_DELAY` | `1s` | 玩家掷满骰子后的结算延迟 |
| `BETTING_DICE_ANIMATION_DELAY` | `500ms` | 机器人发送多个骰子动画之间的间隔 |
| `BETTING_DICE_ANIMATION_COMPLETE_DELAY` | `4500ms` | 骰子动画播放完成后的等待时间 |

### 消息配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `BETTING_MESSAGE_SENDER_ID` | `**********` | Telegram机器人的用户ID |

### 庄家配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `telegramk3.BankerName` | `小马` | 庄家名称 |
| `telegramk3.BankerID` | `*********` | 庄家ID |
| `telegramk3.BankerBalance` | `1000000.0` | 庄家初始余额 |
| `telegramk3.BaseBet` | `100` | 底注金额 |

### 底注配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `telegramk3.BaseBet` | `100` | 底注金额 |

### 网格配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `BETTING_GRID_WIDTH` | `14` | 网格横向大小（列数） |
| `BETTING_GRID_HEIGHT` | `7` | 网格纵向大小（行数） |
| `BETTING_STATS_NUMBERS` | `3,18` | 统计的指定数字列表 |

### 最大期数配置

最大期数限制根据网格配置自动计算得出：

- **计算公式**: `GridWidth × (GridHeight - 1)`
- **说明**: 最后一行用于统计信息，不能用于显示期数

**示例**: 网格14×7
- 总显示容量: 14 × 7 = 98
- 实际可用容量: 14 × 6 = 84
- **最大期数**: 84

### 分类限额配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `BETTING_BASIC_BET_LIMIT` | `2000` | 大小单双限额 |
| `BETTING_COMBINATION_BET_LIMIT` | `500` | 组合玩法限额 |
| `BETTING_SPECIAL_BET_LIMIT` | `250` | 特殊玩法限额 |

### 游戏规则配置

游戏规则配置（大小单双的数值范围）根据骰子配置自动计算得出：

- **小的范围**: `[DiceCount × DiceMinValue, (DiceCount × DiceMinValue + DiceCount × DiceMaxValue) / 2]`
- **大的范围**: `[(DiceCount × DiceMinValue + DiceCount × DiceMaxValue) / 2 + 1, DiceCount × DiceMaxValue]`

**示例**: 3个骰子，点数范围[1-6]
- 最小总和: 3 × 1 = 3
- 最大总和: 3 × 6 = 18
- 分界点: (3 + 18) / 2 = 10.5
- 小的范围: [3, 10]
- 大的范围: [11, 18]

### 骰子配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `BETTING_DICE_COUNT` | `3` | 骰子数量 |
| `BETTING_DICE_MIN_VALUE` | `1` | 骰子最小点数 |
| `BETTING_DICE_MAX_VALUE` | `6` | 骰子最大点数 |
| `BETTING_MAX_DICE_ROLLS` | `3` | 最大掷骰子次数 |

### 赔率配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `telegramk3.odds.Basic` | `1.97` | 大小单双赔率 |
| `telegramk3.odds.BigOdd` | `3.4` | 大单赔率 |
| `telegramk3.odds.SmallOdd` | `4.4` | 小单赔率 |
| `telegramk3.odds.BigEven` | `4.4` | 大双赔率 |
| `telegramk3.odds.SmallEven` | `3.4` | 小双赔率 |
| `telegramk3.odds.Leopard` | `32.0` | 豹子赔率 |
| `telegramk3.odds.Straight` | `8.0` | 顺子赔率 |
| `telegramk3.odds.Pair` | `2.1` | 对子赔率 |
| `telegramk3.odds.LeopardSpecific` | `166.0` | 指定豹子赔率 |
| `telegramk3.odds.Special4` | `58.0` | 定位胆4赔率 |
| `telegramk3.odds.Special5` | `28.0` | 定位胆5赔率 |
| `telegramk3.odds.Special6` | `16.0` | 定位胆6赔率 |
| `telegramk3.odds.Special7` | `12.0` | 定位胆7赔率 |
| `telegramk3.odds.Special8` | `8.0` | 定位胆8赔率 |
| `telegramk3.odds.Special9` | `7.0` | 定位胆9赔率 |
| `telegramk3.odds.Special10` | `7.0` | 定位胆10赔率 |
| `telegramk3.odds.Special11` | `6.0` | 定位胆11赔率 |
| `telegramk3.odds.Special12` | `6.0` | 定位胆12赔率 |
| `telegramk3.odds.Special13` | `8.0` | 定位胆13赔率 |
| `telegramk3.odds.Special14` | `12.0` | 定位胆14赔率 |
| `telegramk3.odds.Special15` | `16.0` | 定位胆15赔率 |
| `telegramk3.odds.Special16` | `28.0` | 定位胆16赔率 |
| `telegramk3.odds.Special17` | `58.0` | 定位胆17赔率 |

### 限额配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `telegramk3.BasicBetLimit` | `2000` | 大小单双限额 |
| `telegramk3.CombinationBetLimit` | `500` | 组合玩法限额 |
| `telegramk3.SpecialBetLimit` | `250` | 特殊玩法限额 |
| `telegramk3.TotalBetLimit` | `10000` | 用户单期总下注限额 |

### 其他配置

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `BETTING_MAX_PERIODS` | `84` | 最大期数限制 |

## 配置示例

### 开发环境配置

```bash
# 时间配置 - 快速测试
export BETTING_BETTING_DURATION="10s"
export BETTING_ROLLING_DURATION="10s"
export BETTING_NEXT_GAME_DELAY="2s"

# 限额配置 - 测试用
export BETTING_BASIC_BET_LIMIT="1000"
export BETTING_COMBINATION_BET_LIMIT="200"
export BETTING_SPECIAL_BET_LIMIT="100"

# 赔率配置 - 测试用
export telegramk3.odds.Basic="2.0"
export telegramk3.odds.BigOdd="3.5"
export telegramk3.odds.SmallOdd="4.5"
export telegramk3.odds.BigEven="4.5"
export telegramk3.odds.SmallEven="3.5"
export telegramk3.odds.Leopard="30.0"
export telegramk3.odds.Straight="7.0"
export telegramk3.odds.Pair="2.0"
export telegramk3.odds.LeopardSpecific="150.0"

# 限额配置 - 测试用
export telegramk3.BasicBetLimit="1000"
export telegramk3.CombinationBetLimit="200"
export telegramk3.SpecialBetLimit="100"
export telegramk3.TotalBetLimit="5000"

# 庄家配置
export telegramk3.BankerName="测试庄家"
export telegramk3.BankerID="*********"
export telegramk3.BankerBalance="10000"
export telegramk3.BaseBet="100"
```

### 生产环境配置

```bash
# 时间配置 - 生产环境
export BETTING_BETTING_DURATION="30s"
export BETTING_ROLLING_DURATION="20s"
export BETTING_NEXT_GAME_DELAY="5s"

# 限额配置 - 生产环境
export BETTING_BASIC_BET_LIMIT="5000"
export BETTING_COMBINATION_BET_LIMIT="1000"
export BETTING_SPECIAL_BET_LIMIT="500"

# 赔率配置 - 生产环境
export telegramk3.odds.Basic="1.95"
export telegramk3.odds.BigOdd="3.3"
export telegramk3.odds.SmallOdd="4.3"
export telegramk3.odds.BigEven="4.3"
export telegramk3.odds.SmallEven="3.3"
export telegramk3.odds.Leopard="30.0"
export telegramk3.odds.Straight="7.5"
export telegramk3.odds.Pair="2.0"
export telegramk3.odds.LeopardSpecific="160.0"

# 限额配置 - 生产环境
export telegramk3.BasicBetLimit="5000"
export telegramk3.CombinationBetLimit="1000"
export telegramk3.SpecialBetLimit="500"
export telegramk3.TotalBetLimit="20000"

# 庄家配置
export telegramk3.BankerName="正式庄家"
export telegramk3.BankerID="*********"
export telegramk3.BankerBalance="1000000"
export telegramk3.BaseBet="100"
```

### 自定义游戏规则

```bash
# 自定义大小单双边界
export BETTING_BIG_MIN="12"
export BETTING_BIG_MAX="20"
export BETTING_SMALL_MIN="4"
export BETTING_SMALL_MAX="11"

# 自定义骰子配置
export BETTING_DICE_COUNT="4"
export BETTING_DICE_MIN_VALUE="1"
export BETTING_DICE_MAX_VALUE="8"
export BETTING_MAX_DICE_ROLLS="4"
```

## 配置加载

配置在程序启动时自动加载，可以通过以下方式访问配置：

```go
import "s2/modules/telegram-k3/configs"

// 获取当前配置
config := configs.GetConfig()

// 直接访问配置项
bettingDuration := config.BettingDuration
basicBetLimit := config.BasicBetLimit
bankerName := config.BankerName

// 打印配置信息
fmt.Printf("当前配置: %+v\n", config)
```

## 配置访问方式

### 1. 直接访问配置结构体
```go
config := configs.GetConfig()

// 时间配置
duration := config.BettingDuration
rollingTime := config.RollingDuration

// 限额配置
basicLimit := config.BasicBetLimit
combinationLimit := config.CombinationBetLimit
specialLimit := config.SpecialBetLimit

// 游戏规则配置
bigMin := config.BigMin
bigMax := config.BigMax
smallMin := config.SmallMin
smallMax := config.SmallMax

// 骰子配置
diceCount := config.DiceCount
diceMinValue := config.DiceMinValue
diceMaxValue := config.DiceMaxValue
maxDiceRolls := config.MaxDiceRolls

// 庄家配置
bankerName := config.BankerName
bankerID := config.BankerID
bankerBalance := config.BankerBalance
baseBet := config.BaseBet

// 网格配置
gridWidth := config.GridWidth
gridHeight := config.GridHeight
statsNumbers := config.StatsNumbers
```

### 2. 使用便捷函数（仅对有额外逻辑的配置）
```go
// 赔率配置 - 有默认值处理
odds := configs.GetBetOdds("big")

// 限额配置 - 有默认值处理
limit := configs.GetBetLimit("big")

// 分类限额 - 有分类逻辑
categoryLimit := configs.GetBetLimitByCategory("big")

// 下注消息判断 - 有复杂逻辑
isBetting := configs.IsBettingMessage("大100")
```

## 动态配置更新

配置支持在运行时动态更新：

```go
// 获取当前配置
config := configs.GetConfig()

// 修改配置
config.BasicBetLimit = 3000
config.BettingDuration = 20 * time.Second
config.BankerName = "新庄家"

// 应用新配置
configs.SetConfig(config)

// 或者直接修改全局配置
configs.GetConfig().BasicBetLimit = 3000
```

## 注意事项

1. **时间格式**: 时间配置支持Go的`time.Duration`格式，如`5s`、`2m30s`、`1h`等
2. **数值范围**: 确保配置的数值在合理范围内，避免游戏逻辑错误
3. **限额关系**: 确保各类限额配置合理，避免玩家无法正常下注
4. **骰子配置**: 修改骰子配置会影响游戏逻辑，需要同时更新相关规则
5. **配置验证**: 程序启动时会验证配置的合理性，不合理的配置会使用默认值

## 配置优先级

1. **配置文件** (最高优先级) - `/Users/<USER>/work/cd/datax/service/telegram/configs.yaml`
2. **环境变量** (次高优先级) - `telegramk3.配置项名`
3. **代码设置** (通过`SetConfig`函数)
4. **默认配置** (最低优先级)

配置加载顺序：默认配置 → 配置文件覆盖 → 环境变量覆盖 → 代码设置覆盖 