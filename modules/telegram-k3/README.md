# Telegram 群组骰子下注游戏

基于 Telegram Bot API 实现的群组骰子下注游戏，支持多种下注类型、实时结算、图片网格显示等功能。

## 🎯 核心功能

### 游戏流程
1. **下注阶段**：玩家可以下注，支持多种下注类型
2. **掷骰子阶段**：最高下注玩家掷骰子，或机器人代掷
3. **结算阶段**：根据开奖结果结算输赢

### 下注类型
- **基础下注**：大、小、单、双
- **组合下注**：大单、大双、小单、小双
- **高倍下注**：豹子
- **特码下注**：定位胆

## 🔧 重点问题与解决方案

### 1. 游戏状态管理 ✅
**问题**：确保游戏在正确的阶段执行相应的操作
**解决方案**：
- 严格的状态检查：仅在下注阶段接受下注，仅在掷骰子阶段接受掷骰子
- 状态机管理：`GameStateBetting` → `GameStateRolling` → `GameStateSettling`
- 详细的状态日志记录

### 2. 掷骰子权限控制 ✅
**问题**：确保只有最高下注玩家可以掷骰子
**解决方案**：
- 获取最高下注玩家：`getMaxBetUser()`
- 权限验证：检查当前用户是否为最高下注玩家
- 友好的错误提示

### 3. 冻结金额机制 ✅
**问题**：下注时正确管理用户余额，防止超额下注
**解决方案**：
- 下注时只冻结不扣除：`freezeUserBalance()`
- 可用余额计算：`当前余额 - 已冻结金额`
- 结算时根据输赢决定是否扣除：赢家不扣除，输家扣除冻结金额

### 4. 庄家余额溢出检查 ✅
**问题**：防止庄家余额不足导致无法赔付
**解决方案**：
- 下注前检查庄家余额：`庄家余额 >= 最大赔付金额`
- 最大赔付计算：`下注金额 × 赔率`
- 拒绝可能导致庄家余额不足的下注

### 5. 结算流程优化 ✅
**问题**：确保结算时正确更新所有用户余额
**解决方案**：
- 赢家处理：获得赔付，不扣除下注金额
- 输家处理：扣除冻结的下注金额
- 庄家处理：获得输家金额，赔付赢家金额
- 冻结金额重置：无论输赢都重置

### 6. 掷骰子阶段优化 ✅
**问题**：玩家掷完最后一颗骰子后立即结算
**解决方案**：
- 检测第3次掷骰子完成
- 停止掷骰子阶段定时器
- 等待动画播放完成后立即结算
- 防止重复结算

### 7. 图片网格显示 ✅
**问题**：生成美观的开奖结果网格图片
**解决方案**：
- 动态图片生成：`ImageGenerator`
- 支持中文字体：`MicrosoftYaHei.ttf`
- 统计信息显示：指定数字的出现次数
- 图片与文本合并发送

## 🛠️ 技术特点

### 内存余额系统
- 实时余额管理：`UserBalances map[int64]float64`
- 程序重启后重置
- 不持久化到数据库

### 消息重试机制
- 可配置重试次数和延迟
- 防止消息发送失败
- 详细的错误处理

### 配置化管理
- 环境变量支持
- 可调整的时间参数
- 灵活的网格配置

## 📋 配置参数

### 时间配置
- `BETTING_BETTING_DURATION`: 下注阶段时长
- `BETTING_ROLLING_DURATION`: 掷骰子阶段时长
- `BETTING_DICE_ANIMATION_COMPLETE_DELAY`: 骰子动画播放时间

### 网格配置
- `BETTING_GRID_WIDTH`: 网格宽度
- `BETTING_GRID_HEIGHT`: 网格高度
- `BETTING_STATS_NUMBERS`: 统计数字列表

### 庄家配置
- `BETTING_BANKER_NAME`: 庄家名称
- `BETTING_BANKER_ID`: 庄家ID
- `BETTING_BANKER_BALANCE`: 庄家初始余额

## 🚀 部署说明

1. **环境准备**：
   ```bash
   # 设置环境变量
   export BETTING_BETTING_DURATION=30s
   export BETTING_ROLLING_DURATION=25s
   export BETTING_BANKER_NAME="JIBA"
   export BETTING_BANKER_ID=**********
   ```

2. **编译运行**：
   ```bash
   go build -o betting .
   ./betting
   ```

3. **字体文件**：
   - 确保 `statics/MicrosoftYaHei.ttf` 存在（中文字体）
   - 确保 `statics/bahnschrift.ttf` 存在（英文字体）

## 📝 命令列表

- `/start_game`: 开始新游戏
- `/bet`: 下注命令
- `/status`: 查看游戏状态
- `/help`: 查看帮助信息
- `/history`: 查看历史记录
- `/stats`: 查看统计数据
- `/debug`: 系统诊断
- `/dice_grid`: 查看开奖网格

## 🔍 调试功能

- 详细的状态日志
- 余额变化跟踪
- 错误信息记录
- 系统诊断命令

## 📊 监控指标

- 游戏状态转换
- 下注成功率
- 消息发送成功率
- 余额变化统计

---

**注意**：本系统使用内存余额管理，程序重启后余额会重置。生产环境需要集成真实的用户系统。 