package telegramk3

import (
	"fmt"
	"strings"

	"s2/common"
	"s2/common/cache"
	"s2/define"
	"s2/pb"
	"sync"

	"s2/modules/telegram-k3/configs"
	"s2/modules/telegram-k3/game"
	"s2/modules/telegram-k3/models"

	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
)

var cacheUids sync.Map = sync.Map{}

type module struct {
	iface.IModule
	adminIds    []int64
	operatorIds []int64
	bot         *tgbotapi.BotAPI
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	return m
}

func (m *module) Name() string {
	return define.ModuleName.Telegram
}

func (m *module) Init() error {
	// 初始化下注游戏数据库表
	if err := mdb.Default().AutoMigrate(&models.TelegramK3GamePeriod{}, &models.TelegramK3BetRecord{}, &models.TelegramK3DiceResult{}); err != nil {
		log.Errorf("Failed to migrate betting tables: %v", err)
		return err
	}
	m.adminIds = conf.List("telegram.AdminIds", []int64{})
	m.operatorIds = conf.List("telegram.OperatorIds", []int64{})

	// 加载配置
	configs.LoadConfig()

	return nil
}

func (m *module) Run() error {
	conc.Go(func() {
		m.runTelegram()
	})
	return nil
}

func (m *module) Exit() error {
	m.IModule.Exit()
	return nil
}

// runTelegram 运行Telegram机器人
func (m *module) runTelegram() error {
	token := conf.Str("telegram.token", "")
	bot, err := tgbotapi.NewBotAPI(token)
	if err != nil {
		panic(err)
	}
	m.bot = bot
	log.Infof("初始化BotAPI成功：%d:%s", bot.Self.ID, bot.Self.UserName)

	// 设置bot实例到游戏模块
	game.SetBot(bot)

	u := tgbotapi.NewUpdate(0)
	u.Timeout = 60
	updates := bot.GetUpdatesChan(u)
	// Handle updates with context cancellation support
	for {
		select {
		case <-system.RootCtx().Done():
			log.Info("Telegram service shutting down...")
			return nil
		case update, ok := <-updates:
			if !ok {
				log.Info("Telegram updates channel closed")
				return nil
			}
			go m.telegramHandle(update)
		}
	}
}

// telegramHandle 处理Telegram更新
func (m *module) telegramHandle(update tgbotapi.Update) error {
	defer utils.RecoverPanic()
	if update.CallbackQuery != nil {
		// 处理下注相关的回调
		if err := m.HandleCallback(&update); err != nil {
			log.Errorf("处理下注回调查询失败: %v", err)
		}
	} else if update.Message != nil {
		text := update.Message.Text
		chatID := update.Message.Chat.ID
		fromID := update.Message.From.ID
		userName := update.Message.From.UserName
		chatType := update.Message.Chat.Type

		log.Info("收到消息 chatID:", chatID, " fromID:", fromID, " userName:", userName, " chatType:", chatType, " text:", text)

		// 处理下注相关消息
		if err := m.HandleMessage(&update); err != nil {
			// 检查是否是"不是下注消息"的特殊错误
			if err.Error() == "not_betting_message" {
				// 不是下注消息，继续处理其他命令
				log.Infof("消息不是下注相关，继续处理其他命令")
			} else {
				// 真正的错误
				log.Errorf("处理下注消息失败: %v", err)
			}
		}
	}
	return nil
}

func GetGameUid(telegramId int64, name string, parent int64) (int64, error) {
	uid, ok := cacheUids.Load(telegramId)
	if ok {
		return uid.(int64), nil
	}
	token := conf.Str("telegram.token", "")
	data, err := common.CreateTelegramLoginToken(telegramId, name, token)
	if err != nil {
		return 0, err
	}
	resp, err := message.RequestAny[pb.TelegramLoginResp](define.ModuleName.Account, &pb.TelegramLoginToAccountReq{
		TelegramData: data,
		ParentUid:    parent,
	})
	if err != nil {
		return 0, err
	}
	bc, err := cache.QueryUserBasicInfoByToken(resp.Token)
	if err != nil {
		return 0, err
	}
	cacheUids.Store(telegramId, bc.ID)
	return bc.ID, nil
}

// HandleMessage 处理消息
func (m *module) HandleMessage(update *tgbotapi.Update) error {
	text := update.Message.Text
	chatID := update.Message.Chat.ID

	// 获取游戏状态信息
	manager := game.GetGameManager(chatID)
	gameState := "无游戏"
	if manager != nil && manager.CurrentGame != nil {
		gameState = manager.CurrentGame.CurrentState
	}

	// 检查是否是骰子消息
	if update.Message.Dice != nil {
		log.Infof("[游戏状态:%s] 识别为骰子消息 - 群组ID: %d", gameState, chatID)
		return game.HandleDiceCommand(update)
	}

	log.Infof("[游戏状态:%s] 下注模块检查消息: '%s' - 群组ID: %d", gameState, text, chatID)

	// 检查是否是下注相关命令
	if m.isBettingCommand(text) {
		log.Infof("[游戏状态:%s] 识别为下注命令: '%s' - 群组ID: %d", gameState, text, chatID)
		return m.handleBettingCommand(update)
	}

	// 检查是否是下注消息（不包含命令前缀）
	if configs.IsBettingMessage(text) {
		log.Infof("[游戏状态:%s] 识别为下注消息: '%s' - 群组ID: %d", gameState, text, chatID)
		return game.HandleBetCommand(update)
	}

	log.Infof("[游戏状态:%s] 不是下注相关消息: '%s' - 群组ID: %d", gameState, text, chatID)
	// 不是下注相关消息，返回特殊错误表示未处理
	return fmt.Errorf("not_betting_message")
}

// HandleCallback 处理回调查询
func (m *module) HandleCallback(update *tgbotapi.Update) error {
	if update.CallbackQuery != nil {
		return game.HandleCallbackQuery(update)
	}
	return nil
}

// isBettingCommand 检查是否是下注相关命令
func (m *module) isBettingCommand(text string) bool {
	bettingCommands := []string{
		"/start",
		"/status",
		"/help",
		"/history",
		"/stats",
		"/debug",
		"/dice_grid",
	}

	for _, cmd := range bettingCommands {
		if strings.HasPrefix(text, cmd) {
			return true
		}
	}

	return false
}

// handleBettingCommand 处理下注相关命令
func (m *module) handleBettingCommand(update *tgbotapi.Update) error {
	text := update.Message.Text

	switch {
	case strings.HasPrefix(text, "/start"):
		return game.HandleStartCommand(update)
	case strings.HasPrefix(text, "/status"):
		return game.HandleStatusCommand(update)
	case strings.HasPrefix(text, "/help"):
		return game.HandleHelpCommand(update)
	case strings.HasPrefix(text, "/history"):
		return game.HandleHistoryCommand(update)
	case strings.HasPrefix(text, "/stats"):
		return game.HandleStatsCommand(update)
	case strings.HasPrefix(text, "/debug"):
		return game.HandleDebugCommand(update)
	case strings.HasPrefix(text, "/dice_grid"):
		return game.HandleDiceGridCommand(update)
	}

	return nil
}
