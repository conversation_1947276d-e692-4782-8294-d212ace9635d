package game

import (
	"fmt"
	"math"
	"s2/modules/telegram-k3/configs"
)

// FormatCurrency 格式化货币显示（带单位）
// amount: 金额（整数）
// currencyUnit: 货币单位
// 返回格式化的货币字符串，如 "100u", "50btc"
func FormatCurrency(amount int64, currencyUnit configs.CurrencyUnit) string {
	return fmt.Sprintf("%d%s", amount, currencyUnit)
}

// FormatCurrencyNoUnit 格式化货币显示（不带单位）
// amount: 金额（整数）
// 返回格式化的货币字符串，如 "100", "50"
func FormatCurrencyNoUnit(amount int64) string {
	return fmt.Sprintf("%d", amount)
}

// FormatCurrencyFloat 格式化浮点数货币显示（带单位）
// amount: 金额（浮点数）
// currencyUnit: 货币单位
// precision: 小数位数（默认2位）
// 返回格式化的货币字符串，如 "100.50u", "50.123btc"
func FormatCurrencyFloat(amount float64, currencyUnit configs.CurrencyUnit, precision ...int) string {
	prec := 2 // 默认2位小数
	if len(precision) > 0 {
		prec = precision[0]
	}

	// 处理精度，避免浮点数精度问题
	multiplier := math.Pow(10, float64(prec))
	rounded := math.Round(amount*multiplier) / multiplier

	return fmt.Sprintf("%.*f%s", prec, rounded, currencyUnit)
}

// FormatCurrencyFloatNoUnit 格式化浮点数货币显示（不带单位）
// amount: 金额（浮点数）
// precision: 小数位数（默认2位）
// 返回格式化的货币字符串，如 "100.50", "50.123"
func FormatCurrencyFloatNoUnit(amount float64, precision ...int) string {
	prec := 2 // 默认2位小数
	if len(precision) > 0 {
		prec = precision[0]
	}

	// 处理精度，避免浮点数精度问题
	multiplier := math.Pow(10, float64(prec))
	rounded := math.Round(amount*multiplier) / multiplier

	return fmt.Sprintf("%.*f", prec, rounded)
}

// FormatCurrencyRange 格式化货币范围显示（带单位）
// minAmount, maxAmount: 最小和最大金额
// currencyUnit: 货币单位
// 返回格式化的货币范围字符串，如 "100-500u"
func FormatCurrencyRange(minAmount, maxAmount int64, currencyUnit configs.CurrencyUnit) string {
	return fmt.Sprintf("%d-%d%s", minAmount, maxAmount, currencyUnit)
}

// FormatCurrencyRangeNoUnit 格式化货币范围显示（不带单位）
// minAmount, maxAmount: 最小和最大金额
// 返回格式化的货币范围字符串，如 "100-500"
func FormatCurrencyRangeNoUnit(minAmount, maxAmount int64) string {
	return fmt.Sprintf("%d-%d", minAmount, maxAmount)
}
