package game

import (
	"fmt"
	"os"
	"strings"
	"time"

	"s2/modules/telegram-k3/configs"
	"s2/modules/telegram-k3/images"
	"s2/modules/telegram-k3/models"

	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/log"
)

// getUserDisplayName 获取用户显示名称
// 优先从缓存中获取用户名，如果没有则使用默认格式
func (gm *GameManager) getUserDisplayName(userID int64) string {
	// 尝试从缓存中获取用户名
	for username, id := range gm.UsernameCache {
		if id == userID {
			return username
		}
	}
	// 如果没有找到，返回默认格式
	return fmt.Sprintf("用户%d", userID)
}

// sendWithRetry 带重试机制的消息发送函数
func sendWithRetry(sendFunc func() (interface{}, error), maxRetries int) error {
	var err error
	for retry := 0; retry < maxRetries; retry++ {
		_, err = sendFunc()
		if err == nil {
			return nil
		}

		if retry < maxRetries-1 {
			log.Warnf("消息发送失败(第%d次重试): %v", retry+1, err)
			time.Sleep(configs.GetConfig().MessageRetryDelay)
		}
	}

	log.Errorf("消息发送最终失败，已重试%d次: %v", maxRetries, err)
	return err
}

// sendWithRetryConfig 使用配置的重试参数的消息发送函数
func sendWithRetryConfig(sendFunc func() (interface{}, error)) error {
	return sendWithRetry(sendFunc, configs.GetConfig().MessageRetryCount)
}

// 发送开始下注消息
func (gm *GameManager) sendBettingStartMessage(period *models.TelegramK3GamePeriod) {
	log.Infof("开始构建开始下注消息 - 群组ID: %d, 期数: %d", gm.ChatID, period.PeriodNum)

	bot := GetBot()
	if bot == nil {
		log.Errorf("bot not initialized")
		return
	}

	// 使用配置中的庄家信息，但余额使用内存余额
	// 获取当前庄家信息
	bankerBalance := gm.getUserBalance(gm.CurrentBanker, gm.CurrencyType)

	// 构建消息文本，余额带单位
	config := configs.GetConfig()

	// 获取庄家用户名
	bankerName := gm.getUserDisplayName(gm.CurrentBanker)

	text := fmt.Sprintf("期号: <code>%s</code>\n\n发包手ID: %s (%d) 庄\n\n🧧底注: %s 余额(%s)\n\n手摇快三文字下注格式为:\n\n组合: dd10 ds10 xd10 xs10 或 大单10 大双10 小单10 小双10\n\n高倍: bz1 10 bz1 10 或 豹子1 10 豹子2 10\n\n特码: 定位胆位置+数字，例如: 定位胆4 10, dwd4 10, 4y10",
		period.ID, bankerName, gm.CurrentBanker, FormatCurrency(config.BaseBet, gm.CurrencyUnit), FormatCurrencyFloat(bankerBalance, gm.CurrencyUnit))

	// 创建内联键盘按钮
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("充值提现", "recharge_withdraw"),
			tgbotapi.NewInlineKeyboardButtonData("钱包频道", "wallet_channel"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("钱包客服", "wallet_service"),
			tgbotapi.NewInlineKeyboardButtonData("XX担保", "xx_guarantee"),
		),
	)

	// 发送MP4作为动画
	videoPath := "statics/betting.mp4"
	log.Infof("开始检查MP4文件: %s", videoPath)

	if _, err := os.Stat(videoPath); err == nil {
		log.Infof("MP4文件存在，开始发送动画消息")
		// 将MP4作为动画发送，这样会自动播放
		animation := tgbotapi.NewAnimation(gm.ChatID, tgbotapi.FilePath(videoPath))
		animation.Caption = text
		animation.ParseMode = "HTML"
		animation.ReplyMarkup = keyboard

		_, err := bot.Send(animation)
		if err != nil {
			log.Infof("发送MP4动画消息失败: %v，回退到文本消息", err)
			// 如果MP4发送失败，回退到文本消息
			msg := tgbotapi.NewMessage(gm.ChatID, text)
			msg.ParseMode = "HTML"
			msg.ReplyMarkup = keyboard
			_, err = bot.Send(msg)
			if err != nil {
				log.Errorf("发送下注消息失败: %v - 群组ID: %d", err, gm.ChatID)
			}
		}
		log.Infof("下注消息发送成功 - 群组ID: %d", gm.ChatID)
	} else {
		// 如果MP4文件不存在，发送文本消息
		log.Infof("MP4文件不存在: %s，发送文本消息", videoPath)
		msg := tgbotapi.NewMessage(gm.ChatID, text)
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = keyboard
		_, err := bot.Send(msg)
		if err != nil {
			log.Errorf("发送下注消息失败: %v - 群组ID: %d", err, gm.ChatID)
		} else {
			log.Infof("下注消息发送成功 - 群组ID: %d", gm.ChatID)
		}
	}
}

// 发送停止下注消息
func (gm *GameManager) sendBettingEndMessage(period *models.TelegramK3GamePeriod) error {
	stats := gm.getBetStats(period.ID)
	bot := GetBot()
	if bot == nil {
		log.Errorf("bot not initialized")
		return fmt.Errorf("bot not initialized")
	}

	// 构建消息文本
	var text string
	if stats.TotalBets == 0 {
		// 无玩家投注情况
		text = fmt.Sprintf("----<code>%s</code>期下注玩家-----\n\n无人下注, 机器人将在3秒后摇骰子", period.ID)
	} else {
		// 有玩家投注情况
		text = fmt.Sprintf("----<code>%s</code>期下注玩家-----\n", period.ID)

		// 添加下注玩家信息，余额带单位
		for _, bet := range gm.BetRecords[period.ID] {
			text += fmt.Sprintf("ㅤ%s %d %s %s\n", bet.UserName, bet.UserID, configs.GetBetTypeDisplayName(bet.BetType), FormatCurrency(bet.Amount, gm.CurrencyUnit))
		}

		text += "———————————\n"
		text += "👉轻触【<code>🎲</code>】复制投掷。\n\n"

		// 获取下注最高的玩家
		maxBetUserID, maxBetUserName := gm.getMaxBetUser(period.ID)
		if maxBetUserID > 0 {
			text += fmt.Sprintf("请掷骰子玩家：<a href=\"tg://user?id=%d\">@%s</a>  (总投注：%d)\n\n", maxBetUserID, maxBetUserName, stats.BetByUser[maxBetUserID])
		} else {
			text += fmt.Sprintf("请掷骰子玩家：@Gy1166  (总投注%d)\n\n", stats.TotalAmount)
		}

		// 计算掷骰子截止时间
		rollingDuration := configs.GetConfig().RollingDuration
		deadline := time.Now().Add(rollingDuration)
		deadlineStr := deadline.Format("15:04:05")

		text += fmt.Sprintf("%d秒内 %s之前 掷出3颗骰子，超时机器补发，无争议", int(rollingDuration.Seconds()), deadlineStr)
	}

	// 创建内联键盘按钮
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("充值提现", "recharge_withdraw"),
		),
	)

	// 发送MP4作为动画
	videoPath := "statics/rolling.mp4"
	log.Infof("开始检查停止下注MP4文件: %s", videoPath)

	if _, err := os.Stat(videoPath); err == nil {
		log.Infof("停止下注MP4文件存在，开始发送动画消息")
		// 将MP4作为动画发送，这样会自动播放
		animation := tgbotapi.NewAnimation(gm.ChatID, tgbotapi.FilePath(videoPath))
		animation.Caption = text
		animation.ParseMode = "HTML"
		animation.ReplyMarkup = keyboard

		err := sendWithRetryConfig(func() (interface{}, error) {
			return bot.Send(animation)
		})
		if err != nil {
			log.Infof("发送停止下注MP4动画消息失败: %v，回退到文本消息", err)
			// 如果MP4发送失败，回退到文本消息
			msg := tgbotapi.NewMessage(gm.ChatID, text)
			msg.ParseMode = "HTML"
			msg.ReplyMarkup = keyboard
			err = sendWithRetryConfig(func() (interface{}, error) {
				return bot.Send(msg)
			})
			if err != nil {
				log.Errorf("发送停止下注消息失败: %v", err)
				return err
			}
		}
		log.Infof("停止下注MP4动画消息发送成功")
		return nil
	} else {
		// 如果MP4文件不存在，发送文本消息
		log.Infof("停止下注MP4文件不存在: %s，发送文本消息", videoPath)
		msg := tgbotapi.NewMessage(gm.ChatID, text)
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = keyboard
		err := sendWithRetryConfig(func() (interface{}, error) {
			return bot.Send(msg)
		})
		if err != nil {
			log.Errorf("发送停止下注消息失败: %v", err)
			return err
		}
		return nil
	}
}

// 发送机器人掷骰子消息
func (gm *GameManager) sendBotRollMessage(period *models.TelegramK3GamePeriod, action string) {
	bot := GetBot()
	if bot == nil {
		log.Errorf("bot not initialized")
		return
	}

	msg := tgbotapi.NewMessage(gm.ChatID, action)
	_, err := bot.Send(msg)
	if err != nil {
		log.Errorf("发送机器人掷骰子消息失败: %v", err)
	}
}

// 发送开奖结算消息
func (gm *GameManager) sendSettlementMessage(period *models.TelegramK3GamePeriod, diceResult *models.TelegramK3DiceResult) {
	// 构建结算文本内容
	var caption strings.Builder

	// 添加开奖结果信息
	caption.WriteString(fmt.Sprintf("❤️ 第<code>%s</code>期输赢 ❤️\n\n", period.ID))
	caption.WriteString(fmt.Sprintf("骰子为: %d+%d+%d=%d\n\n", diceResult.Dice1, diceResult.Dice2, diceResult.Dice3, diceResult.Sum))

	// 开奖结果
	caption.WriteString(fmt.Sprintf("🏆 开奖结果: %s\n\n", configs.GetBetTypeDisplayName(diceResult.Result)))

	// 结算统计
	settlementStats := gm.getSettlementStats(period.ID)
	if settlementStats.WinCount == 0 && settlementStats.LoseCount == 0 {
		caption.WriteString("流局\n")
	} else {
		// 显示闲家信息
		if len(settlementStats.WinByUser) > 0 {
			caption.WriteString("闲家:\n")
			for userID, winAmount := range settlementStats.WinByUser {
				// 查找该用户的下注记录以获取下注类型和金额
				var betType string
				var betAmount int64
				for _, bet := range gm.BetRecords[period.ID] {
					if bet.UserID == userID && bet.IsWin {
						betType = bet.BetType
						betAmount = bet.Amount
						break
					}
				}

				// 计算余额（这里需要根据实际情况获取用户余额）
				// 使用实际余额（考虑下注扣款和赔付）
				balance := gm.calculateUserActualBalance(userID, period.ID)

				caption.WriteString(fmt.Sprintf("%s[%d] %s %d,赢 %.2f余额%.2f\n",
					settlementStats.UserNames[userID], userID,
					configs.GetBetTypeDisplayName(betType), betAmount,
					float64(winAmount), balance))
			}
		}

		// 显示庄家信息
		// 使用当前庄家余额
		bankerBalance := gm.getUserBalance(gm.CurrentBanker, gm.CurrencyType)

		caption.WriteString(fmt.Sprintf("\n庄家(ID:%d) 赔付-%s 余额:%s\n",
			gm.CurrentBanker,
			FormatCurrencyFloat(float64(settlementStats.TotalWinAmount), gm.CurrencyUnit),
			FormatCurrencyFloat(bankerBalance, gm.CurrencyUnit)))
	}

	// 生成开奖网格图片
	if gm.ImageGen != nil {
		grid := gm.generateDiceGridWithCurrent(period, diceResult)
		statsNumbers := configs.GetConfig().StatsNumbers

		imageFilename, err := gm.ImageGen.(*images.ImageGenerator).GenerateDiceGridImage(grid, statsNumbers)
		if err != nil {
			log.Errorf("生成开奖网格图片失败: %v - 群组ID: %d", err, gm.ChatID)
			// 如果图片生成失败，回退到文本模式
			msg := tgbotapi.NewMessage(gm.ChatID, caption.String())
			msg.ParseMode = "HTML"

			err := sendWithRetryConfig(func() (interface{}, error) {
				return GetBot().Send(msg)
			})
			if err != nil {
				log.Errorf("发送开奖文本消息失败: %v - 群组ID: %d", err, gm.ChatID)
			}
			return
		}

		// 发送图片和文本
		photo := tgbotapi.NewPhoto(gm.ChatID, tgbotapi.FilePath(imageFilename))
		photo.Caption = caption.String()
		photo.ParseMode = "HTML"

		err = sendWithRetryConfig(func() (interface{}, error) {
			return GetBot().Send(photo)
		})
		if err != nil {
			log.Errorf("发送开奖图片消息失败: %v - 群组ID: %d", err, gm.ChatID)
		}
	} else {
		// 如果图片生成器未初始化，发送文本消息
		msg := tgbotapi.NewMessage(gm.ChatID, caption.String())
		msg.ParseMode = "HTML"

		err := sendWithRetryConfig(func() (interface{}, error) {
			return GetBot().Send(msg)
		})
		if err != nil {
			log.Errorf("发送开奖文本消息失败: %v - 群组ID: %d", err, gm.ChatID)
		}
	}
}

// 发送新的下注成功消息格式
func (gm *GameManager) sendBetSuccessMessage(chatID, userID int64, userName string, successBets []string, messageID int) {
	var text strings.Builder

	// 第一行：下注内容
	text.WriteString(strings.Join(successBets, " "))
	text.WriteString(" 下注成功\n")

	// 分隔线
	text.WriteString("------------\n")

	// 用户信息行
	text.WriteString(fmt.Sprintf("%s   id:%d\n", userName, userID))

	// 分隔线
	text.WriteString("------------\n")

	// 余额信息（这里需要从用户系统获取，暂时使用模拟数据）
	// 使用实际余额（考虑下注扣款）
	periodID := ""
	if gm.CurrentGame != nil && gm.CurrentGame.Period != nil {
		periodID = gm.CurrentGame.Period.ID
	}
	balance := gm.calculateUserActualBalance(userID, periodID)
	frozenAmount := gm.getUserFrozenAmount(userID, periodID)
	text.WriteString(fmt.Sprintf("余额: %.2f (冻结: %d)", balance, frozenAmount))

	msg := tgbotapi.NewMessage(chatID, text.String())
	msg.ParseMode = "HTML"
	msg.ReplyToMessageID = messageID

	_, err := GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送新下注成功消息失败: %v", err)
	}
}

// 发送游戏状态消息
func (gm *GameManager) sendGameStatusMessage(chatID int64) {
	if gm.CurrentGame == nil {
		msg := tgbotapi.NewMessage(chatID, "🎲 当前没有进行中的游戏")
		GetBot().Send(msg)
		return
	}

	period := gm.CurrentGame.Period
	stats := gm.getBetStats(period.ID)

	var text strings.Builder

	text.WriteString("🎲 游戏状态\n")
	text.WriteString(fmt.Sprintf("期号: %s\n", period.ID))
	text.WriteString(fmt.Sprintf("状态: %s\n", getStateDisplayName(gm.CurrentGame.CurrentState)))

	remainingTime := time.Until(period.EndTime)
	if remainingTime > 0 {
		text.WriteString(fmt.Sprintf("剩余时间: %s\n", formatDuration(remainingTime)))
	}

	text.WriteString(fmt.Sprintf("下注人数: %d\n", len(stats.UserNames)))
	text.WriteString(fmt.Sprintf("总下注金额: %d", stats.TotalAmount))

	msg := tgbotapi.NewMessage(chatID, text.String())
	msg.ParseMode = "HTML"

	_, err := GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送游戏状态消息失败: %v", err)
	}
}

// 获取状态显示名称
func getStateDisplayName(state string) string {
	switch state {
	case GameStateWaiting:
		return "等待开始"
	case GameStateBetting:
		return "下注中"
	case GameStateRolling:
		return "掷骰子"
	case GameStateSettling:
		return "结算中"
	default:
		return state
	}
}

// 格式化时间
func formatDuration(d time.Duration) string {
	if d < 0 {
		return "已结束"
	}

	minutes := int(d.Minutes())
	seconds := int(d.Seconds()) % 60

	if minutes > 0 {
		return fmt.Sprintf("%d分%d秒", minutes, seconds)
	}
	return fmt.Sprintf("%d秒", seconds)
}
