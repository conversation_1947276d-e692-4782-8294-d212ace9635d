package game

import (
	"s2/modules/telegram-k3/configs"
	"s2/modules/telegram-k3/models"
	"time"
)

// 游戏状态常量
const (
	GameStateWaiting  = "waiting"  // 等待开始
	GameStateBetting  = "betting"  // 下注中
	GameStateRolling  = "rolling"  // 掷骰子
	GameStateSettling = "settling" // 结算中
)

// 下注类型常量
const (
	// 大小单双
	BetTypeBig   = "big"   // 大 (11-18)
	BetTypeSmall = "small" // 小 (3-10)
	BetTypeOdd   = "odd"   // 单
	BetTypeEven  = "even"  // 双

	// 组合
	BetTypeBigOdd    = "big_odd"    // 大单
	BetTypeSmallOdd  = "small_odd"  // 小单
	BetTypeBigEven   = "big_even"   // 大双
	BetTypeSmallEven = "small_even" // 小双

	// 特殊
	BetTypeLeopard  = "leopard"  // 豹子 (三个相同数字)
	BetTypeStraight = "straight" // 顺子 (三个连续数字)
	BetTypePair     = "pair"     // 对子 (两个相同数字)

	// 指定豹子
	BetTypeLeopard1 = "leopard_1" // 豹子1
	BetTypeLeopard2 = "leopard_2" // 豹子2
	BetTypeLeopard3 = "leopard_3" // 豹子3
	BetTypeLeopard4 = "leopard_4" // 豹子4
	BetTypeLeopard5 = "leopard_5" // 豹子5
	BetTypeLeopard6 = "leopard_6" // 豹子6
)

// 赔率配置 - 已移动到configs包中
// 使用configs.GetBetOdds()获取赔率

// 游戏状态机
type GameStateMachine struct {
	CurrentState string
	Period       *models.TelegramK3GamePeriod
	Timer        *time.Timer
	StopChan     chan bool
}

// 游戏管理器
type GameManager struct {
	ChatID           int64
	CurrentGame      *GameStateMachine
	BetRecords       map[string][]models.TelegramK3BetRecord // periodID -> bets
	DiceResults      map[string]*models.TelegramK3DiceResult // periodID -> result
	PeriodCount      int                                     // 当前期数
	DiceRollCount    int                                     // 当前期掷骰子次数
	DiceValues       []int                                   // 当前期已掷的骰子值
	ImageGen         interface{}                             // 图片生成器接口
	UserBalances     map[int64]float64                       // 用户内存余额（仅程序运行时有效）
	CurrencyType     configs.CurrencyType                    // 当前货币类型，默认：ustd
	CurrencyUnit     configs.CurrencyUnit                    // 货币单位，默认：u
	CurrentBanker    int64                                   // 当前庄家ID
	NextBanker       int64                                   // 下一期庄家ID（用于抢庄和下庄）
	BankerMode       bool                                    // 是否处于抢庄模式
	BankerCandidates map[int64]string                        // 抢庄候选人 map[userID]userName
	UsernameCache    map[string]int64                        // 用户名到用户ID的缓存 map[username]userID
	PeriodPrefix     string                                  // 期号前缀，格式：K3+数字，例如：K36537661
}

// 下注统计
type BetStats struct {
	TotalBets    int64            `json:"total_bets"`
	TotalAmount  int64            `json:"total_amount"`
	BetByType    map[string]int64 `json:"bet_by_type"`
	BetByUser    map[int64]int64  `json:"bet_by_user"`
	UserNames    map[int64]string `json:"user_names"`
	MaxBetUser   int64            `json:"max_bet_user"`
	MaxBetAmount int64            `json:"max_bet_amount"`
}

// 结算统计
type SettlementStats struct {
	TotalWinAmount int64            `json:"total_win_amount"`
	WinByUser      map[int64]int64  `json:"win_by_user"`
	UserNames      map[int64]string `json:"user_names"`
	WinCount       int              `json:"win_count"`
	LoseCount      int              `json:"lose_count"`
}
