package game

import (
	"fmt"
	"s2/modules/telegram-k3/configs"
	"s2/modules/telegram-k3/images"
	"s2/modules/telegram-k3/models"
	"sort"
	"strconv"
	"strings"
	"time"

	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
)

// 全局游戏管理器
var gameManagers = make(map[int64]*GameManager)

// 初始化数据库表
func InitDB() {
	db := mdb.Default()
	db.AutoMigrate(&models.TelegramK3GamePeriod{}, &models.TelegramK3BetRecord{}, &models.TelegramK3DiceResult{})
}

// 获取或创建游戏管理器
func GetGameManager(chatID int64) *GameManager {
	if manager, exists := gameManagers[chatID]; exists {
		return manager
	}

	// 初始化图片生成器
	imageGen, err := images.NewImageGenerator()
	if err != nil {
		log.Errorf("初始化图片生成器失败: %v - 群组ID: %d", err, chatID)
		// 即使图片生成器初始化失败，也继续创建游戏管理器
		imageGen = nil
	}

	manager := &GameManager{
		ChatID:           chatID,
		BetRecords:       make(map[string][]models.TelegramK3BetRecord),
		DiceResults:      make(map[string]*models.TelegramK3DiceResult),
		PeriodCount:      0,
		ImageGen:         imageGen,
		UserBalances:     make(map[int64]float64), // 初始化内存余额表
		CurrencyType:     configs.CurrencyUSTD,    // 默认货币类型
		CurrencyUnit:     configs.UnitU,           // 默认货币单位
		CurrentBanker:    0,                       // 当前庄家ID（0表示未设置）
		NextBanker:       0,                       // 下一期庄家ID
		BankerMode:       false,                   // 抢庄模式
		BankerCandidates: make(map[int64]string),  // 抢庄候选人
		UsernameCache:    make(map[string]int64),  // 用户名缓存
		PeriodPrefix:     "K300000000",            // 默认期号前缀（K3+默认数字）
	}

	// 从数据库中读取最新的期号记录，初始化期号前缀和期数
	manager.initializeFromDatabase()

	gameManagers[chatID] = manager
	return manager
}

// 生成期号
func generatePeriodID(periodPrefix string, periodNum int) string {
	// 格式：K3+数字+8位期数
	// 例如：K3653766100000001
	// periodPrefix 已经是完整的 K3+数字 格式
	return fmt.Sprintf("%s%08d", periodPrefix, periodNum)
}

// 解析期号，提取前缀和期数
func parsePeriodID(periodID string) (string, int, error) {
	// 期号格式：K3+7位数字+8位期数
	// 例如：K3378578600000001
	if len(periodID) < 16 {
		return "", 0, fmt.Errorf("期号格式错误: %s", periodID)
	}

	// 提取前缀：K3+7位数字
	prefix := periodID[:10]

	// 提取期数：最后8位
	periodNumStr := periodID[10:]
	periodNum, err := strconv.Atoi(periodNumStr)
	if err != nil {
		return "", 0, fmt.Errorf("期号期数解析错误: %s", periodNumStr)
	}

	return prefix, periodNum, nil
}

// initializeFromDatabase 从数据库初始化期号前缀和期数
func (gm *GameManager) initializeFromDatabase() {
	db := mdb.Default()

	// 查询该群组最新的期号记录
	var latestPeriod models.TelegramK3GamePeriod
	err := db.Where("chat_id = ?", gm.ChatID).
		Order("created_at DESC").
		First(&latestPeriod).Error

	if err != nil {
		// 如果没有记录，使用默认值
		log.Infof("群组 %d 没有期号记录，使用默认值 - 期号前缀: %s, 期数: 0", gm.ChatID, gm.PeriodPrefix)
		return
	}

	// 解析期号，提取前缀和期数
	prefix, periodNum, err := parsePeriodID(latestPeriod.ID)
	if err != nil {
		log.Warnf("解析期号失败: %v，使用默认值 - 群组ID: %d", err, gm.ChatID)
		return
	}

	// 更新期号前缀和期数
	gm.PeriodPrefix = prefix
	gm.PeriodCount = periodNum

	log.Infof("从数据库初始化成功 - 群组ID: %d, 期号前缀: %s, 当前期数: %d", gm.ChatID, gm.PeriodPrefix, gm.PeriodCount)
}

// 开始新游戏
func (gm *GameManager) StartNewGame() {
	log.Infof("开始新游戏 - 群组ID: %d", gm.ChatID)

	// 检查是否有庄家
	if gm.CurrentBanker == 0 {
		log.Errorf("没有设置庄家，无法开始游戏 - 群组ID: %d", gm.ChatID)
		return
	}

	// 检查并停止当前游戏
	if gm.CurrentGame != nil {
		currentState := gm.CurrentGame.CurrentState
		log.Infof("检测到当前游戏状态: %s，准备停止 - 群组ID: %d", currentState, gm.ChatID)

		// 如果游戏正在进行中，先停止
		if currentState == GameStateBetting || currentState == GameStateRolling || currentState == GameStateSettling {
			log.Infof("停止当前进行中的游戏 - 群组ID: %d", gm.ChatID)
			gm.CurrentGame.Stop()

			// 等待一小段时间确保游戏完全停止
			time.Sleep(100 * time.Millisecond)
		}
	}

	// 检查数据库中最后一期的状态
	lastPeriod := gm.checkLastPeriodStatus()
	if lastPeriod != nil {
		// 如果最后一期是未完成状态，直接加载到内存中继续
		log.Infof("发现未完成的最后一期，加载到内存继续: %s (状态: %s) - 群组ID: %d",
			lastPeriod.ID, lastPeriod.State, gm.ChatID)
		gm.loadPeriodToMemory(lastPeriod)
		return
	}

	// 没有未完成的期号，创建新期号
	gm.createNewPeriod()
}

// 将期号加载到内存中，按照正常游戏流程继续
func (gm *GameManager) loadPeriodToMemory(period *models.TelegramK3GamePeriod) {
	log.Infof("将期号加载到内存: %s (状态: %s) - 群组ID: %d", period.ID, period.State, gm.ChatID)

	// 加载该期的下注记录
	gm.loadBetRecords(period.ID)

	// 加载该期的骰子结果（如果有）
	gm.loadDiceResult(period.ID)

	// 创建状态机
	stateMachine := &GameStateMachine{
		CurrentState: period.State,
		Period:       period,
		StopChan:     make(chan bool),
	}

	gm.CurrentGame = stateMachine
	log.Infof("[状态转换] 加载期号到内存，状态: %s - 群组ID: %d", stateMachine.CurrentState, gm.ChatID)

	// 根据状态按照正常游戏流程继续
	switch period.State {
	case GameStateBetting:
		gm.resumeBettingPeriod(period)
	case GameStateRolling:
		gm.resumeRollingPeriod(period)
	case GameStateSettling:
		gm.resumeSettlingPeriod(period)
	default:
		log.Errorf("未知的游戏状态: %s - 群组ID: %d", period.State, gm.ChatID)
	}
}

// 恢复下注阶段（按照正常游戏流程）
func (gm *GameManager) resumeBettingPeriod(period *models.TelegramK3GamePeriod) {
	log.Infof("恢复下注阶段: %s - 群组ID: %d", period.ID, gm.ChatID)

	// 检查下注时间是否已过
	if time.Now().After(period.EndTime) {
		log.Infof("下注时间已过，直接转换到掷骰子阶段 - 群组ID: %d", gm.ChatID)
		gm.transitionToRolling()
		return
	}

	// 计算剩余下注时间
	remainingTime := period.EndTime.Sub(time.Now())

	// 发送继续下注消息
	gm.sendBettingStartMessage(period)

	// 设置定时器
	gm.CurrentGame.Timer = time.AfterFunc(remainingTime, func() {
		log.Infof("下注时间结束，准备转换到掷骰子阶段 - 群组ID: %d", gm.ChatID)
		gm.transitionToRolling()
	})
	log.Infof("恢复下注阶段定时器设置成功，剩余时间: %v - 群组ID: %d", remainingTime, gm.ChatID)
}

// 恢复掷骰子阶段（按照正常游戏流程）
func (gm *GameManager) resumeRollingPeriod(period *models.TelegramK3GamePeriod) {
	log.Infof("恢复掷骰子阶段: %s - 群组ID: %d", period.ID, gm.ChatID)

	// 检查是否有骰子结果
	diceResult := gm.DiceResults[period.ID]
	if diceResult != nil {
		log.Infof("已有骰子结果，直接转换到结算阶段 - 群组ID: %d", gm.ChatID)
		gm.transitionToSettling()
		return
	}

	// 检查掷骰子时间是否已过
	rollingEndTime := period.EndTime.Add(configs.GetConfig().RollingDuration)
	if time.Now().After(rollingEndTime) {
		log.Infof("掷骰子时间已过，机器人代掷 - 群组ID: %d", gm.ChatID)
		// 机器人立即掷骰子
		diceResult = gm.rollDiceByBot(period)
		if diceResult != nil {
			gm.transitionToSettling()
		}
		return
	}

	// 计算剩余掷骰子时间
	remainingTime := rollingEndTime.Sub(time.Now())

	// 发送掷骰子消息
	gm.sendBotRollMessage(period, "🎲")

	// 设置定时器
	gm.CurrentGame.Timer = time.AfterFunc(remainingTime, func() {
		log.Infof("掷骰子时间结束，机器人代掷 - 群组ID: %d", gm.ChatID)
		diceResult := gm.rollDiceByBot(period)
		if diceResult != nil {
			gm.transitionToSettling()
		}
	})
	log.Infof("恢复掷骰子阶段定时器设置成功，剩余时间: %v - 群组ID: %d", remainingTime, gm.ChatID)
}

// 恢复结算阶段（按照正常游戏流程）
func (gm *GameManager) resumeSettlingPeriod(period *models.TelegramK3GamePeriod) {
	log.Infof("恢复结算阶段: %s - 群组ID: %d", period.ID, gm.ChatID)

	// 检查是否有骰子结果
	diceResult := gm.DiceResults[period.ID]
	if diceResult == nil {
		log.Errorf("结算阶段但没有骰子结果 - 群组ID: %d", gm.ChatID)
		return
	}

	// 立即完成结算（按照正常流程，会自动开始下一局）
	log.Infof("立即完成结算 - 群组ID: %d", gm.ChatID)
	gm.settleAndShowResult(period)
}

// 检查数据库中最后一期的状态
func (gm *GameManager) checkLastPeriodStatus() *models.TelegramK3GamePeriod {
	db := mdb.Default()
	var lastPeriod models.TelegramK3GamePeriod

	err := db.Where("chat_id = ?", gm.ChatID).
		Order("created_at DESC").
		First(&lastPeriod).Error

	if err != nil {
		// 没有记录，返回nil
		return nil
	}

	// 检查是否是未完成状态
	if lastPeriod.State == GameStateBetting ||
		lastPeriod.State == GameStateRolling ||
		lastPeriod.State == GameStateSettling {
		return &lastPeriod
	}

	// 已完成状态，返回nil
	return nil
}

// 创建新期号
func (gm *GameManager) createNewPeriod() {
	// 增加期数
	gm.PeriodCount++
	if gm.PeriodCount > configs.GetConfig().MaxPeriods {
		gm.PeriodCount = 1 // 重置期数
	}
	log.Infof("当前期数: %d - 群组ID: %d", gm.PeriodCount, gm.ChatID)

	// 创建新期号
	periodID := generatePeriodID(gm.PeriodPrefix, gm.PeriodCount)
	period := &models.TelegramK3GamePeriod{
		ID:        periodID,
		ChatID:    gm.ChatID,
		StartTime: time.Now(),
		EndTime:   time.Now().Add(configs.GetConfig().BettingDuration),
		State:     GameStateBetting,
		PeriodNum: gm.PeriodCount,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	log.Infof("创建期号成功: %s - 群组ID: %d", periodID, gm.ChatID)

	// 保存到数据库
	db := mdb.Default()
	err := db.Create(period).Error
	if err != nil {
		log.Errorf("保存期号到数据库失败: %v - 群组ID: %d", err, gm.ChatID)
		return
	}
	log.Infof("期号保存到数据库成功 - 群组ID: %d", gm.ChatID)

	// 创建状态机
	stateMachine := &GameStateMachine{
		CurrentState: GameStateBetting,
		Period:       period,
		StopChan:     make(chan bool),
	}

	gm.CurrentGame = stateMachine
	log.Infof("[状态转换] 创建新游戏，初始状态: %s - 群组ID: %d", stateMachine.CurrentState, gm.ChatID)

	// 发送开始下注消息
	log.Infof("准备发送开始下注消息 - 群组ID: %d", gm.ChatID)
	gm.sendBettingStartMessage(period)

	// 设置定时器
	stateMachine.Timer = time.AfterFunc(configs.GetConfig().BettingDuration, func() {
		log.Infof("下注时间结束，准备转换到掷骰子阶段 - 群组ID: %d", gm.ChatID)
		gm.transitionToRolling()
	})
	log.Infof("定时器设置成功，下注时间: %v - 群组ID: %d", configs.GetConfig().BettingDuration, gm.ChatID)
}

// 加载下注记录
func (gm *GameManager) loadBetRecords(periodID string) {
	db := mdb.Default()
	var betRecords []models.TelegramK3BetRecord

	err := db.Where("period_id = ?", periodID).Find(&betRecords).Error
	if err != nil {
		log.Errorf("加载下注记录失败: %v - 群组ID: %d", err, gm.ChatID)
		return
	}

	gm.BetRecords[periodID] = betRecords
	log.Infof("加载下注记录成功: %d 条 - 群组ID: %d", len(betRecords), gm.ChatID)
}

// 加载骰子结果
func (gm *GameManager) loadDiceResult(periodID string) {
	db := mdb.Default()
	var diceResult models.TelegramK3DiceResult

	err := db.Where("period_id = ?", periodID).First(&diceResult).Error
	if err != nil {
		// 没有骰子结果是正常的（比如还在下注阶段）
		return
	}

	gm.DiceResults[periodID] = &diceResult
	log.Infof("加载骰子结果成功: %d+%d+%d=%d - 群组ID: %d",
		diceResult.Dice1, diceResult.Dice2, diceResult.Dice3, diceResult.Sum, gm.ChatID)
}

// 状态转换：下注 -> 掷骰子
func (gm *GameManager) transitionToRolling() {
	log.Infof("开始转换到掷骰子阶段 - 群组ID: %d", gm.ChatID)

	if gm.CurrentGame == nil {
		log.Errorf("当前游戏为nil，无法转换到掷骰子阶段 - 群组ID: %d", gm.ChatID)
		return
	}

	stateMachine := gm.CurrentGame
	period := stateMachine.Period
	log.Infof("获取期号: %s - 群组ID: %d", period.ID, gm.ChatID)

	// 重置掷骰子计数器和骰子值
	gm.DiceRollCount = 0
	gm.DiceValues = nil
	log.Infof("重置掷骰子计数器: %d, 清空骰子值 - 群组ID: %d", gm.DiceRollCount, gm.ChatID)

	// 更新状态
	period.State = GameStateRolling
	period.UpdatedAt = time.Now()
	stateMachine.CurrentState = GameStateRolling // 同时更新状态机的当前状态
	log.Infof("[状态转换] 下注阶段 -> 掷骰子阶段 - 群组ID: %d", gm.ChatID)

	db := mdb.Default()
	err := db.Save(period).Error
	if err != nil {
		log.Errorf("保存期号状态失败: %v - 群组ID: %d", err, gm.ChatID)
		return
	}
	log.Infof("期号状态保存成功 - 群组ID: %d", gm.ChatID)

	// 发送停止下注消息
	log.Infof("准备发送停止下注消息 - 群组ID: %d", gm.ChatID)
	err = gm.sendBettingEndMessage(period)
	if err != nil {
		log.Errorf("发送停止下注消息失败: %v - 群组ID: %d", err, gm.ChatID)
		// 如果消息发送失败，延迟重试
		time.Sleep(configs.GetConfig().MessageRetryDelay)
		err = gm.sendBettingEndMessage(period)
		if err != nil {
			log.Errorf("重试发送停止下注消息仍然失败: %v - 群组ID: %d", err, gm.ChatID)
		}
	}
	log.Infof("停止下注消息发送完成 - 群组ID: %d", gm.ChatID)

	// 检查是否有玩家下注
	stats := gm.getBetStats(period.ID)
	if stats.TotalBets == 0 {
		// 无人下注，机器人3秒后开始掷骰子
		log.Infof("无人下注，机器人将在3秒后开始掷骰子 - 群组ID: %d", gm.ChatID)
		gm.sendBotRollMessage(period, "未检测到用户骰子，将由系统发送")
		// 延迟后机器人代掷
		go func() {
			time.Sleep(configs.GetConfig().BotRollDelay)
			log.Infof("3秒延迟结束，机器人开始代掷 - 群组ID: %d", gm.ChatID)
			gm.rollDiceByBot(period)
		}()
	} else {
		// 有玩家下注，设置掷骰子定时器
		stateMachine.Timer = time.AfterFunc(configs.GetConfig().RollingDuration, func() {
			log.Infof("掷骰子时间结束，准备转换到结算阶段 - 群组ID: %d", gm.ChatID)
			gm.transitionToSettling()
		})
		log.Infof("掷骰子定时器设置成功，时间: %v - 群组ID: %d", configs.GetConfig().RollingDuration, gm.ChatID)
	}
}

// 状态转换：掷骰子 -> 结算
func (gm *GameManager) transitionToSettling() {
	if gm.CurrentGame == nil {
		return
	}

	stateMachine := gm.CurrentGame
	period := stateMachine.Period

	// 更新状态
	period.State = GameStateSettling
	period.UpdatedAt = time.Now()
	stateMachine.CurrentState = GameStateSettling // 同时更新状态机的当前状态

	db := mdb.Default()
	db.Save(period)

	log.Infof("[状态转换] 掷骰子阶段 -> 结算阶段 - 群组ID: %d, 掷骰子次数: %d", gm.ChatID, gm.DiceRollCount)

	// 检查掷骰子次数
	if gm.DiceRollCount == 0 {
		// 没有玩家掷骰子，机器人应该已经在掷骰子阶段掷过了
		log.Infof("没有玩家掷骰子，但机器人应该已经在掷骰子阶段掷过了 - 群组ID: %d", gm.ChatID)
		// 如果机器人还没有掷骰子，立即掷骰子
		if _, exists := gm.DiceResults[period.ID]; !exists {
			log.Infof("机器人还没有掷骰子，立即代掷 - 群组ID: %d", gm.ChatID)
			gm.sendBotRollMessage(period, "未检测到用户骰子，将由系统发送")
			gm.rollDiceByBot(period)
		}
	} else if gm.DiceRollCount < configs.GetConfig().MaxDiceRolls {
		// 掷骰子次数不足，机器人补掷
		remaining := configs.GetConfig().MaxDiceRolls - gm.DiceRollCount
		log.Infof("掷骰子次数不足，机器人补掷 %d 次 - 群组ID: %d", remaining, gm.ChatID)
		gm.sendBotRollMessage(period, fmt.Sprintf("检测到用户骰子数不够，将由系统发送补发%d次", remaining))
		gm.rollDiceByBot(period)
	} else {
		// 掷骰子次数已满3次，检查是否已经有骰子结果
		log.Infof("掷骰子次数已满3次，检查骰子结果 - 群组ID: %d", gm.ChatID)
		if _, exists := gm.DiceResults[period.ID]; exists {
			// 已经有骰子结果，说明玩家已经掷完骰子，结算应该已经触发
			log.Infof("已有骰子结果，结算应该已经触发，跳过重复结算 - 群组ID: %d", gm.ChatID)
		} else {
			// 如果没有骰子结果，机器人代掷
			log.Infof("没有骰子结果，机器人代掷 - 群组ID: %d", gm.ChatID)
			gm.sendBotRollMessage(period, "未检测到用户骰子，将由系统发送")
			gm.rollDiceByBot(period)
		}
	}
}

// 结束游戏
func (gm *GameManager) endGame() {
	if gm.CurrentGame == nil {
		return
	}

	stateMachine := gm.CurrentGame
	period := stateMachine.Period

	// 更新状态
	period.State = GameStateWaiting
	period.UpdatedAt = time.Now()
	stateMachine.CurrentState = GameStateWaiting // 同时更新状态机的当前状态

	db := mdb.Default()
	db.Save(period)

	// 发送游戏结束消息
	// gm.sendGameEndMessage(period)

	// 清理当前游戏
	gm.CurrentGame = nil

	// 自动开始下一局
	log.Infof("本期结束，%v后开始下一局 - 群组ID: %d", configs.GetConfig().NextGameDelay, gm.ChatID)
	time.AfterFunc(configs.GetConfig().NextGameDelay, func() {
		log.Infof("开始下一局 - 群组ID: %d", gm.ChatID)
		gm.StartNewGame()
	})
}

// 处理下注
func (gm *GameManager) HandleBet(userID int64, userName, betType string, amount int64) error {
	// 注意：状态检查已经在 HandleBetCommand 中进行，这里不再重复检查
	// 如果直接调用此方法，请确保已经进行了状态检查

	// 验证下注类型
	if configs.GetBetOdds(betType) == 0.0 {
		return fmt.Errorf("无效的下注类型: %s", betType)
	}

	// 验证金额
	if amount <= 0 {
		return fmt.Errorf("下注金额必须大于0")
	}

	// 验证限红
	limit := configs.GetBetLimit(betType)
	if limit > 0 && amount > limit {
		return fmt.Errorf("下注金额超过限额: %d (限额: %d)", amount, limit)
	}

	period := gm.CurrentGame.Period

	// 创建下注记录
	bet := models.TelegramK3BetRecord{
		PeriodID:  period.ID,
		ChatID:    gm.ChatID,
		UserID:    userID,
		UserName:  userName,
		BetType:   betType,
		Amount:    amount,
		Odds:      configs.GetBetOdds(betType),
		CreatedAt: time.Now(),
	}

	// 保存到数据库
	db := mdb.Default()
	db.Create(&bet)

	// 添加到内存
	gm.BetRecords[period.ID] = append(gm.BetRecords[period.ID], bet)

	log.Infof("用户 %s (ID: %d) 下注 %s %d", userName, userID, betType, amount)

	return nil
}

// 内部下注方法，用于在已经验证状态后调用
func (gm *GameManager) handleBetInternal(userID int64, userName, betType string, amount int64) error {
	// 调用完整的下注处理流程，包含所有必要的检查
	return gm.processBetWithBalanceCheck(userID, userName, betType, amount)
}

// 生成骰子开奖网格
func (gm *GameManager) generateDiceGrid() *images.DiceGrid {
	// 获取配置的网格大小
	width := configs.GetConfig().GridWidth
	height := configs.GetConfig().GridHeight

	// 初始化动态大小的网格
	grid := &images.DiceGrid{
		Numbers: make([][]int, height),
		Colors:  make([][]string, height),
		Stats:   make(map[string]int),
		Width:   width,
		Height:  height,
	}

	// 初始化二维数组
	for i := 0; i < height; i++ {
		grid.Numbers[i] = make([]int, width)
		grid.Colors[i] = make([]string, width)
	}

	// 获取最近的骰子结果（根据网格大小）
	totalSlots := width * height
	db := mdb.Default()
	var results []models.TelegramK3DiceResult
	err := db.Where("chat_id = ?", gm.ChatID).
		Order("created_at DESC").
		Limit(totalSlots).
		Find(&results).Error

	if err != nil {
		log.Errorf("获取骰子结果失败: %v - 群组ID: %d", err, gm.ChatID)
		return grid
	}

	// 过滤和验证数据
	var validResults []models.TelegramK3DiceResult
	for _, result := range results {
		// 验证骰子点数是否有效（使用配置的边界）
		if result.Sum >= configs.GetConfig().SmallMin && result.Sum <= configs.GetConfig().BigMax {
			validResults = append(validResults, result)
		} else {
			log.Warnf("发现无效的骰子结果: %d (期号: %s) - 群组ID: %d", result.Sum, result.PeriodID, gm.ChatID)
		}
	}

	log.Infof("获取到 %d 条骰子结果，有效结果 %d 条 - 群组ID: %d", len(results), len(validResults), gm.ChatID)

	// 数据一致性检查：检查是否有期号但没有开奖结果的情况
	gm.checkDataConsistency()

	// 填充网格数据，按列优先填充（从左到右，从上到下）
	recordIndex := 0
	for col := 0; col < width; col++ {
		for row := 0; row < height; row++ {
			if recordIndex < len(validResults) {
				result := validResults[recordIndex]
				grid.Numbers[row][col] = result.Sum
				grid.Colors[row][col] = gm.getDiceColor(result.Sum)
				recordIndex++
			} else {
				// 如果没有更多记录，留空（显示为00）
				grid.Numbers[row][col] = 0
				grid.Colors[row][col] = "⚪"
			}
		}
	}

	// 计算统计信息
	grid.CalculateStats()

	return grid
}

// 检查数据一致性
func (gm *GameManager) checkDataConsistency() {
	db := mdb.Default()

	// 检查是否有期号但没有开奖结果的情况
	var inconsistentPeriods []models.TelegramK3GamePeriod
	err := db.Where("chat_id = ? AND state IN (?)", gm.ChatID, []string{"rolling", "settling"}).
		Find(&inconsistentPeriods).Error

	if err != nil {
		log.Errorf("检查数据一致性失败: %v - 群组ID: %d", err, gm.ChatID)
		return
	}

	if len(inconsistentPeriods) > 0 {
		log.Warnf("发现 %d 个未完成开奖的期号 - 群组ID: %d", len(inconsistentPeriods), gm.ChatID)

		for _, period := range inconsistentPeriods {
			// 检查是否有对应的开奖结果
			var diceResult models.TelegramK3DiceResult
			err := db.Where("period_id = ?", period.ID).First(&diceResult).Error

			if err != nil {
				log.Warnf("期号 %s 没有对应的开奖结果，可能服务意外停止 - 群组ID: %d", period.ID, gm.ChatID)
				// 可以选择将状态标记为异常或删除该期号
				// db.Model(&period).Update("state", "abnormal")
			}
		}
	}
}

// 新增：带当前期数据的网格生成
func (gm *GameManager) generateDiceGridWithCurrent(period *models.TelegramK3GamePeriod, diceResult *models.TelegramK3DiceResult) *images.DiceGrid {
	// 获取配置的网格大小
	width := configs.GetConfig().GridWidth
	height := configs.GetConfig().GridHeight
	totalSlots := width * height

	// 初始化动态大小的网格
	grid := &images.DiceGrid{
		Numbers: make([][]int, height),
		Colors:  make([][]string, height),
		Stats:   make(map[string]int),
		Width:   width,
		Height:  height,
	}

	// 初始化二维数组
	for i := 0; i < height; i++ {
		grid.Numbers[i] = make([]int, width)
		grid.Colors[i] = make([]string, width)
	}

	// 查询该群组数据库中总共有多少条开奖记录
	db := mdb.Default()
	var totalCount int64
	db.Model(&models.TelegramK3DiceResult{}).Where("chat_id = ?", gm.ChatID).Count(&totalCount)

	// 计算应该显示多少条数据：总数 % 总槽位数
	displayCount := int(totalCount % int64(totalSlots))
	log.Infof("该群组总开奖记录数: %d, 网格大小: %dx%d, 应显示数据条数: %d - 群组ID: %d",
		totalCount, width, height, displayCount, gm.ChatID)

	// 收集要显示的数据
	var results []models.TelegramK3DiceResult

	// 查询该群组最新的开奖记录（按创建时间倒序），限制为displayCount条
	var history []models.TelegramK3DiceResult
	err := db.Where("chat_id = ?", gm.ChatID).
		Order("created_at DESC").
		Limit(displayCount).
		Find(&history).Error

	if err != nil {
		log.Errorf("查询历史开奖记录失败: %v - 群组ID: %d", err, gm.ChatID)
	}

	// 反转顺序，让最早的在前面
	for i, j := 0, len(history)-1; i < j; i, j = i+1, j-1 {
		history[i], history[j] = history[j], history[i]
	}

	// 添加历史数据
	results = append(results, history...)

	// 确保当前期的开奖结果被包含在网格中
	// 如果当前期不在历史记录中，则添加它
	if diceResult != nil {
		currentPeriodInHistory := false
		for _, h := range history {
			if h.PeriodID == diceResult.PeriodID {
				currentPeriodInHistory = true
				break
			}
		}

		if !currentPeriodInHistory {
			log.Infof("当前期开奖结果不在历史记录中，添加到网格 - 群组ID: %d, 期号: %s", gm.ChatID, diceResult.PeriodID)
			results = append(results, *diceResult)
		}
	}

	log.Infof("查询到历史数据: %d 条，总计: %d 条 - 群组ID: %d",
		len(history), len(results), gm.ChatID)

	// 填充网格（自上到下，自左到右）
	recordIndex := 0
	for col := 0; col < width; col++ {
		for row := 0; row < height; row++ {
			if recordIndex < len(results) {
				result := results[recordIndex]
				grid.Numbers[row][col] = result.Sum
				grid.Colors[row][col] = gm.getDiceColor(result.Sum)
				recordIndex++
			} else {
				grid.Numbers[row][col] = 0
				grid.Colors[row][col] = "⚪"
			}
		}
	}

	grid.CalculateStats()
	return grid
}

// 根据骰子点数获取颜色
func (gm *GameManager) getDiceColor(sum int) string {
	// 根据点数总和确定颜色
	if sum >= 3 && sum <= 10 {
		return "🔵" // 小
	} else if sum >= 11 && sum <= 18 {
		return "🔴" // 大
	} else {
		// 无效点数（0或超出范围）显示为白色
		return "⚪"
	}
}

// 发送骰子开奖网格
func (gm *GameManager) sendDiceGrid(userID int64) {
	log.Infof("准备发送骰子开奖网格 - 用户ID: %d", userID)

	// 生成网格
	grid := gm.generateDiceGrid()

	// 生成开奖网格图片
	if gm.ImageGen != nil {
		statsNumbers := configs.GetConfig().StatsNumbers
		imageFilename, err := gm.ImageGen.(*images.ImageGenerator).GenerateDiceGridImage(grid, statsNumbers)
		if err != nil {
			log.Errorf("生成开奖网格图片失败: %v - 用户ID: %d", err, userID)
			return
		}

		// 发送图片
		photo := tgbotapi.NewPhoto(userID, tgbotapi.FilePath(imageFilename))
		_, err = GetBot().Send(photo)
		if err != nil {
			log.Errorf("发送骰子开奖网格图片失败: %v - 用户ID: %d", err, userID)
		} else {
			log.Infof("骰子开奖网格图片发送成功 - 用户ID: %d", userID)
		}
	} else {
		log.Errorf("图片生成器未初始化，无法发送网格图片 - 用户ID: %d", userID)
	}
}

// 机器人掷骰子
func (gm *GameManager) rollDiceByBot(period *models.TelegramK3GamePeriod) *models.TelegramK3DiceResult {
	// 计算需要掷多少个骰子
	remaining := 3 - gm.DiceRollCount
	if remaining <= 0 {
		log.Infof("无需掷骰子，当前次数: %d/3 - 群组ID: %d", gm.DiceRollCount, gm.ChatID)
		return nil
	}

	log.Infof("机器人开始掷骰子，需要掷 %d 次 - 群组ID: %d", remaining, gm.ChatID)

	bot := GetBot()
	if bot == nil {
		log.Errorf("Bot实例为nil，无法发送动画骰子 - 群组ID: %d", gm.ChatID)
		return nil
	}

	// 根据剩余次数发送动画骰子
	var diceResults []int
	var messages []tgbotapi.Message

	for i := 0; i < remaining; i++ {
		dice := tgbotapi.NewDice(gm.ChatID)

		// 重试机制
		var msg tgbotapi.Message
		var err error
		maxRetries := configs.GetConfig().MessageRetryCount

		for retry := 0; retry < maxRetries; retry++ {
			msg, err = bot.Send(dice)
			if err == nil {
				break
			}

			log.Warnf("发送第%d个骰子失败(第%d次重试): %v - 群组ID: %d", i+1, retry+1, err, gm.ChatID)

			if retry < maxRetries-1 {
				// 等待重试延迟
				time.Sleep(configs.GetConfig().MessageRetryDelay)
			}
		}

		if err != nil {
			log.Errorf("发送第%d个骰子最终失败，已重试%d次: %v - 群组ID: %d", i+1, maxRetries, err, gm.ChatID)
			return nil
		}

		log.Infof("第%d个骰子发送成功 - 群组ID: %d", i+1, gm.ChatID)

		diceResults = append(diceResults, msg.Dice.Value)
		messages = append(messages, msg)

		// 等待一小段时间再发送下一个骰子
		if i < remaining-1 {
			time.Sleep(configs.GetConfig().DiceAnimationDelay)
		}
	}

	// 计算总和
	sum := 0
	for _, value := range diceResults {
		sum += value
	}

	log.Infof("机器人掷骰子结果: %v = %d - 群组ID: %d", diceResults, sum, gm.ChatID)

	// 结合用户已掷的骰子值和机器人新掷的骰子值
	var allDiceValues []int

	// 先添加用户已掷的骰子值
	if gm.DiceValues != nil {
		allDiceValues = append(allDiceValues, gm.DiceValues...)
	}

	// 再添加机器人新掷的骰子值
	allDiceValues = append(allDiceValues, diceResults...)

	// 确保有3个骰子值（如果不足3个，用0填充）
	dice1Value := 0
	dice2Value := 0
	dice3Value := 0

	if len(allDiceValues) >= 1 {
		dice1Value = allDiceValues[0]
	}
	if len(allDiceValues) >= 2 {
		dice2Value = allDiceValues[1]
	}
	if len(allDiceValues) >= 3 {
		dice3Value = allDiceValues[2]
	}

	// 重新计算总和
	sum = dice1Value + dice2Value + dice3Value

	log.Infof("完整骰子结果: 用户[%v] + 机器人[%v] = [%d, %d, %d] = %d - 群组ID: %d",
		gm.DiceValues, diceResults, dice1Value, dice2Value, dice3Value, sum, gm.ChatID)

	result := gm.calculateResult(dice1Value, dice2Value, dice3Value, sum)

	diceResult := &models.TelegramK3DiceResult{
		PeriodID:  period.ID,
		ChatID:    gm.ChatID,
		UserID:    0, // 机器人
		UserName:  "机器人",
		PeriodNum: period.PeriodNum,
		Dice1:     dice1Value,
		Dice2:     dice2Value,
		Dice3:     dice3Value,
		Sum:       sum,
		Result:    result,
		CreatedAt: time.Now(),
	}

	// 保存骰子结果
	db := mdb.Default()
	err := db.Create(diceResult).Error
	if err != nil {
		log.Errorf("保存骰子结果失败: %v - 群组ID: %d", err, gm.ChatID)
		return nil
	}
	log.Infof("骰子结果保存成功 - 群组ID: %d", gm.ChatID)

	// 保存到内存
	gm.DiceResults[period.ID] = diceResult

	// 增加掷骰子计数器（机器人掷了remaining次）
	gm.DiceRollCount += remaining
	log.Infof("机器人掷骰子，当前掷骰子次数: %d/3 - 群组ID: %d", gm.DiceRollCount, gm.ChatID)

	log.Infof("机器人掷骰子完成: %d+%d+%d=%d, 开奖结果: %s - 群组ID: %d",
		diceResult.Dice1, diceResult.Dice2, diceResult.Dice3, diceResult.Sum, diceResult.Result, gm.ChatID)

	// 立即开奖
	go func() {
		// 等待动画完成（Telegram骰子动画大约需要3-4秒）
		// 为了确保动画完全播放完成，等待配置的时间
		time.Sleep(configs.GetConfig().DiceAnimationCompleteDelay)
		log.Infof("动画完成，立即开奖 - 群组ID: %d", gm.ChatID)
		gm.settleAndShowResult(period)
	}()

	return diceResult
}

// 计算开奖结果
func (gm *GameManager) calculateResult(dice1, dice2, dice3, sum int) string {
	// 检查豹子
	if dice1 == dice2 && dice2 == dice3 {
		return BetTypeLeopard
	}

	// 检查大小
	isBig := sum >= 11
	isOdd := sum%2 == 1

	if isBig && isOdd {
		return BetTypeBigOdd
	} else if !isBig && isOdd {
		return BetTypeSmallOdd
	} else if isBig && !isOdd {
		return BetTypeBigEven
	} else {
		return BetTypeSmallEven
	}
}

// 结算下注
func (gm *GameManager) settleBets(period *models.TelegramK3GamePeriod, diceResult *models.TelegramK3DiceResult) {
	bets := gm.BetRecords[period.ID]

	for i := range bets {
		bet := &bets[i]

		// 检查是否中奖
		bet.IsWin = gm.checkBetWin(bet.BetType, diceResult)

		if bet.IsWin {
			bet.WinAmount = int64(float64(bet.Amount) * bet.Odds)

			// 给中奖用户增加余额
			err := gm.addUserBalance(bet.UserID, bet.WinAmount)
			if err != nil {
				log.Errorf("给用户 %d 增加中奖金额失败: %v", bet.UserID, err)
				// 注意：这里应该考虑如何处理赔付失败的情况
				// 可能需要记录到错误日志，或者重试机制
			} else {
				log.Infof("用户 %s (ID: %d) 中奖，增加余额: %d", bet.UserName, bet.UserID, bet.WinAmount)
			}
		} else {
			bet.WinAmount = 0
			log.Infof("用户 %s (ID: %d) 未中奖，下注金额: %d", bet.UserName, bet.UserID, bet.Amount)
		}

		// 更新数据库
		db := mdb.Default()
		db.Save(bet)
	}
}

// 检查下注是否中奖
func (gm *GameManager) checkBetWin(betType string, diceResult *models.TelegramK3DiceResult) bool {
	dice1, dice2, dice3 := diceResult.Dice1, diceResult.Dice2, diceResult.Dice3
	sum := diceResult.Sum

	switch betType {
	case BetTypeBig:
		return sum >= configs.GetConfig().BigMin && sum <= configs.GetConfig().BigMax
	case BetTypeSmall:
		return sum >= configs.GetConfig().SmallMin && sum <= configs.GetConfig().SmallMax
	case BetTypeOdd:
		return sum%2 == 1
	case BetTypeEven:
		return sum%2 == 0
	case BetTypeBigOdd:
		return sum >= configs.GetConfig().BigMin && sum <= configs.GetConfig().BigMax && sum%2 == 1
	case BetTypeSmallOdd:
		return sum >= configs.GetConfig().SmallMin && sum <= configs.GetConfig().SmallMax && sum%2 == 1
	case BetTypeBigEven:
		return sum >= configs.GetConfig().BigMin && sum <= configs.GetConfig().BigMax && sum%2 == 0
	case BetTypeSmallEven:
		return sum >= configs.GetConfig().SmallMin && sum <= configs.GetConfig().SmallMax && sum%2 == 0
	case BetTypeLeopard:
		return dice1 == dice2 && dice2 == dice3
	case BetTypeStraight:
		// 顺子：三个连续数字 (如 1,2,3 或 4,5,6)
		nums := []int{dice1, dice2, dice3}
		sort.Ints(nums)
		return nums[1] == nums[0]+1 && nums[2] == nums[1]+1
	case BetTypePair:
		// 对子：两个相同数字
		return (dice1 == dice2 && dice1 != dice3) || (dice1 == dice3 && dice1 != dice2) || (dice2 == dice3 && dice2 != dice1)
	case BetTypeLeopard1:
		return dice1 == dice2 && dice2 == dice3 && dice1 == 1
	case BetTypeLeopard2:
		return dice1 == dice2 && dice2 == dice3 && dice1 == 2
	case BetTypeLeopard3:
		return dice1 == dice2 && dice2 == dice3 && dice1 == 3
	case BetTypeLeopard4:
		return dice1 == dice2 && dice2 == dice3 && dice1 == 4
	case BetTypeLeopard5:
		return dice1 == dice2 && dice2 == dice3 && dice1 == 5
	case BetTypeLeopard6:
		return dice1 == dice2 && dice2 == dice3 && dice1 == 6
	default:
		// 处理定位胆下注 (special_4, special_5, etc.)
		if strings.HasPrefix(betType, "special_") {
			parts := strings.Split(betType, "_")
			if len(parts) == 2 {
				if pos, err := strconv.Atoi(parts[1]); err == nil && pos >= 4 && pos <= 17 {
					// 定位胆：检查骰子总和是否为指定数字
					return sum == pos
				}
			}
		}
		return false
	}
}

// 获取下注统计
func (gm *GameManager) getBetStats(periodID string) *BetStats {
	bets := gm.BetRecords[periodID]

	stats := &BetStats{
		BetByType:    make(map[string]int64),
		BetByUser:    make(map[int64]int64),
		UserNames:    make(map[int64]string),
		MaxBetUser:   0,
		MaxBetAmount: 0,
	}

	for _, bet := range bets {
		stats.TotalBets++
		stats.TotalAmount += bet.Amount
		stats.BetByType[bet.BetType] += bet.Amount
		stats.BetByUser[bet.UserID] += bet.Amount
		stats.UserNames[bet.UserID] = bet.UserName

		if bet.Amount > stats.MaxBetAmount {
			stats.MaxBetAmount = bet.Amount
			stats.MaxBetUser = bet.UserID
		}
	}

	return stats
}

// 获取下注最高的玩家
func (gm *GameManager) getMaxBetUser(periodID string) (int64, string) {
	stats := gm.getBetStats(periodID)
	if stats.MaxBetUser == 0 {
		return 0, ""
	}
	return stats.MaxBetUser, stats.UserNames[stats.MaxBetUser]
}

// 获取结算统计
func (gm *GameManager) getSettlementStats(periodID string) *SettlementStats {
	bets := gm.BetRecords[periodID]

	stats := &SettlementStats{
		WinByUser: make(map[int64]int64),
		UserNames: make(map[int64]string),
	}

	for _, bet := range bets {
		stats.UserNames[bet.UserID] = bet.UserName

		if bet.IsWin {
			stats.TotalWinAmount += bet.WinAmount
			stats.WinByUser[bet.UserID] += bet.WinAmount
			stats.WinCount++
		} else {
			stats.LoseCount++
		}
	}

	return stats
}

// 结算并显示结果
func (gm *GameManager) settleAndShowResult(period *models.TelegramK3GamePeriod) {
	log.Infof("开始结算并显示结果 - 群组ID: %d", gm.ChatID)

	// 获取骰子结果
	diceResult := gm.DiceResults[period.ID]
	if diceResult == nil {
		log.Errorf("未找到骰子结果 - 群组ID: %d", gm.ChatID)
		return
	}

	// 结算下注（使用新的结算流程）
	log.Infof("开始结算下注 - 群组ID: %d", gm.ChatID)
	gm.processSettlementWithPayout(period, diceResult)
	log.Infof("下注结算完成 - 群组ID: %d", gm.ChatID)

	// 发送开奖消息
	log.Infof("准备发送开奖消息 - 群组ID: %d", gm.ChatID)
	gm.sendSettlementMessage(period, diceResult)
	log.Infof("开奖消息发送完成 - 群组ID: %d", gm.ChatID)

	// 按照正常游戏流程，总是自动开始下一期
	log.Infof("开奖完成，立即结束本期并准备开始下一期 - 群组ID: %d", gm.ChatID)
	gm.endGame()
}

// 获取用户余额（优先使用内存余额）
func (gm *GameManager) getUserBalance(userID int64, currencyType configs.CurrencyType) float64 {
	if gm.UserBalances == nil {
		gm.UserBalances = make(map[int64]float64)
	}
	if balance, ok := gm.UserBalances[userID]; ok {
		return balance
	}
	// 首次出现的玩家初始化余额
	if userID == gm.CurrentBanker {
		gm.UserBalances[userID] = 999999.0 // 庄家余额
		return 999999.0
	}
	gm.UserBalances[userID] = 1000.0
	return 1000.0
}

// 扣除用户余额（只影响内存余额）
func (gm *GameManager) deductUserBalance(userID int64, amount int64) error {
	if gm.UserBalances == nil {
		gm.UserBalances = make(map[int64]float64)
	}
	currentBalance := gm.getUserBalance(userID, gm.CurrencyType)
	if currentBalance < float64(amount) {
		return fmt.Errorf("余额不足，当前余额: %.2f, 需要扣除: %d", currentBalance, amount)
	}
	gm.UserBalances[userID] = currentBalance - float64(amount)
	log.Infof("模拟扣除用户 %d 余额: %d, 当前余额: %.2f", userID, amount, gm.UserBalances[userID])
	return nil
}

// 增加用户余额（只影响内存余额）
func (gm *GameManager) addUserBalance(userID int64, amount int64) error {
	if gm.UserBalances == nil {
		gm.UserBalances = make(map[int64]float64)
	}
	currentBalance := gm.getUserBalance(userID, gm.CurrencyType)
	gm.UserBalances[userID] = currentBalance + float64(amount)
	log.Infof("模拟增加用户 %d 余额: %d, 当前余额: %.2f", userID, amount, gm.UserBalances[userID])
	return nil
}

// 冻结用户余额（实际实现）
func (gm *GameManager) freezeUserBalance(userID int64, amount int64) error {
	// 在实际系统中，这里应该：
	// 1. 检查用户可用余额是否足够（当前余额 - 已冻结金额）
	// 2. 冻结指定金额（不扣除，只是标记为不可用）
	// 3. 记录冻结日志

	// 获取当前余额和已冻结金额
	currentBalance := gm.getUserBalance(userID, gm.CurrencyType)
	currentPeriodID := gm.CurrentGame.Period.ID
	alreadyFrozen := gm.getUserFrozenAmount(userID, currentPeriodID)

	// 计算可用余额（当前余额 - 已冻结金额）
	availableBalance := currentBalance - float64(alreadyFrozen)

	// 检查可用余额是否足够
	if availableBalance < float64(amount) {
		return fmt.Errorf("可用余额不足，无法冻结。当前余额: %.2f, 已冻结: %d, 可用余额: %.2f, 冻结金额: %d",
			currentBalance, alreadyFrozen, availableBalance, amount)
	}

	log.Infof("冻结用户 %d 余额: %d (当前余额: %.2f, 已冻结: %d, 可用余额: %.2f)",
		userID, amount, currentBalance, alreadyFrozen, availableBalance)
	return nil
}

// 解冻用户余额（实际实现）
func (gm *GameManager) unfreezeUserBalance(userID int64, amount int64) error {
	// 在实际系统中，这里应该：
	// 1. 解冻指定金额
	// 2. 记录解冻日志

	log.Infof("解冻用户 %d 余额: %d", userID, amount)
	return nil
}

// 扣除冻结的余额（结算时调用）
func (gm *GameManager) deductFrozenBalance(userID int64, amount int64) error {
	// 在实际系统中，这里应该：
	// 1. 扣除已冻结的余额
	// 2. 记录扣除日志

	err := gm.deductUserBalance(userID, amount)
	if err != nil {
		return fmt.Errorf("扣除冻结余额失败: %v", err)
	}

	log.Infof("扣除用户 %d 冻结余额: %d", userID, amount)
	return nil
}

// 获取用户冻结金额（本期下注总额）
func (gm *GameManager) getUserFrozenAmount(userID int64, periodID string) int64 {
	var frozen int64 = 0
	if bets, exists := gm.BetRecords[periodID]; exists {
		for _, bet := range bets {
			if bet.UserID == userID {
				frozen += bet.Amount
			}
		}
	}
	return frozen
}

// 计算用户实际余额（直接返回内存余额）
func (gm *GameManager) calculateUserActualBalance(userID int64, periodID string) float64 {
	// 直接返回内存余额，因为下注和赔付都已经实时更新到内存余额中
	balance := gm.getUserBalance(userID, gm.CurrencyType)
	log.Infof("用户 %d 当前余额: %.2f", userID, balance)
	return balance
}

// 检查同类型累计下注限额
func (gm *GameManager) checkSameTypeBetLimit(userID int64, betType string, newAmount int64) error {
	currentPeriodID := gm.CurrentGame.Period.ID
	var totalAmount int64 = 0

	// 计算当前期数中该用户该类型的总下注金额
	if bets, exists := gm.BetRecords[currentPeriodID]; exists {
		for _, bet := range bets {
			if bet.UserID == userID && bet.BetType == betType {
				totalAmount += bet.Amount
			}
		}
	}

	// 加上新下注金额
	totalAmount += newAmount

	// 检查是否超过限额
	limit := configs.GetBetLimit(betType)
	if limit > 0 && totalAmount > limit {
		return fmt.Errorf("同类型下注累计金额超过限额: %d (限额: %d)", totalAmount, limit)
	}

	return nil
}

// 检查总体下注限额
func (gm *GameManager) checkTotalBetLimit(userID int64, newAmount int64) error {
	currentPeriodID := gm.CurrentGame.Period.ID
	var totalAmount int64 = 0

	// 计算当前期数中该用户的总下注金额
	if bets, exists := gm.BetRecords[currentPeriodID]; exists {
		for _, bet := range bets {
			if bet.UserID == userID {
				totalAmount += bet.Amount
			}
		}
	}

	// 加上新下注金额
	totalAmount += newAmount

	// 检查是否超过总体限额
	totalLimit := configs.GetConfig().TotalBetLimit
	if totalLimit > 0 && totalAmount > totalLimit {
		return fmt.Errorf("总下注金额超过限额: %d (限额: %d)", totalAmount, totalLimit)
	}

	return nil
}

// 检查下注金额是否为底注的倍数
func (gm *GameManager) checkBetAmountMultiple(amount int64) error {
	config := configs.GetConfig()
	baseBet := config.BaseBet

	// 检查是否为底注的倍数
	if amount%baseBet != 0 {
		return fmt.Errorf("下注金额必须是底注的倍数，底注: %d, 下注金额: %d", baseBet, amount)
	}

	// 检查是否大于等于底注
	if amount < baseBet {
		return fmt.Errorf("下注金额不能小于底注，底注: %d, 下注金额: %d", baseBet, amount)
	}

	return nil
}

// 完整下注流程（包含余额检查、扣款、记录）
func (gm *GameManager) processBetWithBalanceCheck(userID int64, userName, betType string, amount int64) error {
	// 1. 基础验证
	if configs.GetBetOdds(betType) == 0.0 {
		return fmt.Errorf("无效的下注类型: %s", betType)
	}
	if amount <= 0 {
		return fmt.Errorf("下注金额必须大于0")
	}

	// 2. 庄家下注检查
	if userID == gm.CurrentBanker {
		return fmt.Errorf("庄家不能下注")
	}

	// 3. 单次下注限额检查
	limit := configs.GetBetLimit(betType)
	if limit > 0 && amount > limit {
		return fmt.Errorf("下注金额超过限额: %d (限额: %d)", amount, limit)
	}

	// 4. 同类型累计限额检查
	if err := gm.checkSameTypeBetLimit(userID, betType, amount); err != nil {
		return err
	}

	// 5. 总体下注限额检查
	if err := gm.checkTotalBetLimit(userID, amount); err != nil {
		return err
	}

	// 6. 底注倍数检查
	if err := gm.checkBetAmountMultiple(amount); err != nil {
		return err
	}

	// 7. 玩家余额检查（考虑已冻结金额）
	currentBalance := gm.getUserBalance(userID, gm.CurrencyType)
	currentPeriodID := gm.CurrentGame.Period.ID
	alreadyFrozen := gm.getUserFrozenAmount(userID, currentPeriodID)
	availableBalance := currentBalance - float64(alreadyFrozen)

	if availableBalance < float64(amount) {
		return fmt.Errorf("可用余额不足，当前余额: %.2f, 已冻结: %d, 可用余额: %.2f, 下注金额: %d",
			currentBalance, alreadyFrozen, availableBalance, amount)
	}

	// 8. 庄家余额溢出检查
	// 计算如果玩家中奖，庄家需要赔付的最大金额
	maxPayout := int64(float64(amount) * configs.GetBetOdds(betType))
	bankerID := gm.CurrentBanker
	bankerBalance := gm.getUserBalance(bankerID, gm.CurrencyType)

	// 检查庄家是否有足够余额赔付
	if bankerBalance < float64(maxPayout) {
		return fmt.Errorf("庄家余额不足，无法接受此下注。庄家余额: %.2f, 最大赔付: %d", bankerBalance, maxPayout)
	}

	// 9. 冻结玩家余额（不立即扣除）
	err := gm.freezeUserBalance(userID, amount)
	if err != nil {
		return fmt.Errorf("冻结余额失败: %v", err)
	}

	// 10. 创建下注记录
	period := gm.CurrentGame.Period
	bet := models.TelegramK3BetRecord{
		PeriodID:  period.ID,
		ChatID:    gm.ChatID,
		UserID:    userID,
		UserName:  userName,
		BetType:   betType,
		Amount:    amount,
		Odds:      configs.GetBetOdds(betType),
		CreatedAt: time.Now(),
	}

	// 11. 保存到数据库
	db := mdb.Default()
	err = db.Create(&bet).Error
	if err != nil {
		// 如果保存失败，应该回滚冻结
		log.Errorf("保存下注记录失败，需要回滚冻结: %v", err)
		// 回滚冻结
		_ = gm.unfreezeUserBalance(userID, amount)
		return fmt.Errorf("保存下注记录失败: %v", err)
	}

	// 12. 添加到内存
	gm.BetRecords[period.ID] = append(gm.BetRecords[period.ID], bet)

	log.Infof("用户 %s (ID: %d) 下注 %s %d 成功，冻结余额: %d", userName, userID, betType, amount, amount)
	return nil
}

// 完整结算流程（包含赔付、记录更新）
func (gm *GameManager) processSettlementWithPayout(period *models.TelegramK3GamePeriod, diceResult *models.TelegramK3DiceResult) {
	bets := gm.BetRecords[period.ID]

	log.Infof("开始结算，共 %d 个下注记录", len(bets))

	// 结算时庄家余额变化
	bankerID := gm.CurrentBanker
	bankerPay := int64(0) // 庄家需要赔付的金额（玩家赢钱）
	bankerWin := int64(0) // 庄家获得的金额（玩家输钱）

	for i := range bets {
		bet := &bets[i]

		// 1. 检查是否中奖
		bet.IsWin = gm.checkBetWin(bet.BetType, diceResult)

		if bet.IsWin {
			// 2. 计算中奖金额
			bet.WinAmount = int64(float64(bet.Amount) * bet.Odds)

			// 3. 给中奖用户增加余额（赢家不扣除下注金额，只获得赔付）
			err := gm.addUserBalance(bet.UserID, bet.WinAmount)
			if err != nil {
				log.Errorf("给用户 %d 增加中奖金额失败: %v", bet.UserID, err)
				bet.PayoutStatus = "failed"
			} else {
				log.Infof("用户 %s (ID: %d) 中奖，增加余额: %d", bet.UserName, bet.UserID, bet.WinAmount)
				bet.PayoutStatus = "success"
				bankerPay += bet.WinAmount // 庄家需要赔付
			}
		} else {
			// 4. 输家扣除冻结的下注金额
			bet.WinAmount = 0 // 输家没有中奖金额
			err := gm.deductFrozenBalance(bet.UserID, bet.Amount)
			if err != nil {
				log.Errorf("扣除用户 %d 冻结余额失败: %v", bet.UserID, err)
				bet.PayoutStatus = "failed"
			} else {
				log.Infof("用户 %s (ID: %d) 未中奖，扣除冻结余额: %d", bet.UserName, bet.UserID, bet.Amount)
				bet.PayoutStatus = "no_win"
				bankerWin += bet.Amount // 庄家获得玩家输的下注金额
			}
		}

		// 5. 重置冻结金额（无论输赢都要重置）
		err := gm.unfreezeUserBalance(bet.UserID, bet.Amount)
		if err != nil {
			log.Errorf("重置用户 %d 冻结金额失败: %v", bet.UserID, err)
		} else {
			log.Infof("用户 %s (ID: %d) 冻结金额已重置: %d", bet.UserName, bet.UserID, bet.Amount)
		}
	}

	// 更新庄家余额：获得玩家输的钱，赔付玩家赢的钱
	if bankerWin > 0 {
		err := gm.addUserBalance(bankerID, bankerWin)
		if err != nil {
			log.Errorf("给庄家增加赢钱金额失败: %v", err)
		} else {
			log.Infof("庄家(ID: %d) 获得玩家输钱总额: %d", bankerID, bankerWin)
		}
	}

	if bankerPay > 0 {
		err := gm.deductUserBalance(bankerID, bankerPay)
		if err != nil {
			log.Errorf("扣除庄家赔付金额失败: %v", err)
		} else {
			log.Infof("庄家(ID: %d) 赔付总额: %d", bankerID, bankerPay)
		}
	}

	// 更新数据库中的下注记录
	db := mdb.Default()
	for _, bet := range bets {
		err := db.Save(&bet).Error
		if err != nil {
			log.Errorf("更新下注记录失败: %v", err)
		}
	}

	log.Infof("结算完成，庄家获得: %d，庄家赔付: %d，净收益: %d", bankerWin, bankerPay, bankerWin-bankerPay)
}

// 停止游戏
func (gsm *GameStateMachine) Stop() {
	log.Infof("停止游戏状态机，当前状态: %s", gsm.CurrentState)

	// 停止定时器
	if gsm.Timer != nil {
		gsm.Timer.Stop()
		log.Infof("游戏定时器已停止")
	}

	// 关闭停止通道
	select {
	case <-gsm.StopChan:
		// 通道已经关闭
		log.Infof("停止通道已经关闭")
	default:
		close(gsm.StopChan)
		log.Infof("停止通道已关闭")
	}

	// 重置状态
	gsm.CurrentState = ""
	log.Infof("游戏状态机已完全停止")
}

// 添加用户名到用户ID的映射
func (gm *GameManager) AddUsernameMapping(username string, userID int64) {
	if gm.UsernameCache == nil {
		gm.UsernameCache = make(map[string]int64)
	}
	gm.UsernameCache[strings.ToLower(username)] = userID
	log.Infof("添加用户名映射: %s -> %d - 群组ID: %d", username, userID, gm.ChatID)
}

// 通过用户名获取用户ID
func (gm *GameManager) GetUserIDByUsername(username string) (int64, bool) {
	if gm.UsernameCache == nil {
		return 0, false
	}
	userID, exists := gm.UsernameCache[strings.ToLower(username)]
	return userID, exists
}

// 获取所有用户名映射
func (gm *GameManager) GetAllUsernameMappings() map[string]int64 {
	if gm.UsernameCache == nil {
		return make(map[string]int64)
	}
	return gm.UsernameCache
}
