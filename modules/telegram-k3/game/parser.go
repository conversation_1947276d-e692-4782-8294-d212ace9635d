package game

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// 下注解析结果
type BetParseResult struct {
	BetType string // 标准化的下注类型
	Amount  int64  // 下注金额
	Valid   bool   // 是否有效
	Error   string // 错误信息
}

// 下注类型映射表
var betTypeMapping = map[string]string{
	// 组合下注 - 简写
	"dd": "big_odd",    // 大单
	"ds": "big_even",   // 大双
	"xd": "small_odd",  // 小单
	"xs": "small_even", // 小双

	// 组合下注 - 全称
	"大单": "big_odd",
	"大双": "big_even",
	"小单": "small_odd",
	"小双": "small_even",

	// 高倍下注 - 简写
	"bz1": "leopard_1", // 豹子1
	"bz2": "leopard_2", // 豹子2
	"bz3": "leopard_3", // 豹子3
	"bz4": "leopard_4", // 豹子4
	"bz5": "leopard_5", // 豹子5
	"bz6": "leopard_6", // 豹子6

	// 高倍下注 - 全称
	"豹子1": "leopard_1",
	"豹子2": "leopard_2",
	"豹子3": "leopard_3",
	"豹子4": "leopard_4",
	"豹子5": "leopard_5",
	"豹子6": "leopard_6",

	// 基础下注类型
	"大": "big",
	"小": "small",
	"单": "odd",
	"双": "even",

	// 基础下注缩写
	"da":  "big",   // 大
	"x":   "small", // 小
	"dan": "odd",   // 单
	"s":   "even",  // 双

	// 特码下注 - 定位胆格式
	"定位胆1": "special_1",
	"定位胆2": "special_2",
	"定位胆3": "special_3",
	"定位胆4": "special_4",
	"定位胆5": "special_5",
	"定位胆6": "special_6",

	// 特码下注 - 简写格式
	"dwd1": "special_1",
	"dwd2": "special_2",
	"dwd3": "special_3",
	"dwd4": "special_4",
	"dwd5": "special_5",
	"dwd6": "special_6",
}

// 解析下注命令
func ParseBetCommand(text string) *BetParseResult {
	// 移除命令前缀（如果存在）
	text = strings.TrimPrefix(text, "/bet")
	text = strings.TrimSpace(text)

	if text == "" {
		return &BetParseResult{
			Valid: false,
			Error: "请输入下注内容",
		}
	}

	// 尝试解析不同格式
	result := parseCombinationBet(text)
	if result.Valid {
		return result
	}

	result = parseHighOddsBet(text)
	if result.Valid {
		return result
	}

	result = parseSpecialBet(text)
	if result.Valid {
		return result
	}

	result = parseSpecialGameBet(text)
	if result.Valid {
		return result
	}

	result = parseBasicBet(text)
	if result.Valid {
		return result
	}

	return &BetParseResult{
		Valid: false,
		Error: "无法识别的下注格式",
	}
}

// 解析组合下注格式
func parseCombinationBet(text string) *BetParseResult {
	// 匹配格式: dd10, ds10, xd10, xs10 或 大单10, 大双10, 小单10, 小双10
	patterns := []string{
		`^(dd|ds|xd|xs)(\d+)$`, // 简写格式
		`^(大单|大双|小单|小双)(\d+)$`, // 全称格式
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) == 3 {
			betType := matches[1]
			amountStr := matches[2]

			// 查找映射
			if mappedType, exists := betTypeMapping[betType]; exists {
				amount, err := strconv.ParseInt(amountStr, 10, 64)
				if err != nil {
					return &BetParseResult{
						Valid: false,
						Error: "金额格式错误",
					}
				}

				return &BetParseResult{
					BetType: mappedType,
					Amount:  amount,
					Valid:   true,
				}
			}
		}
	}

	return &BetParseResult{Valid: false}
}

// 解析高倍下注格式
func parseHighOddsBet(text string) *BetParseResult {
	// 匹配格式: bz1 10, bz2 10 或 豹子1 10, 豹子2 10
	patterns := []string{
		`^(bz[1-6])\s*(\d+)$`, // 简写格式
		`^(豹子[1-6])\s*(\d+)$`, // 全称格式
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) == 3 {
			betType := matches[1]
			amountStr := matches[2]

			// 查找映射
			if mappedType, exists := betTypeMapping[betType]; exists {
				amount, err := strconv.ParseInt(amountStr, 10, 64)
				if err != nil {
					return &BetParseResult{
						Valid: false,
						Error: "金额格式错误",
					}
				}

				return &BetParseResult{
					BetType: mappedType,
					Amount:  amount,
					Valid:   true,
				}
			}
		}
	}

	return &BetParseResult{Valid: false}
}

// 解析定位胆下注格式
func parseSpecialBet(text string) *BetParseResult {
	// 匹配格式: 定位胆4 10, dwd4 10, 4y10 (支持4-17)
	patterns := []string{
		`^定位胆(\d+)\s*(\d+)$`, // 定位胆格式
		`^dwd(\d+)\s*(\d+)$`, // 简写格式
		`^(\d+)y(\d+)$`,      // 数字y数字格式
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) == 3 {
			position := matches[1]
			amountStr := matches[2]

			// 验证位置 (4-17)
			pos, err := strconv.Atoi(position)
			if err != nil || pos < 4 || pos > 17 {
				return &BetParseResult{
					Valid: false,
					Error: "定位胆数字必须在4-17之间",
				}
			}

			amount, err := strconv.ParseInt(amountStr, 10, 64)
			if err != nil {
				return &BetParseResult{
					Valid: false,
					Error: "金额格式错误",
				}
			}

			// 构造定位胆下注类型
			betType := fmt.Sprintf("special_%d", pos)

			return &BetParseResult{
				BetType: betType,
				Amount:  amount,
				Valid:   true,
			}
		}
	}

	return &BetParseResult{Valid: false}
}

// 解析基础下注格式
func parseBasicBet(text string) *BetParseResult {
	// 匹配格式: 大 100, 小 100, 单 100, 双 100 或 da100, x100, dan100, s100
	patterns := []string{
		`^(大|小|单|双)\s*(\d+)$`, // 全称格式
		`^(da|x|dan|s)(\d+)$`, // 缩写格式
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) == 3 {
			betType := matches[1]
			amountStr := matches[2]

			// 查找映射
			if mappedType, exists := betTypeMapping[betType]; exists {
				amount, err := strconv.ParseInt(amountStr, 10, 64)
				if err != nil {
					return &BetParseResult{
						Valid: false,
						Error: "金额格式错误",
					}
				}

				return &BetParseResult{
					BetType: mappedType,
					Amount:  amount,
					Valid:   true,
				}
			}
		}
	}

	return &BetParseResult{Valid: false}
}

// 解析特殊玩法下注格式
func parseSpecialGameBet(text string) *BetParseResult {
	// 匹配格式: bz 10, dz 10, sz 10 或 豹子 10, 顺子 10, 对子 10
	patterns := []string{
		`^(bz|dz|sz)\s*(\d+)$`, // 简写格式
		`^(豹子|顺子|对子)\s*(\d+)$`, // 全称格式
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) == 3 {
			betType := matches[1]
			amountStr := matches[2]

			// 查找映射
			if mappedType, exists := betTypeMapping[betType]; exists {
				amount, err := strconv.ParseInt(amountStr, 10, 64)
				if err != nil {
					return &BetParseResult{
						Valid: false,
						Error: "金额格式错误",
					}
				}

				return &BetParseResult{
					BetType: mappedType,
					Amount:  amount,
					Valid:   true,
				}
			}
		}
	}

	return &BetParseResult{Valid: false}
}

// 多种下注解析结果
type MultiBetParseResult struct {
	Bets  []BetParseResult // 多个下注
	Valid bool             // 是否有效
	Error string           // 错误信息
}

// 解析多种下注
func ParseMultiBetCommand(text string) *MultiBetParseResult {
	// 移除命令前缀（如果存在）
	text = strings.TrimPrefix(text, "/bet")
	text = strings.TrimSpace(text)

	if text == "" {
		return &MultiBetParseResult{
			Valid: false,
			Error: "请输入下注内容",
		}
	}

	// 尝试解析不带分隔符的格式
	noSeparatorBets := parseNoSeparatorBets(text)
	if len(noSeparatorBets) > 0 {
		return processBetResults(noSeparatorBets)
	}

	// 按换行符或分号分割多种下注
	lines := strings.Split(text, "\n")
	var allBets []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			// 按分号分割同一行的多种下注
			semicolonBets := strings.Split(line, ";")
			for _, bet := range semicolonBets {
				bet = strings.TrimSpace(bet)
				if bet != "" {
					allBets = append(allBets, bet)
				}
			}
		}
	}

	if len(allBets) == 0 {
		return &MultiBetParseResult{
			Valid: false,
			Error: "请输入下注内容",
		}
	}

	return processBetResults(allBets)
}

// 解析不带分隔符的下注格式
func parseNoSeparatorBets(text string) []string {
	var bets []string
	remaining := text

	// 定义所有可能的下注模式
	patterns := []struct {
		pattern *regexp.Regexp
		name    string
	}{
		// 组合下注简写
		{regexp.MustCompile(`^(dd|ds|xd|xs)(\d+)`), "组合简写"},
		// 组合下注全称
		{regexp.MustCompile(`^(大单|大双|小单|小双)(\d+)`), "组合全称"},
		// 高倍下注简写
		{regexp.MustCompile(`^(bz[1-6])\s*(\d+)`), "高倍简写"},
		// 高倍下注全称
		{regexp.MustCompile(`^(豹子[1-6])\s*(\d+)`), "高倍全称"},
		// 特码下注
		{regexp.MustCompile(`^(定位胆\d+)\s*(\d+)`), "特码定位胆"},
		{regexp.MustCompile(`^(dwd\d+)\s*(\d+)`), "特码简写"},
		{regexp.MustCompile(`^(\d+y\d+)`), "特码数字"},
		// 基础下注
		{regexp.MustCompile(`^(大|小|单|双)\s*(\d+)`), "基础下注"},
		// 基础下注缩写
		{regexp.MustCompile(`^(d|x|dan|shuang)(\d+)`), "基础下注缩写"},
	}

	for len(remaining) > 0 {
		found := false

		for _, p := range patterns {
			if match := p.pattern.FindString(remaining); match != "" {
				bets = append(bets, match)
				remaining = remaining[len(match):]
				found = true
				break
			}
		}

		if !found {
			// 如果没有匹配到任何模式，尝试跳过空白字符
			if strings.TrimSpace(remaining) == "" {
				break
			}
			// 跳过第一个字符，继续尝试
			remaining = remaining[1:]
		}
	}

	return bets
}

// 处理下注结果
func processBetResults(betTexts []string) *MultiBetParseResult {
	var results []BetParseResult
	var errors []string

	for i, betText := range betTexts {
		result := ParseBetCommand(betText)
		if result.Valid {
			results = append(results, *result)
		} else {
			errors = append(errors, fmt.Sprintf("第%d个下注: %s", i+1, result.Error))
		}
	}

	if len(results) == 0 {
		return &MultiBetParseResult{
			Valid: false,
			Error: "所有下注格式都无效:\n" + strings.Join(errors, "\n"),
		}
	}

	return &MultiBetParseResult{
		Bets:  results,
		Valid: true,
		Error: func() string {
			if len(errors) > 0 {
				return "部分下注成功，以下下注失败:\n" + strings.Join(errors, "\n")
			}
			return ""
		}(),
	}
}
