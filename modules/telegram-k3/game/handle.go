package game

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"s2/modules/telegram-k3/configs"
	"s2/modules/telegram-k3/models"

	tgbotapi "github.com/fcwrsmall/telegram-bot-api"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
)

// 全局变量
var bot *tgbotapi.BotAPI

// SetBot 设置bot实例
func SetBot(b *tgbotapi.BotAPI) {
	bot = b
}

// GetBot 获取bot实例
func GetBot() *tgbotapi.BotAPI {
	return bot
}

// StartCommandParams 开始命令参数结构体
type StartCommandParams struct {
	BankerName   string // 庄家用户名（@username格式）
	BankerID     string // 庄家用户ID
	Currency     string // 货币类型
	PeriodMiddle string // 期号中间数字（7位）
}

// parseStartCommandParams 解析开始命令参数
func parseStartCommandParams(args []string) StartCommandParams {
	params := StartCommandParams{}

	for _, arg := range args {
		if strings.HasPrefix(arg, "--") {
			parts := strings.SplitN(arg[2:], "=", 2)
			if len(parts) == 2 {
				key := parts[0]
				value := parts[1]

				switch key {
				case "bankerName":
					params.BankerName = value
				case "bankerID":
					params.BankerID = value
				case "currency":
					params.Currency = value
				case "periodMiddle":
					params.PeriodMiddle = value
				}
			}
		}
	}

	return params
}

// resolveBankerParamWithParams 使用参数结构体解析庄家参数
func resolveBankerParamWithParams(bankerName string, chatID int64, triggerUser *tgbotapi.User, bankerID string) (int64, string, error) {
	// 检查是否是@用户名格式
	if strings.HasPrefix(bankerName, "@") {
		username := strings.TrimPrefix(bankerName, "@")

		// 如果有指定的用户ID，直接使用
		if bankerID != "" {
			if userID, err := strconv.ParseInt(bankerID, 10, 64); err == nil {
				// 获取游戏管理器并添加映射
				manager := GetGameManager(chatID)
				if manager != nil {
					manager.AddUsernameMapping(username, userID)
					log.Infof("添加用户名映射: %s -> %d", username, userID)
				}
				return userID, username, nil
			}
		}

		// 获取游戏管理器
		manager := GetGameManager(chatID)
		if manager == nil {
			// 如果游戏管理器不存在，使用触发用户作为庄家
			log.Warnf("游戏管理器不存在，使用触发用户作为庄家 - 用户名: %s", username)
			userName := triggerUser.UserName
			if userName == "" {
				userName = triggerUser.FirstName
			}
			return triggerUser.ID, userName, nil
		}

		// 尝试从缓存中获取用户ID
		if userID, exists := manager.GetUserIDByUsername(username); exists {
			log.Infof("从缓存中找到用户名映射: %s -> %d", username, userID)
			return userID, username, nil
		}

		// 如果都找不到，使用触发用户作为庄家并添加到缓存
		log.Warnf("无法找到用户名 %s 对应的用户ID，使用触发用户作为庄家", username)
		manager.AddUsernameMapping(username, triggerUser.ID)

		userName := triggerUser.UserName
		if userName == "" {
			userName = triggerUser.FirstName
		}
		return triggerUser.ID, userName, nil
	} else {
		// 尝试解析为用户ID
		if id, err := strconv.ParseInt(bankerName, 10, 64); err == nil {
			return id, fmt.Sprintf("用户%d", id), nil
		} else {
			return 0, "", fmt.Errorf("无效的庄家参数: %s", bankerName)
		}
	}
}

// 处理下注命令
func HandleBetCommand(update *tgbotapi.Update) error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("HandleBetCommand panic: %v", r)
		}
	}()

	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID
	userName := update.Message.From.UserName
	if userName == "" {
		userName = update.Message.From.FirstName
	}
	messageID := update.Message.MessageID

	// 获取游戏管理器并记录状态
	manager := GetGameManager(chatID)
	gameState := "无游戏"
	if manager != nil && manager.CurrentGame != nil {
		gameState = manager.CurrentGame.CurrentState
	}
	log.Infof("[游戏状态:%s] 收到下注命令 - 群组ID: %d, 用户ID: %d, 用户名: %s", gameState, chatID, userID, userName)

	// 检查游戏状态 - 仅在下注阶段允许下注
	if manager.CurrentGame == nil {
		log.Infof("[游戏状态:无游戏] 拒绝下注 - 当前没有进行中的游戏")
		return sendBetErrorMessageToGroup(chatID, "❌ 当前没有进行中的游戏，无法下注", messageID)
	}

	if manager.CurrentGame.CurrentState != GameStateBetting {
		stateName := getStateDisplayName(manager.CurrentGame.CurrentState)
		log.Infof("[游戏状态:%s] 拒绝下注 - 当前不在下注阶段", manager.CurrentGame.CurrentState)
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ 当前游戏状态为：%s，无法下注\n\n请等待下注阶段开始", stateName), messageID)
	}

	log.Infof("[游戏状态:%s] 状态检查通过，允许下注", manager.CurrentGame.CurrentState)

	// 使用新的解析器解析多种下注命令
	text := update.Message.Text
	result := ParseMultiBetCommand(text)

	if !result.Valid {
		log.Infof("[游戏状态:%s] 下注格式错误: %s", manager.CurrentGame.CurrentState, result.Error)
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ 下注格式错误\n%s\n\n%s", result.Error, configs.GetBetHelpText()), messageID)
	}

	log.Infof("[游戏状态:%s] 下注解析成功，共%d个下注", manager.CurrentGame.CurrentState, len(result.Bets))

	// 处理多个下注
	var successBets []string
	var failedBets []string

	for _, bet := range result.Bets {
		log.Infof("[游戏状态:%s] 处理下注: %s %d", manager.CurrentGame.CurrentState, bet.BetType, bet.Amount)
		err := manager.handleBetInternal(userID, userName, bet.BetType, bet.Amount)
		if err != nil {
			log.Infof("[游戏状态:%s] 下注失败: %s %d - %s", manager.CurrentGame.CurrentState, bet.BetType, bet.Amount, err.Error())
			failedBets = append(failedBets, fmt.Sprintf("%s%d: %s", configs.GetBetTypeDisplayName(bet.BetType), bet.Amount, err.Error()))
		} else {
			log.Infof("[游戏状态:%s] 下注成功: %s %d", manager.CurrentGame.CurrentState, bet.BetType, bet.Amount)
			successBets = append(successBets, fmt.Sprintf("%s%d", configs.GetBetTypeDisplayName(bet.BetType), bet.Amount))
		}
	}

	// 发送下注结果消息
	if len(successBets) > 0 {
		log.Infof("[游戏状态:%s] 发送下注成功消息，成功下注数: %d", manager.CurrentGame.CurrentState, len(successBets))
		// 使用新的下注成功消息格式
		manager.sendBetSuccessMessage(chatID, userID, userName, successBets, messageID)
	}

	if len(failedBets) > 0 {
		log.Infof("[游戏状态:%s] 发送下注失败消息，失败下注数: %d", manager.CurrentGame.CurrentState, len(failedBets))
		failedMsg := fmt.Sprintf("❌ 下注失败:\n%s", strings.Join(failedBets, "\n"))
		sendBetErrorMessageToGroup(chatID, failedMsg, messageID)
	}

	// 如果有部分下注失败，显示错误信息
	if result.Error != "" {
		log.Warnf("[游戏状态:%s] 部分下注失败: %s", manager.CurrentGame.CurrentState, result.Error)
	}

	return nil
}

// 处理骰子网格命令
func HandleDiceGridCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID

	log.Infof("收到骰子网格命令 - 群组ID: %d, 用户ID: %d", chatID, userID)

	// 获取游戏管理器
	manager := GetGameManager(chatID)

	// 发送骰子开奖网格
	manager.sendDiceGrid(userID)

	log.Infof("用户 %d 查看了骰子开奖网格 - 群组ID: %d", userID, chatID)

	return nil
}

// 处理开始游戏命令 /start --bankerName=@aritestbot001_bot --bankerID=********** --currency=ustd --periodMiddle=3785786
func HandleStartCommand(update *tgbotapi.Update) error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("HandleStartCommand panic: %v", r)
		}
	}()

	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID
	messageID := update.Message.MessageID

	log.Infof("收到开始游戏命令 - 群组ID: %d, 用户ID: %d, 群组类型: %s", chatID, userID, update.Message.Chat.Type)

	// 检查是否是群组
	if update.Message.Chat.Type == "private" {
		log.Warnf("用户 %d 尝试在私聊中启动游戏", userID)
		return sendBetErrorMessage(userID, "此功能只能在群组中使用")
	}

	// 获取游戏管理器
	manager := GetGameManager(chatID)
	log.Infof("获取游戏管理器成功 - 群组ID: %d", chatID)

	// 解析命令参数
	text := update.Message.Text
	parts := strings.Fields(text)

	// 检查是否有参数
	if len(parts) < 2 {
		return sendBetErrorMessageToGroup(chatID, "❌ 请指定参数\n\n格式:\n/start --bankerName=@用户名 --bankerID=用户ID --currency=货币类型 --periodMiddle=7位期号数字\n\n例如:\n/start --bankerName=@Gy1166\n/start --bankerName=@Gy1166 --periodMiddle=6537661\n/start --bankerName=@Gy1166 --bankerID=********* --periodMiddle=6537661 --currency=ustd\n\n注意：期号数字必须是7位数字", messageID)
	}

	// 解析标准参数格式
	params := parseStartCommandParams(parts[1:])

	// 验证必需参数
	if params.BankerName == "" {
		return sendBetErrorMessageToGroup(chatID, "❌ 缺少必需参数 --bankerName\n\n格式:\n/start --bankerName=@用户名 [其他参数]", messageID)
	}

	// 解析庄家参数
	bankerID, bankerName, err := resolveBankerParamWithParams(params.BankerName, chatID, update.Message.From, params.BankerID)
	if err != nil {
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ %s\n\n请使用有效的Telegram用户ID或@用户名", err.Error()), messageID)
	}

	// 设置庄家
	manager.CurrentBanker = bankerID
	manager.NextBanker = bankerID

	// 设置庄家
	manager.CurrentBanker = bankerID
	manager.NextBanker = bankerID

	// 解析货币类型参数（可选）
	if params.Currency != "" {
		currencyTypeStr := strings.ToLower(params.Currency)
		// 验证货币类型
		if configs.IsValidCurrencyType(currencyTypeStr) {
			currencyType := configs.CurrencyType(currencyTypeStr)
			manager.CurrencyType = currencyType
			manager.CurrencyUnit = configs.GetCurrencyUnit(currencyType)
			log.Infof("设置货币类型为: %s, 单位: %s - 群组ID: %d", currencyType, manager.CurrencyUnit, chatID)
		} else {
			log.Warnf("无效的货币类型: %s，使用默认货币类型 ustd - 群组ID: %d", currencyTypeStr, chatID)
		}
	}

	// 验证并设置期号前缀
	if params.PeriodMiddle != "" {
		// 验证期号前缀格式：必须是7位纯数字
		if len(params.PeriodMiddle) == 7 {
			if _, err := strconv.ParseInt(params.PeriodMiddle, 10, 64); err == nil {
				manager.PeriodPrefix = "K3" + params.PeriodMiddle
				log.Infof("设置期号前缀为: %s - 群组ID: %d", manager.PeriodPrefix, chatID)
			} else {
				log.Warnf("无效的期号前缀: %s（不是纯数字），使用默认前缀 K300000000 - 群组ID: %d", params.PeriodMiddle, chatID)
			}
		} else {
			log.Warnf("无效的期号前缀: %s（必须是7位数字），使用默认前缀 K300000000 - 群组ID: %d", params.PeriodMiddle, chatID)
		}
	}

	log.Infof("设置庄家为: %s (ID: %d) - 群组ID: %d", bankerName, bankerID, chatID)

	// 检查是否已有游戏在进行中
	if manager.CurrentGame != nil {
		currentState := manager.CurrentGame.CurrentState
		log.Infof("当前已有游戏在进行中，状态: %s - 群组ID: %d", currentState, chatID)
		return sendBetErrorMessageToGroup(chatID, "❌ 当前已有游戏在进行中，无法启动新游戏", messageID)
	}

	// 开始新游戏
	log.Infof("开始启动新游戏 - 群组ID: %d, 货币类型: %s", chatID, manager.CurrencyType)
	manager.StartNewGame()

	log.Infof("用户 %d 在群组 %d 中成功启动了新游戏，货币类型: %s", userID, chatID, manager.CurrencyType)

	return nil
}

// 处理游戏状态命令
func HandleStatusCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID

	// 获取游戏管理器
	manager := GetGameManager(chatID)

	// 发送游戏状态
	manager.sendGameStatusMessage(chatID)

	return nil
}

// 处理帮助命令
func HandleHelpCommand(update *tgbotapi.Update) error {
	userID := update.Message.From.ID

	helpText := configs.GetBetHelpText()

	msg := tgbotapi.NewMessage(userID, helpText)
	msg.ParseMode = "HTML"

	_, err := bot.Send(msg)
	if err != nil {
		log.Errorf("发送帮助消息失败: %v", err)
		return err
	}

	return nil
}

// 处理历史记录命令
func HandleHistoryCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID

	// 获取最近的历史记录
	db := mdb.Default()

	var results []models.TelegramK3DiceResult
	err := db.Where("chat_id = ?", chatID).
		Order("created_at DESC").
		Limit(10).
		Find(&results).Error

	if err != nil {
		log.Errorf("查询历史记录失败: %v", err)
		return sendBetErrorMessage(userID, "查询历史记录失败")
	}

	if len(results) == 0 {
		return sendBetErrorMessage(userID, "暂无历史记录")
	}

	var text strings.Builder
	text.WriteString("📊 最近10期开奖记录\n\n")

	for i, result := range results {
		text.WriteString(fmt.Sprintf("%d. 期号: %s\n", i+1, result.PeriodID[:8]))
		text.WriteString(fmt.Sprintf("   点数: %d+%d+%d=%d\n",
			result.Dice1, result.Dice2, result.Dice3, result.Sum))
		text.WriteString(fmt.Sprintf("   结果: %s\n", configs.GetBetTypeDisplayName(result.Result)))
		text.WriteString(fmt.Sprintf("   时间: %s\n\n",
			result.CreatedAt.Format("01-02 15:04")))
	}

	msg := tgbotapi.NewMessage(userID, text.String())
	msg.ParseMode = "HTML"

	_, err = bot.Send(msg)
	if err != nil {
		log.Errorf("发送历史记录失败: %v", err)
		return err
	}

	return nil
}

// 处理统计命令
func HandleStatsCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID

	// 获取统计数据
	db := mdb.Default()

	// 总游戏数
	var totalGames int64
	db.Model(&models.TelegramK3DiceResult{}).Where("chat_id = ?", chatID).Count(&totalGames)

	// 总下注数
	var totalBets int64
	db.Model(&models.TelegramK3BetRecord{}).Where("chat_id = ?", chatID).Count(&totalBets)

	// 总下注金额
	var totalBetAmount int64
	db.Model(&models.TelegramK3BetRecord{}).Where("chat_id = ?", chatID).Select("COALESCE(SUM(amount), 0)").Scan(&totalBetAmount)

	// 总派奖金额
	var totalWinAmount int64
	db.Model(&models.TelegramK3BetRecord{}).Where("chat_id = ? AND is_win = ?", chatID, true).Select("COALESCE(SUM(win_amount), 0)").Scan(&totalWinAmount)

	// 中奖次数
	var winCount int64
	db.Model(&models.TelegramK3BetRecord{}).Where("chat_id = ? AND is_win = ?", chatID, true).Count(&winCount)

	var text strings.Builder
	text.WriteString("📊 群组游戏统计\n\n")
	text.WriteString(fmt.Sprintf("🎲 总游戏数: %d\n", totalGames))
	text.WriteString(fmt.Sprintf("💰 总下注数: %d\n", totalBets))
	text.WriteString(fmt.Sprintf("💵 总下注金额: %d\n", totalBetAmount))
	text.WriteString(fmt.Sprintf("🎉 总派奖金额: %d\n", totalWinAmount))
	text.WriteString(fmt.Sprintf("🏆 中奖次数: %d\n", winCount))

	if totalBets > 0 {
		winRate := float64(winCount) / float64(totalBets) * 100
		text.WriteString(fmt.Sprintf("📈 中奖率: %.2f%%\n", winRate))
	}

	if totalBetAmount > 0 {
		profitRate := float64(totalWinAmount-totalBetAmount) / float64(totalBetAmount) * 100
		text.WriteString(fmt.Sprintf("💹 盈亏率: %.2f%%", profitRate))
	}

	msg := tgbotapi.NewMessage(userID, text.String())
	msg.ParseMode = "HTML"

	_, err := bot.Send(msg)
	if err != nil {
		log.Errorf("发送统计信息失败: %v", err)
		return err
	}

	return nil
}

// 处理诊断命令
func HandleDebugCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID

	log.Infof("收到诊断命令 - 群组ID: %d, 用户ID: %d", chatID, userID)

	// 检查bot状态
	bot := GetBot()
	if bot == nil {
		return sendBetErrorMessage(userID, "❌ Bot实例未设置")
	}

	// 检查数据库连接
	db := mdb.Default()
	if db == nil {
		return sendBetErrorMessage(userID, "❌ 数据库连接失败")
	}

	// 检查游戏管理器
	manager := GetGameManager(chatID)
	if manager == nil {
		return sendBetErrorMessage(userID, "❌ 游戏管理器创建失败")
	}

	// 发送诊断结果
	var text strings.Builder
	text.WriteString("🔍 系统诊断结果\n\n")
	text.WriteString("✅ Bot实例: 正常\n")
	text.WriteString("✅ 数据库连接: 正常\n")
	text.WriteString("✅ 游戏管理器: 正常\n")
	text.WriteString(fmt.Sprintf("📊 当前期数: %d\n", manager.PeriodCount))
	text.WriteString(fmt.Sprintf("🎮 当前游戏: %v\n", manager.CurrentGame != nil))

	if manager.CurrentGame != nil {
		text.WriteString(fmt.Sprintf("📋 游戏状态: %s\n", manager.CurrentGame.CurrentState))
	}

	msg := tgbotapi.NewMessage(userID, text.String())
	msg.ParseMode = "HTML"

	_, err := bot.Send(msg)
	if err != nil {
		log.Errorf("发送诊断结果失败: %v", err)
		return err
	}

	return nil
}

// 发送下注失败消息（独立函数）
func sendBetErrorMessage(userID int64, errorMsg string) error {
	text := fmt.Sprintf("❌ 下注失败\n%s", errorMsg)

	msg := tgbotapi.NewMessage(userID, text)
	msg.ParseMode = "HTML"

	_, err := bot.Send(msg)
	if err != nil {
		log.Errorf("发送下注失败消息失败: %v", err)
	}
	return err
}

// 发送下注失败消息到群组
func sendBetErrorMessageToGroup(chatID int64, errorMsg string, messageID int) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	msg := tgbotapi.NewMessage(chatID, errorMsg)
	msg.ReplyToMessageID = messageID
	_, err := bot.Send(msg)
	return err
}

// 处理内联键盘回调
func HandleCallbackQuery(update *tgbotapi.Update) error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("HandleCallbackQuery panic: %v", r)
		}
	}()

	callbackQuery := update.CallbackQuery
	chatID := callbackQuery.Message.Chat.ID
	userID := callbackQuery.From.ID
	userName := callbackQuery.From.UserName
	if userName == "" {
		userName = callbackQuery.From.FirstName
	}
	data := callbackQuery.Data

	log.Infof("收到内联键盘回调 - 群组ID: %d, 用户ID: %d, 用户名: %s, 数据: %s", chatID, userID, userName, data)

	// 处理不同的回调数据
	switch data {
	case "recharge_withdraw":
		return handleRechargeWithdrawCallback(callbackQuery)
	case "wallet_channel":
		return handleWalletChannelCallback(callbackQuery)
	case "wallet_service":
		return handleWalletServiceCallback(callbackQuery)
	case "xx_guarantee":
		return handleXXGuaranteeCallback(callbackQuery)
	case "copy_dice":
		return handleCopyDiceCallback(callbackQuery)
	case "example_button":
		return handleExampleButtonCallback(callbackQuery)
	default:
		log.Infof("未知的回调数据: %s", data)
		return fmt.Errorf("未知的回调数据: %s", data)
	}
}

// 处理充值提现回调
func handleRechargeWithdrawCallback(callbackQuery *tgbotapi.CallbackQuery) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	// 发送充值提现相关信息
	text := "💳 充值提现服务\n\n请联系客服进行充值或提现操作。"

	callback := tgbotapi.NewCallback(callbackQuery.ID, "充值提现")
	bot.Request(callback)

	msg := tgbotapi.NewMessage(callbackQuery.Message.Chat.ID, text)
	_, err := bot.Send(msg)
	return err
}

// 处理钱包频道回调
func handleWalletChannelCallback(callbackQuery *tgbotapi.CallbackQuery) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	// 发送钱包频道信息
	text := "📢 钱包频道\n\n关注我们的官方钱包频道获取最新资讯。"

	callback := tgbotapi.NewCallback(callbackQuery.ID, "钱包频道")
	bot.Request(callback)

	msg := tgbotapi.NewMessage(callbackQuery.Message.Chat.ID, text)
	_, err := bot.Send(msg)
	return err
}

// 处理钱包客服回调
func handleWalletServiceCallback(callbackQuery *tgbotapi.CallbackQuery) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	// 发送钱包客服信息
	text := "👨‍💼 钱包客服\n\n如有问题请联系我们的客服团队。"

	callback := tgbotapi.NewCallback(callbackQuery.ID, "钱包客服")
	bot.Request(callback)

	msg := tgbotapi.NewMessage(callbackQuery.Message.Chat.ID, text)
	_, err := bot.Send(msg)
	return err
}

// 处理XX担保回调
func handleXXGuaranteeCallback(callbackQuery *tgbotapi.CallbackQuery) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	// 发送XX担保信息
	text := "🛡️ XX担保\n\n我们提供安全可靠的担保服务。"

	callback := tgbotapi.NewCallback(callbackQuery.ID, "XX担保")
	bot.Request(callback)

	msg := tgbotapi.NewMessage(callbackQuery.Message.Chat.ID, text)
	_, err := bot.Send(msg)
	return err
}

// 处理复制骰子回调
func handleCopyDiceCallback(callbackQuery *tgbotapi.CallbackQuery) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	userID := callbackQuery.From.ID
	userName := callbackQuery.From.UserName
	if userName == "" {
		userName = callbackQuery.From.FirstName
	}

	log.Infof("用户 %s (ID: %d) 点击了复制骰子按钮", userName, userID)

	// 发送复制确认回调（这会显示"🎲"在按钮上）
	callback := tgbotapi.NewCallback(callbackQuery.ID, "🎲")
	bot.Request(callback)

	// 发送提示消息到用户（私信）
	text := "🎲 已复制到剪贴板，请粘贴到聊天窗口发送"
	msg := tgbotapi.NewMessage(callbackQuery.From.ID, text)
	_, err := bot.Send(msg)
	return err
}

// 处理🎲命令（复制投掷）
func HandleDiceCommand(update *tgbotapi.Update) error {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("HandleDiceCommand panic: %v", r)
		}
	}()

	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID
	userName := update.Message.From.UserName
	if userName == "" {
		userName = update.Message.From.FirstName
	}
	messageID := update.Message.MessageID

	log.Infof("收到🎲命令 - 群组ID: %d, 用户ID: %d, 用户名: %s", chatID, userID, userName)

	// 获取游戏管理器
	manager := GetGameManager(chatID)

	// 检查当前游戏状态
	if manager.CurrentGame == nil {
		return sendBetErrorMessageToGroup(chatID, "❌ 当前没有进行中的游戏", messageID)
	}

	if manager.CurrentGame.CurrentState != GameStateRolling {
		return sendBetErrorMessageToGroup(chatID, "❌ 当前不是掷骰子阶段", messageID)
	}

	// 检查是否是指定的掷骰子玩家
	// 只允许下注最高的玩家掷骰子
	maxBetUserID, maxBetUserName := manager.getMaxBetUser(manager.CurrentGame.Period.ID)
	if maxBetUserID == 0 {
		return sendBetErrorMessageToGroup(chatID, "❌ 没有下注玩家，无法掷骰子", messageID)
	}

	if userID != maxBetUserID {
		log.Infof("用户 %s (ID: %d) 尝试掷骰子，但下注最高的是 %s (ID: %d) - 群组ID: %d",
			userName, userID, maxBetUserName, maxBetUserID, chatID)
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ 只有下注最高的玩家 @%s 可以掷骰子", maxBetUserName), messageID)
	}

	log.Infof("下注最高玩家 %s (ID: %d) 开始掷骰子 - 群组ID: %d", userName, userID, chatID)

	// 检查是否是骰子消息
	if update.Message.Dice == nil {
		return sendBetErrorMessageToGroup(chatID, "❌ 请发送🎲表情符号来掷骰子", messageID)
	}

	// 检查是否已经掷满3次骰子
	if manager.DiceRollCount >= configs.GetConfig().MaxDiceRolls {
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ 已经掷满%d次骰子，无法继续掷骰子", configs.GetConfig().MaxDiceRolls), messageID)
	}

	// 从用户发送的骰子消息中获取结果
	diceValue := update.Message.Dice.Value
	log.Infof("用户掷骰子结果: %d - 群组ID: %d", diceValue, chatID)

	// 增加掷骰子计数器
	manager.DiceRollCount++
	log.Infof("用户掷骰子，当前掷骰子次数: %d/3 - 群组ID: %d", manager.DiceRollCount, chatID)

	// 保存当前骰子值到内存中
	period := manager.CurrentGame.Period
	if manager.DiceValues == nil {
		manager.DiceValues = make([]int, 0, configs.GetConfig().DiceCount)
	}
	manager.DiceValues = append(manager.DiceValues, diceValue)

	// 发送确认消息
	bot := GetBot()
	if bot == nil {
		log.Errorf("bot not initialized")
		return fmt.Errorf("bot not initialized")
	}

	remaining := 3 - manager.DiceRollCount
	if remaining > 0 {
		text := fmt.Sprintf("骰子有效，识别点数为: %d", diceValue)
		msg := tgbotapi.NewMessage(chatID, text)
		err := sendWithRetryConfig(func() (interface{}, error) {
			return bot.Send(msg)
		})
		if err != nil {
			log.Errorf("发送掷骰子确认消息失败: %v", err)
			return err
		}
	} else {
		// 已经掷满3次，计算最终结果
		if len(manager.DiceValues) == configs.GetConfig().DiceCount {
			dice1Value := manager.DiceValues[0]
			dice2Value := manager.DiceValues[1]
			dice3Value := manager.DiceValues[2]
			sum := dice1Value + dice2Value + dice3Value
			result := manager.calculateResult(dice1Value, dice2Value, dice3Value, sum)

			// 创建骰子结果
			diceResult := &models.TelegramK3DiceResult{
				PeriodID:  period.ID,
				ChatID:    chatID,
				UserID:    userID,
				UserName:  userName,
				PeriodNum: period.PeriodNum,
				Dice1:     dice1Value,
				Dice2:     dice2Value,
				Dice3:     dice3Value,
				Sum:       sum,
				Result:    result,
				CreatedAt: time.Now(),
			}

			// 保存骰子结果
			db := mdb.Default()
			err := db.Create(diceResult).Error
			if err != nil {
				log.Errorf("保存骰子结果失败: %v", err)
				return err
			}

			// 保存到内存
			manager.DiceResults[period.ID] = diceResult

			text := fmt.Sprintf("骰子有效，识别点数为: %d", dice3Value)
			msg := tgbotapi.NewMessage(chatID, text)
			err = sendWithRetryConfig(func() (interface{}, error) {
				return bot.Send(msg)
			})
			if err != nil {
				log.Errorf("发送掷骰子确认消息失败: %v", err)
				return err
			}

			log.Infof("用户 %s (ID: %d) 掷骰子完成: %d+%d+%d=%d, 开奖结果: %s - 群组ID: %d",
				userName, userID, dice1Value, dice2Value, dice3Value, sum, result, chatID)

			// 玩家掷骰子完成，立即触发结算
			log.Infof("玩家掷骰子完成，立即开始结算 - 群组ID: %d", chatID)

			// 停止当前定时器（如果还在运行）
			if manager.CurrentGame != nil && manager.CurrentGame.Timer != nil {
				manager.CurrentGame.Timer.Stop()
				log.Infof("停止掷骰子阶段定时器 - 群组ID: %d", chatID)
			}

			// 检查掷骰子阶段剩余时间，确保动画能完整播放
			rollingDuration := configs.GetConfig().RollingDuration
			animationCompleteDelay := configs.GetConfig().DiceAnimationCompleteDelay

			// 计算从掷骰子阶段开始到现在的时间
			elapsedTime := time.Since(period.StartTime.Add(configs.GetConfig().BettingDuration))
			remainingTime := rollingDuration - elapsedTime

			log.Infof("掷骰子阶段已进行: %v, 剩余时间: %v, 动画播放时间: %v - 群组ID: %d",
				elapsedTime, remainingTime, animationCompleteDelay, chatID)

			// 如果剩余时间不足以播放完整动画，延长等待时间
			waitTime := animationCompleteDelay
			if remainingTime < animationCompleteDelay {
				waitTime = animationCompleteDelay + (animationCompleteDelay - remainingTime)
				log.Infof("剩余时间不足，延长等待时间至: %v - 群组ID: %d", waitTime, chatID)
			}

			// 等待动画播放完成后再结算
			go func() {
				time.Sleep(waitTime)
				log.Infof("动画播放完成，开始结算 - 群组ID: %d", chatID)
				manager.settleAndShowResult(period)
			}()
		}
	}

	// 如果还没掷满，记录还需要掷多少次
	if manager.DiceRollCount < configs.GetConfig().MaxDiceRolls {
		remaining := configs.GetConfig().MaxDiceRolls - manager.DiceRollCount
		log.Infof("还需掷 %d 次骰子 - 群组ID: %d", remaining, chatID)
		// 如果玩家没有在剩余时间内掷骰子，机器人会补掷
	}

	return nil
}

// 处理示例按钮回调
func handleExampleButtonCallback(callbackQuery *tgbotapi.CallbackQuery) error {
	bot := GetBot()
	if bot == nil {
		return fmt.Errorf("bot not initialized")
	}

	userID := callbackQuery.From.ID
	userName := callbackQuery.From.UserName
	if userName == "" {
		userName = callbackQuery.From.FirstName
	}

	log.Infof("用户 %s (ID: %d) 点击了示例按钮", userName, userID)

	// 发送确认回调
	callback := tgbotapi.NewCallback(callbackQuery.ID, "已点击")
	bot.Request(callback)

	// 发送提示消息到用户（私信）
	text := "这是一个示例按钮的回调处理"
	msg := tgbotapi.NewMessage(callbackQuery.From.ID, text)
	_, err := bot.Send(msg)
	return err
}

// 处理抢庄命令
func HandleBankerCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID
	userName := update.Message.From.UserName
	if userName == "" {
		userName = update.Message.From.FirstName
	}

	manager := GetGameManager(chatID)
	if manager == nil {
		return fmt.Errorf("游戏管理器未初始化")
	}

	// 检查用户余额
	userBalance := manager.getUserBalance(userID, manager.CurrencyType)
	if userBalance <= 0 {
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ 余额不足，无法参与抢庄\n\n当前余额: %s", FormatCurrencyFloat(userBalance, manager.CurrencyUnit)), update.Message.MessageID)
	}

	// 进入抢庄模式
	manager.BankerMode = true
	manager.BankerCandidates[userID] = userName

	// 发送抢庄消息
	text := fmt.Sprintf("🎯 %s 发起抢庄！\n\n💰 当前余额: %s\n\n其他玩家可发送「我要上庄」参与抢庄\n\n钱最多的玩家将成为下一期庄家",
		userName, FormatCurrencyFloat(userBalance, manager.CurrencyUnit))

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"

	_, err := GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送抢庄消息失败: %v", err)
		return err
	}

	log.Infof("用户 %s (ID: %d) 发起抢庄 - 群组ID: %d", userName, userID, chatID)
	return nil
}

// 处理上庄命令
func HandleJoinBankerCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID
	userName := update.Message.From.UserName
	if userName == "" {
		userName = update.Message.From.FirstName
	}

	manager := GetGameManager(chatID)
	if manager == nil {
		return fmt.Errorf("游戏管理器未初始化")
	}

	// 检查是否在抢庄模式
	if !manager.BankerMode {
		return sendBetErrorMessageToGroup(chatID, "❌ 当前不在抢庄模式\n\n请先有人发起抢庄", update.Message.MessageID)
	}

	// 检查用户余额
	userBalance := manager.getUserBalance(userID, manager.CurrencyType)
	if userBalance <= 0 {
		return sendBetErrorMessageToGroup(chatID, fmt.Sprintf("❌ 余额不足，无法参与抢庄\n\n当前余额: %s", FormatCurrencyFloat(userBalance, manager.CurrencyUnit)), update.Message.MessageID)
	}

	// 添加到抢庄候选人
	manager.BankerCandidates[userID] = userName

	// 发送参与抢庄消息
	text := fmt.Sprintf("🎯 %s 参与抢庄！\n\n💰 当前余额: %s", userName, FormatCurrencyFloat(userBalance, manager.CurrencyUnit))

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"

	_, err := GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送参与抢庄消息失败: %v", err)
		return err
	}

	log.Infof("用户 %s (ID: %d) 参与抢庄 - 群组ID: %d", userName, userID, chatID)
	return nil
}

// 处理下庄命令
func HandleQuitBankerCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID
	userName := update.Message.From.UserName
	if userName == "" {
		userName = update.Message.From.FirstName
	}

	manager := GetGameManager(chatID)
	if manager == nil {
		return fmt.Errorf("游戏管理器未初始化")
	}

	// 检查是否是当前庄家
	if manager.CurrentBanker != userID {
		return sendBetErrorMessageToGroup(chatID, "❌ 只有当前庄家可以下庄", update.Message.MessageID)
	}

	// 设置下一期庄家为0（表示需要重新指定）
	manager.NextBanker = 0

	// 发送下庄消息
	text := fmt.Sprintf("👋 %s 申请下庄\n\n下一期将需要重新指定庄家", userName)

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"

	_, err := GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送下庄消息失败: %v", err)
		return err
	}

	log.Infof("庄家 %s (ID: %d) 申请下庄 - 群组ID: %d", userName, userID, chatID)
	return nil
}

// 结束抢庄模式并确定庄家
func (gm *GameManager) endBankerMode() {
	if !gm.BankerMode {
		return
	}

	// 找到余额最多的候选人
	var maxBalance float64
	var nextBankerID int64
	var nextBankerName string

	for userID, userName := range gm.BankerCandidates {
		balance := gm.getUserBalance(userID, gm.CurrencyType)
		if balance > maxBalance {
			maxBalance = balance
			nextBankerID = userID
			nextBankerName = userName
		}
	}

	// 设置下一期庄家
	if nextBankerID > 0 {
		gm.NextBanker = nextBankerID

		// 发送抢庄结果消息
		text := fmt.Sprintf("🎯 抢庄结束！\n\n🏆 下一期庄家: %s\n💰 余额: %s",
			nextBankerName, FormatCurrencyFloat(maxBalance, gm.CurrencyUnit))

		msg := tgbotapi.NewMessage(gm.ChatID, text)
		msg.ParseMode = "HTML"

		_, err := GetBot().Send(msg)
		if err != nil {
			log.Errorf("发送抢庄结果消息失败: %v", err)
		}

		log.Infof("抢庄结束，下一期庄家: %s (ID: %d) - 群组ID: %d", nextBankerName, nextBankerID, gm.ChatID)
	}

	// 重置抢庄模式
	gm.BankerMode = false
	gm.BankerCandidates = make(map[int64]string)
}

// 处理添加用户名映射命令
func HandleAddUserMappingCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID

	// 解析命令参数
	text := update.Message.Text
	parts := strings.Fields(text)

	// 格式: /adduser @username userID
	if len(parts) != 3 {
		return sendBetErrorMessageToGroup(chatID, "❌ 格式错误\n\n正确格式: /adduser @用户名 用户ID\n\n例如: /adduser @aritestbot001_bot *********", update.Message.MessageID)
	}

	// 解析用户名
	usernameParam := parts[1]
	if !strings.HasPrefix(usernameParam, "@") {
		return sendBetErrorMessageToGroup(chatID, "❌ 用户名格式错误\n\n请使用@用户名格式", update.Message.MessageID)
	}
	username := strings.TrimPrefix(usernameParam, "@")

	// 解析用户ID
	targetUserID, err := strconv.ParseInt(parts[2], 10, 64)
	if err != nil {
		return sendBetErrorMessageToGroup(chatID, "❌ 用户ID格式错误\n\n请使用有效的数字ID", update.Message.MessageID)
	}

	// 获取游戏管理器
	manager := GetGameManager(chatID)
	if manager == nil {
		return sendBetErrorMessageToGroup(chatID, "❌ 游戏管理器未初始化", update.Message.MessageID)
	}

	// 添加用户名映射
	manager.AddUsernameMapping(username, targetUserID)

	// 发送成功消息
	text = fmt.Sprintf("✅ 用户名映射添加成功\n\n用户名: @%s\n用户ID: %d", username, targetUserID)
	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"

	_, err = GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送添加映射成功消息失败: %v", err)
		return err
	}

	log.Infof("用户 %d 添加用户名映射: %s -> %d - 群组ID: %d", userID, username, targetUserID, chatID)
	return nil
}

// 处理查看用户名映射命令
func HandleListUserMappingsCommand(update *tgbotapi.Update) error {
	chatID := update.Message.Chat.ID
	userID := update.Message.From.ID

	// 获取游戏管理器
	manager := GetGameManager(chatID)
	if manager == nil {
		return sendBetErrorMessageToGroup(chatID, "❌ 游戏管理器未初始化", update.Message.MessageID)
	}

	// 获取所有映射
	mappings := manager.GetAllUsernameMappings()

	if len(mappings) == 0 {
		text := "📋 当前没有用户名映射记录"
		msg := tgbotapi.NewMessage(userID, text)
		msg.ParseMode = "HTML"

		_, err := GetBot().Send(msg)
		if err != nil {
			log.Errorf("发送映射列表消息失败: %v", err)
			return err
		}
		return nil
	}

	// 构建映射列表
	var text strings.Builder
	text.WriteString("📋 用户名映射列表\n\n")

	for username, userID := range mappings {
		text.WriteString(fmt.Sprintf("@%s -> %d\n", username, userID))
	}

	msg := tgbotapi.NewMessage(userID, text.String())
	msg.ParseMode = "HTML"

	_, err := GetBot().Send(msg)
	if err != nil {
		log.Errorf("发送映射列表消息失败: %v", err)
		return err
	}

	log.Infof("用户 %d 查看用户名映射列表 - 群组ID: %d", userID, chatID)
	return nil
}
