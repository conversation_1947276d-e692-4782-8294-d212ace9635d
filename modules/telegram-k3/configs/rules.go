package configs

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
)

var BetOdds map[string]float64
var BetLimits map[string]int64

// InitRules 初始化赔率和限额规则
func InitRules() {
	config := GetConfig()

	// 初始化赔率配置 - 从配置中读取
	BetOdds = map[string]float64{
		// 大小单双 - 从配置读取
		"big":   getOddsFromConfig("Basic", 1.97),
		"small": getOddsFromConfig("Basic", 1.97),
		"odd":   getOddsFromConfig("Basic", 1.97),
		"even":  getOddsFromConfig("Basic", 1.97),

		// 组合 - 从配置读取
		"big_odd":    getOddsFromConfig("BigOdd", 3.4),
		"small_odd":  getOddsFromConfig("SmallOdd", 4.4),
		"big_even":   getOddsFromConfig("BigEven", 4.4),
		"small_even": getOddsFromConfig("SmallEven", 3.4),

		// 特殊 - 从配置读取
		"leopard":  getOddsFromConfig("Leopard", 32.0),
		"straight": getOddsFromConfig("Straight", 8.0),
		"pair":     getOddsFromConfig("Pair", 2.1),

		// 指定豹子 - 从配置读取
		"leopard_1": getOddsFromConfig("LeopardSpecific", 166.0),
		"leopard_2": getOddsFromConfig("LeopardSpecific", 166.0),
		"leopard_3": getOddsFromConfig("LeopardSpecific", 166.0),
		"leopard_4": getOddsFromConfig("LeopardSpecific", 166.0),
		"leopard_5": getOddsFromConfig("LeopardSpecific", 166.0),
		"leopard_6": getOddsFromConfig("LeopardSpecific", 166.0),

		// 定位胆 - 从配置读取
		"special_4":  getOddsFromConfig("Special4", 58.0),
		"special_5":  getOddsFromConfig("Special5", 28.0),
		"special_6":  getOddsFromConfig("Special6", 16.0),
		"special_7":  getOddsFromConfig("Special7", 12.0),
		"special_8":  getOddsFromConfig("Special8", 8.0),
		"special_9":  getOddsFromConfig("Special9", 7.0),
		"special_10": getOddsFromConfig("Special10", 7.0),
		"special_11": getOddsFromConfig("Special11", 6.0),
		"special_12": getOddsFromConfig("Special12", 6.0),
		"special_13": getOddsFromConfig("Special13", 8.0),
		"special_14": getOddsFromConfig("Special14", 12.0),
		"special_15": getOddsFromConfig("Special15", 16.0),
		"special_16": getOddsFromConfig("Special16", 28.0),
		"special_17": getOddsFromConfig("Special17", 58.0),
	}

	// 初始化限额配置 - 使用配置的分类限额
	BetLimits = map[string]int64{
		// 大小单双 - 使用配置的分类限额
		"big":   config.BasicBetLimit,
		"small": config.BasicBetLimit,
		"odd":   config.BasicBetLimit,
		"even":  config.BasicBetLimit,

		// 组合 - 使用配置的分类限额
		"big_odd":    config.CombinationBetLimit,
		"small_odd":  config.CombinationBetLimit,
		"big_even":   config.CombinationBetLimit,
		"small_even": config.CombinationBetLimit,

		// 特殊 - 使用配置的分类限额
		"leopard":  config.SpecialBetLimit,
		"straight": config.SpecialBetLimit,
		"pair":     config.SpecialBetLimit,

		// 指定豹子 - 使用配置的分类限额
		"leopard_1": config.SpecialBetLimit,
		"leopard_2": config.SpecialBetLimit,
		"leopard_3": config.SpecialBetLimit,
		"leopard_4": config.SpecialBetLimit,
		"leopard_5": config.SpecialBetLimit,
		"leopard_6": config.SpecialBetLimit,

		// 定位胆 - 使用配置的分类限额
		"special_4":  config.SpecialBetLimit,
		"special_5":  config.SpecialBetLimit,
		"special_6":  config.SpecialBetLimit,
		"special_7":  config.SpecialBetLimit,
		"special_8":  config.SpecialBetLimit,
		"special_9":  config.SpecialBetLimit,
		"special_10": config.SpecialBetLimit,
		"special_11": config.SpecialBetLimit,
		"special_12": config.SpecialBetLimit,
		"special_13": config.SpecialBetLimit,
		"special_14": config.SpecialBetLimit,
		"special_15": config.SpecialBetLimit,
		"special_16": config.SpecialBetLimit,
		"special_17": config.SpecialBetLimit,
	}
}

// getOddsFromConfig 从配置中获取赔率，如果配置不存在则使用默认值
func getOddsFromConfig(configKey string, defaultValue float64) float64 {
	configValue := conf.Str(fmt.Sprintf("telegramk3.odds.%s", configKey), "")
	if configValue == "" {
		return defaultValue
	}

	if value, err := strconv.ParseFloat(configValue, 64); err == nil {
		log.Infof("设置赔率 %s: %.2f->%.2f", configKey, defaultValue, value)
		return value
	}

	log.Warnf("赔率配置 %s 格式错误: %s，使用默认值: %.2f", configKey, configValue, defaultValue)
	return defaultValue
}

// GetBetTypeDisplayName 获取下注类型的显示名称
func GetBetTypeDisplayName(betType string) string {
	displayNames := map[string]string{
		// 大小单双
		"big":   "大",
		"small": "小",
		"odd":   "单",
		"even":  "双",

		// 组合
		"big_odd":    "大单",
		"big_even":   "大双",
		"small_odd":  "小单",
		"small_even": "小双",

		// 特殊
		"leopard":  "豹子",
		"straight": "顺子",
		"pair":     "对子",

		// 指定豹子
		"leopard_1": "豹子1",
		"leopard_2": "豹子2",
		"leopard_3": "豹子3",
		"leopard_4": "豹子4",
		"leopard_5": "豹子5",
		"leopard_6": "豹子6",
	}

	// 处理定位胆
	if strings.HasPrefix(betType, "special_") {
		parts := strings.Split(betType, "_")
		if len(parts) == 2 {
			return fmt.Sprintf("定位胆%s", parts[1])
		}
	}

	if name, exists := displayNames[betType]; exists {
		return name
	}

	return betType
}

// GetBetHelpText 获取下注帮助信息
func GetBetHelpText() string {
	return `🎲 手摇快三下注格式说明

📝 组合下注:
• 简写: dd10 ds10 xd10 xs10
• 全称: 大单10 大双10 小单10 小双10

🎯 高倍下注:
• 简写: bz1 10 bz2 10
• 全称: 豹子1 10 豹子2 10

🎲 特码下注:
• 定位胆4 10
• dwd4 10  
• 4y10

📊 基础下注:
• 简写: da100 x100 dan100 s100
• 全称: 大100 小100 单100 双100

💡 单次下注示例:
dd10     (大单10)
bz1 50   (豹子1 50)
定位胆4 20 (定位胆4 20)

💡 多种下注示例:

1. 换行分隔:
dd10
ds20
bz1 50

2. 分号分隔:
dd10; ds20; bz1 50

3. 无分隔符:
dd10ds20bz150

4. 混合格式:
大单10
大双20
豹子1 50

💡 无分隔符格式示例:
dd10ds20bz150     (大单10+大双20+豹子1 50)
大单10大双20豹子150   (大单10+大双20+豹子1 50)
dd10ds20bz150定位胆430 (大单10+大双20+豹子1 50+定位胆4 30)`
}
