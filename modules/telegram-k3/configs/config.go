package configs

import (
	"strconv"
	"strings"
	"time"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/log"
)

/*
重点问题：
1. 确保仅在下注阶段接受玩家下注消息
2. 确保仅在掷骰子阶段接受最高下注玩家的掷骰子消息
3. 确保玩家下注成功冻结玩家下注金额
4. 确保玩家下注时庄家余额赔付不会溢出
5. 确保结算输家正确扣除余额重置冻结金额
6. 确保结算时赢家正确赔付重置冻结金额
7. 确保结算时正确更新庄家余额
*/

// GameConfig 游戏配置结构
// 包含游戏运行所需的所有配置参数，支持通过环境变量动态配置
type GameConfig struct {
	// ==================== 时间配置 ====================
	// 游戏各阶段的时长和延迟设置，支持 Go 的 time.Duration 格式

	// BettingDuration 下注阶段时长
	// 玩家可以在此时间段内进行下注操作
	BettingDuration time.Duration

	// RollingDuration 掷骰子阶段时长
	// 有玩家下注时，等待玩家掷骰子的时间，超时后机器人补掷
	RollingDuration time.Duration

	// NextGameDelay 下一局游戏开始前的延迟
	// 当前局结束后，等待此时间再开始下一局
	NextGameDelay time.Duration

	// MessageRetryDelay 消息发送失败时的重试延迟
	// 当消息发送失败时，等待此时间后重试
	MessageRetryDelay time.Duration

	// MessageRetryCount 消息发送失败时的最大重试次数
	// 当消息发送失败时，最多重试此次数
	MessageRetryCount int

	// BotRollDelay 无人下注时机器人掷骰子的延迟
	// 无人下注时，机器人等待此时间后开始掷骰子
	BotRollDelay time.Duration

	// SettlementDelay 玩家掷满3次骰子后的结算延迟
	// 玩家完成3次掷骰子后，等待此时间再开奖
	SettlementDelay time.Duration

	// DiceAnimationDelay 机器人发送多个骰子动画之间的间隔
	// 机器人需要掷多个骰子时，每次发送动画的间隔时间
	DiceAnimationDelay time.Duration

	// DiceAnimationCompleteDelay 骰子动画播放完成后的等待时间
	// 等待Telegram骰子动画完全播放完成的时间
	DiceAnimationCompleteDelay time.Duration

	// MaxPeriods 最大期数限制（根据网格配置自动计算）
	// 游戏运行的最大期数，达到后重新开始
	// 计算公式: GridWidth × (GridHeight - 1)
	MaxPeriods int

	// ==================== 消息配置 ====================

	// MessageSenderID 消息发送者ID
	// Telegram机器人的用户ID，用于发送消息
	MessageSenderID int64

	// ==================== 网格配置 ====================
	// 开奖结果网格的显示配置

	// GridWidth 网格横向大小（列数）
	// 开奖网格的宽度，决定每行显示多少个开奖结果
	GridWidth int

	// GridHeight 网格纵向大小（行数）
	// 开奖网格的高度，决定显示多少行开奖结果
	GridHeight int

	// 需要在统计行显示出现次数的特定数字，如 [3, 18] 表示统计数字3和18的出现次数
	StatsNumbers []int

	// ==================== 游戏规则配置 ====================
	// 游戏规则相关的配置（根据骰子配置自动计算）

	// BigMin 大的最小值（自动计算）
	BigMin int

	// BigMax 大的最大值（自动计算）
	BigMax int

	// SmallMin 小的最小值（自动计算）
	SmallMin int

	// SmallMax 小的最大值（自动计算）
	SmallMax int

	// ==================== 骰子配置 ====================
	// 骰子相关的配置

	// DiceCount 骰子数量
	DiceCount int

	// DiceMinValue 骰子最小点数
	DiceMinValue int

	// DiceMaxValue 骰子最大点数
	DiceMaxValue int

	// MaxDiceRolls 最大掷骰子次数
	MaxDiceRolls int

	// ==================== 分类限额配置 ====================
	// 按玩法分类的限额配置

	// BasicBetLimit 大小单双限额
	BasicBetLimit int64

	// CombinationBetLimit 组合玩法限额
	CombinationBetLimit int64

	// SpecialBetLimit 特殊玩法限额
	SpecialBetLimit int64

	// ==================== 总体限额配置 ====================
	// TotalBetLimit 用户单期总下注限额
	TotalBetLimit int64

	// ==================== 底注配置 ====================
	// BaseBet 底注金额
	BaseBet int64
}

// DefaultConfig 默认配置
var DefaultConfig = GameConfig{
	BettingDuration:            30 * time.Second,
	RollingDuration:            25 * time.Second,
	NextGameDelay:              1 * time.Second,
	MessageRetryDelay:          2 * time.Second,
	MessageRetryCount:          3,
	BotRollDelay:               3 * time.Second,
	SettlementDelay:            1 * time.Second,
	DiceAnimationDelay:         500 * time.Millisecond,
	DiceAnimationCompleteDelay: 4500 * time.Millisecond,
	// 最大期数将在LoadConfig中根据网格配置自动计算
	MessageSenderID: 7637060277,
	GridWidth:       14,
	GridHeight:      7,
	StatsNumbers:    []int{3, 18},
	// 游戏规则配置将在LoadConfig中根据骰子配置自动计算
	DiceCount:           3,     // 骰子数量
	DiceMinValue:        1,     // 骰子最小点数
	DiceMaxValue:        6,     // 骰子最大点数
	MaxDiceRolls:        3,     // 最大掷骰子次数
	BasicBetLimit:       2000,  // 大小单双限额
	CombinationBetLimit: 500,   // 组合玩法限额
	SpecialBetLimit:     250,   // 特殊玩法限额
	TotalBetLimit:       10000, // 用户单期总下注限额
	BaseBet:             1,     // 底注金额
}

// CurrentConfig 当前配置
// 运行时使用的实际配置，初始化为默认配置，可通过环境变量或代码动态修改
var CurrentConfig = DefaultConfig

// LoadConfig 从环境变量加载配置
// 读取系统环境变量，覆盖默认配置值
// 支持所有配置项的环境变量设置，格式为 BETTING_配置项名
func LoadConfig() {
	if duration := conf.Str("telegramk3.BettingDuration", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置下注时长: %v->%v", CurrentConfig.BettingDuration, d)
			CurrentConfig.BettingDuration = d
		}
	}

	if duration := conf.Str("telegramk3.RollingDuration", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置掷骰子时长: %v->%v", CurrentConfig.RollingDuration, d)
			CurrentConfig.RollingDuration = d
		}
	}

	if duration := conf.Str("telegramk3.NextGameDelay", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置下一局延迟: %v->%v", CurrentConfig.NextGameDelay, d)
			CurrentConfig.NextGameDelay = d
		}
	}

	if duration := conf.Str("telegramk3.MessageRetryDelay", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置消息重试延迟: %v->%v", CurrentConfig.MessageRetryDelay, d)
			CurrentConfig.MessageRetryDelay = d
		}
	}

	if countStr := conf.Str("telegramk3.MessageRetryCount", ""); countStr != "" {
		if count, err := strconv.Atoi(countStr); err == nil && count > 0 {
			log.Infof("设置消息重试次数: %d->%d", CurrentConfig.MessageRetryCount, count)
			CurrentConfig.MessageRetryCount = count
		}
	}

	if duration := conf.Str("telegramk3.BotRollDelay", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置机器人掷骰子延迟: %v->%v", CurrentConfig.BotRollDelay, d)
			CurrentConfig.BotRollDelay = d
		}
	}

	if duration := conf.Str("telegramk3.SettlementDelay", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置结算延迟: %v->%v", CurrentConfig.SettlementDelay, d)
			CurrentConfig.SettlementDelay = d
		}
	}

	if duration := conf.Str("telegramk3.DiceAnimationDelay", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置骰子动画延迟: %v->%v", CurrentConfig.DiceAnimationDelay, d)
			CurrentConfig.DiceAnimationDelay = d
		}
	}

	if duration := conf.Str("telegramk3.DiceAnimationCompleteDelay", ""); duration != "" {
		if d, err := time.ParseDuration(duration); err == nil {
			log.Infof("设置骰子动画完成延迟: %v->%v", CurrentConfig.DiceAnimationCompleteDelay, d)
			CurrentConfig.DiceAnimationCompleteDelay = d
		}
	}

	// 消息发送者ID
	if senderID := conf.Str("telegramk3.MessageSenderID", ""); senderID != "" {
		if id, err := strconv.ParseInt(senderID, 10, 64); err == nil {
			log.Infof("设置消息发送者ID: %d->%d", CurrentConfig.MessageSenderID, id)
			CurrentConfig.MessageSenderID = id
			SetMessageSenderID(id)
		}
	}

	// 最大期数
	if maxPeriods := conf.Str("telegramk3.MaxPeriods", ""); maxPeriods != "" {
		if periods, err := strconv.Atoi(maxPeriods); err == nil {
			log.Infof("设置最大期数: %d->%d", CurrentConfig.MaxPeriods, periods)
			CurrentConfig.MaxPeriods = periods
		}
	}

	// 网格配置
	if gridWidth := conf.Str("telegramk3.GridWidth", ""); gridWidth != "" {
		if width, err := strconv.Atoi(gridWidth); err == nil {
			log.Infof("设置网格宽度: %d->%d", CurrentConfig.GridWidth, width)
			CurrentConfig.GridWidth = width
		}
	}

	if gridHeight := conf.Str("telegramk3.GridHeight", ""); gridHeight != "" {
		if height, err := strconv.Atoi(gridHeight); err == nil {
			log.Infof("设置网格高度: %d->%d", CurrentConfig.GridHeight, height)
			CurrentConfig.GridHeight = height
		}
	}

	// 统计数字配置
	if statsNumbers := conf.Str("telegramk3.StatsNumbers", ""); statsNumbers != "" {
		numbers := parseStatsNumbers(statsNumbers)
		log.Infof("设置统计数字: %v->%v", CurrentConfig.StatsNumbers, numbers)
		CurrentConfig.StatsNumbers = numbers
	}

	// 游戏规则配置
	if bigMin := conf.Str("telegramk3.BigMin", ""); bigMin != "" {
		if min, err := strconv.Atoi(bigMin); err == nil {
			log.Infof("设置大的最小值: %d->%d", CurrentConfig.BigMin, min)
			CurrentConfig.BigMin = min
		}
	}

	if bigMax := conf.Str("telegramk3.BigMax", ""); bigMax != "" {
		if max, err := strconv.Atoi(bigMax); err == nil {
			log.Infof("设置大的最大值: %d->%d", CurrentConfig.BigMax, max)
			CurrentConfig.BigMax = max
		}
	}

	if smallMin := conf.Str("telegramk3.SmallMin", ""); smallMin != "" {
		if min, err := strconv.Atoi(smallMin); err == nil {
			log.Infof("设置小的最小值: %d->%d", CurrentConfig.SmallMin, min)
			CurrentConfig.SmallMin = min
		}
	}

	if smallMax := conf.Str("telegramk3.SmallMax", ""); smallMax != "" {
		if max, err := strconv.Atoi(smallMax); err == nil {
			log.Infof("设置小的最大值: %d->%d", CurrentConfig.SmallMax, max)
			CurrentConfig.SmallMax = max
		}
	}

	// 骰子配置
	if diceCount := conf.Str("telegramk3.DiceCount", ""); diceCount != "" {
		if count, err := strconv.Atoi(diceCount); err == nil && count > 0 {
			log.Infof("设置骰子数量: %d->%d", CurrentConfig.DiceCount, count)
			CurrentConfig.DiceCount = count
		}
	}

	if diceMinValue := conf.Str("telegramk3.DiceMinValue", ""); diceMinValue != "" {
		if min, err := strconv.Atoi(diceMinValue); err == nil && min > 0 {
			log.Infof("设置骰子最小点数: %d->%d", CurrentConfig.DiceMinValue, min)
			CurrentConfig.DiceMinValue = min
		}
	}

	if diceMaxValue := conf.Str("telegramk3.DiceMaxValue", ""); diceMaxValue != "" {
		if max, err := strconv.Atoi(diceMaxValue); err == nil && max > 0 {
			log.Infof("设置骰子最大点数: %d->%d", CurrentConfig.DiceMaxValue, max)
			CurrentConfig.DiceMaxValue = max
		}
	}

	if maxDiceRolls := conf.Str("telegramk3.MaxDiceRolls", ""); maxDiceRolls != "" {
		if rolls, err := strconv.Atoi(maxDiceRolls); err == nil && rolls > 0 {
			log.Infof("设置最大掷骰子次数: %d->%d", CurrentConfig.MaxDiceRolls, rolls)
			CurrentConfig.MaxDiceRolls = rolls
		}
	}

	// 分类限额配置
	if basicLimit := conf.Str("telegramk3.BasicBetLimit", ""); basicLimit != "" {
		if limit, err := strconv.ParseInt(basicLimit, 10, 64); err == nil {
			log.Infof("设置大小单双限额: %d->%d", CurrentConfig.BasicBetLimit, limit)
			CurrentConfig.BasicBetLimit = limit
		}
	}

	if combinationLimit := conf.Str("telegramk3.CombinationBetLimit", ""); combinationLimit != "" {
		if limit, err := strconv.ParseInt(combinationLimit, 10, 64); err == nil {
			log.Infof("设置组合玩法限额: %d->%d", CurrentConfig.CombinationBetLimit, limit)
			CurrentConfig.CombinationBetLimit = limit
		}
	}

	if specialLimit := conf.Str("telegramk3.SpecialBetLimit", ""); specialLimit != "" {
		if limit, err := strconv.ParseInt(specialLimit, 10, 64); err == nil {
			log.Infof("设置特殊玩法限额: %d->%d", CurrentConfig.SpecialBetLimit, limit)
			CurrentConfig.SpecialBetLimit = limit
		}
	}

	if totalBetLimit := conf.Str("telegramk3.TotalBetLimit", ""); totalBetLimit != "" {
		if limit, err := strconv.ParseInt(totalBetLimit, 10, 64); err == nil {
			log.Infof("设置用户单期总下注限额: %d->%d", CurrentConfig.TotalBetLimit, limit)
			CurrentConfig.TotalBetLimit = limit
		}
	}

	if baseBet := conf.Str("telegramk3.BaseBet", ""); baseBet != "" {
		if bet, err := strconv.ParseInt(baseBet, 10, 64); err == nil {
			log.Infof("设置底注金额: %d->%d", CurrentConfig.BaseBet, bet)
			CurrentConfig.BaseBet = bet
		}
	}

	InitRules()

	// 根据骰子配置自动计算游戏规则
	calculateGameRules()

	// 根据网格配置自动计算最大期数
	calculateMaxPeriods()
}

// 获取当前配置
func GetConfig() GameConfig {
	return CurrentConfig
}

// 设置配置
func SetConfig(config GameConfig) {
	CurrentConfig = config
	log.Info("游戏配置已更新")
}

// 设置消息发送者ID
func SetMessageSenderID(senderID int64) {
	CurrentConfig.MessageSenderID = senderID
	log.Infof("设置消息发送者ID: %d", senderID)
}

// calculateGameRules 根据骰子配置自动计算游戏规则
func calculateGameRules() {
	config := &CurrentConfig

	// 计算最小和最大总和
	minSum := config.DiceCount * config.DiceMinValue
	maxSum := config.DiceCount * config.DiceMaxValue

	// 计算大小分界点
	midPoint := float64(minSum+maxSum) / 2.0

	// 设置小的范围（向下取整）
	config.SmallMin = minSum
	config.SmallMax = int(midPoint)

	// 设置大的范围（向上取整）
	config.BigMin = int(midPoint) + 1
	config.BigMax = maxSum

	log.Infof("根据骰子配置自动计算游戏规则: 骰子%d个[%d-%d], 总和[%d-%d], 小[%d-%d], 大[%d-%d]",
		config.DiceCount, config.DiceMinValue, config.DiceMaxValue,
		minSum, maxSum, config.SmallMin, config.SmallMax, config.BigMin, config.BigMax)
}

// calculateMaxPeriods 根据网格配置自动计算最大期数
func calculateMaxPeriods() {
	config := &CurrentConfig

	// 计算最大期数: 网格宽度 × (网格高度 - 1)
	// 减1是因为最后一行用于统计信息
	maxPeriods := config.GridWidth * (config.GridHeight - 1)

	config.MaxPeriods = maxPeriods

	log.Infof("根据网格配置自动计算最大期数: 网格%d×%d, 最大期数: %d (最后一行用于统计)",
		config.GridWidth, config.GridHeight, maxPeriods)
}

// 解析统计数字字符串
func parseStatsNumbers(numbersStr string) []int {
	if numbersStr == "" {
		return []int{3, 18}
	}

	parts := strings.Split(numbersStr, ",")
	var numbers []int
	for _, part := range parts {
		if num, err := strconv.Atoi(strings.TrimSpace(part)); err == nil {
			numbers = append(numbers, num)
		}
	}
	return numbers
}

// 下注类型映射表 - 用于消息识别
var betTypeMapping = map[string]string{
	// 大小单双 - 简写
	"da":  "big",   // 大
	"x":   "small", // 小
	"dan": "odd",   // 单
	"s":   "even",  // 双

	// 大小单双 - 全称
	"大": "big",
	"小": "small",
	"单": "odd",
	"双": "even",

	// 组合 - 简写
	"dd": "big_odd",    // 大单
	"ds": "big_even",   // 大双
	"xd": "small_odd",  // 小单
	"xs": "small_even", // 小双

	// 组合 - 全称
	"大单": "big_odd",
	"大双": "big_even",
	"小单": "small_odd",
	"小双": "small_even",

	// 特殊 - 简写
	"bz": "leopard",  // 豹子
	"dz": "straight", // 顺子
	"sz": "pair",     // 对子

	// 特殊 - 全称
	"豹子": "leopard",
	"顺子": "straight",
	"对子": "pair",

	// 指定豹子 - 简写
	"bz1": "leopard_1", // 豹子1
	"bz2": "leopard_2", // 豹子2
	"bz3": "leopard_3", // 豹子3
	"bz4": "leopard_4", // 豹子4
	"bz5": "leopard_5", // 豹子5
	"bz6": "leopard_6", // 豹子6

	// 指定豹子 - 全称
	"豹子1": "leopard_1",
	"豹子2": "leopard_2",
	"豹子3": "leopard_3",
	"豹子4": "leopard_4",
	"豹子5": "leopard_5",
	"豹子6": "leopard_6",

	// 定位胆 - 全称格式
	"定位胆4":  "special_4",
	"定位胆5":  "special_5",
	"定位胆6":  "special_6",
	"定位胆7":  "special_7",
	"定位胆8":  "special_8",
	"定位胆9":  "special_9",
	"定位胆10": "special_10",
	"定位胆11": "special_11",
	"定位胆12": "special_12",
	"定位胆13": "special_13",
	"定位胆14": "special_14",
	"定位胆15": "special_15",
	"定位胆16": "special_16",
	"定位胆17": "special_17",

	// 定位胆 - 简写格式
	"dwd4":  "special_4",
	"dwd5":  "special_5",
	"dwd6":  "special_6",
	"dwd7":  "special_7",
	"dwd8":  "special_8",
	"dwd9":  "special_9",
	"dwd10": "special_10",
	"dwd11": "special_11",
	"dwd12": "special_12",
	"dwd13": "special_13",
	"dwd14": "special_14",
	"dwd15": "special_15",
	"dwd16": "special_16",
	"dwd17": "special_17",

	// 定位胆 - 数字格式 (4y, 5y, etc.)
	"4y":  "special_4",
	"5y":  "special_5",
	"6y":  "special_6",
	"7y":  "special_7",
	"8y":  "special_8",
	"9y":  "special_9",
	"10y": "special_10",
	"11y": "special_11",
	"12y": "special_12",
	"13y": "special_13",
	"14y": "special_14",
	"15y": "special_15",
	"16y": "special_16",
	"17y": "special_17",
}

// IsBettingMessage 检查是否是下注消息
// 通过配置的赔率表来判断消息是否包含有效的下注类型
func IsBettingMessage(text string) bool {
	// 移除空白字符
	text = strings.TrimSpace(text)

	// 如果为空，不是下注消息
	if text == "" {
		return false
	}

	// 如果以/开头，不是下注消息（这些是命令）
	if strings.HasPrefix(text, "/") {
		return false
	}

	// 检查是否包含数字（金额）
	if !strings.ContainsAny(text, "0123456789") {
		return false
	}

	// 检查是否包含下注相关的关键词
	for keyword := range betTypeMapping {
		if strings.Contains(text, keyword) {
			// 验证该下注类型是否有对应的赔率配置
			betType := betTypeMapping[keyword]
			if _, exists := BetOdds[betType]; exists {
				return true
			}
		}
	}

	return false
}

// GetBetOdds 获取指定下注类型的赔率
func GetBetOdds(betType string) float64 {
	if odds, exists := BetOdds[betType]; exists {
		return odds
	}
	return 0.0
}

// GetBetTypeMapping 获取下注类型映射表
func GetBetTypeMapping() map[string]string {
	return betTypeMapping
}

// GetBetLimit 获取指定下注类型的限额
func GetBetLimit(betType string) int64 {
	if limit, exists := BetLimits[betType]; exists {
		return limit
	}
	return 0
}

// GetBetLimitByCategory 根据下注类型获取对应的限额
func GetBetLimitByCategory(betType string) int64 {
	// 根据下注类型分类获取限额
	switch {
	case betType == "big" || betType == "small" || betType == "odd" || betType == "even":
		return GetConfig().BasicBetLimit // 大小单双
	case betType == "big_odd" || betType == "small_odd" || betType == "big_even" || betType == "small_even":
		return GetConfig().CombinationBetLimit // 组合
	case strings.HasPrefix(betType, "leopard") || betType == "straight" || betType == "pair" || strings.HasPrefix(betType, "special"):
		return GetConfig().SpecialBetLimit // 特殊玩法
	default:
		return 0
	}
}
