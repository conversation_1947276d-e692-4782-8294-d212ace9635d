package configs

// CurrencyType 货币类型枚举
type CurrencyType string

const (
	// 支持的货币类型
	CurrencyUSTD CurrencyType = "ustd" // USDT (Tether)
	CurrencyUSDT CurrencyType = "usdt" // USDT (Tether) - 别名
	CurrencyBTC  CurrencyType = "btc"  // Bitcoin
	CurrencyETH  CurrencyType = "eth"  // Ethereum
	CurrencyTRX  CurrencyType = "trx"  // TRON
)

// CurrencyUnit 货币单位枚举
type CurrencyUnit string

const (
	// 对应的货币单位
	UnitU   CurrencyUnit = "u"   // USDT/USDT 单位
	UnitBTC CurrencyUnit = "btc" // Bitcoin 单位
	UnitETH CurrencyUnit = "eth" // Ethereum 单位
	UnitTRX CurrencyUnit = "trx" // TRON 单位
)

// currencyUnitMap 货币类型到单位的映射
var currencyUnitMap = map[CurrencyType]CurrencyUnit{
	CurrencyUSTD: UnitU,
	CurrencyUSDT: UnitU,
	CurrencyBTC:  UnitBTC,
	CurrencyETH:  UnitETH,
	CurrencyTRX:  UnitTRX,
}

// GetCurrencyUnit 根据货币类型获取对应的单位
func GetCurrencyUnit(currencyType CurrencyType) CurrencyUnit {
	if unit, exists := currencyUnitMap[currencyType]; exists {
		return unit
	}
	// 默认返回 USDT 单位
	return UnitU
}

// GetCurrencyUnitString 根据货币类型获取对应的单位字符串
func GetCurrencyUnitString(currencyType CurrencyType) string {
	return string(GetCurrencyUnit(currencyType))
}

// IsValidCurrencyType 检查货币类型是否有效
func IsValidCurrencyType(currencyType string) bool {
	_, exists := currencyUnitMap[CurrencyType(currencyType)]
	return exists
}

// GetSupportedCurrencyTypes 获取所有支持的货币类型
func GetSupportedCurrencyTypes() []CurrencyType {
	types := make([]CurrencyType, 0, len(currencyUnitMap))
	for currencyType := range currencyUnitMap {
		types = append(types, currencyType)
	}
	return types
}

// GetSupportedCurrencyTypeStrings 获取所有支持的货币类型字符串
func GetSupportedCurrencyTypeStrings() []string {
	types := GetSupportedCurrencyTypes()
	strings := make([]string, len(types))
	for i, currencyType := range types {
		strings[i] = string(currencyType)
	}
	return strings
}
