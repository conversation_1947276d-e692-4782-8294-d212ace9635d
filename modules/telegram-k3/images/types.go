package images

import (
	"fmt"
)

// 骰子开奖网格结构
type DiceGrid struct {
	Numbers [][]int        // 动态大小的数字网格
	Colors  [][]string     // 对应的颜色
	Stats   map[string]int // 统计信息
	Width   int            // 网格宽度
	Height  int            // 网格高度
}

// CalculateStats 计算网格统计信息
func (grid *DiceGrid) CalculateStats() {
	// 重置统计信息
	grid.Stats = make(map[string]int)

	blueCount := 0
	redCount := 0
	whiteCount := 0

	// 统计大小颜色和数字
	for row := 0; row < grid.Height; row++ {
		for col := 0; col < grid.Width; col++ {
			num := grid.Numbers[row][col]
			color := grid.Colors[row][col]

			// 统计颜色
			switch color {
			case "🔵":
				blueCount++
			case "🔴":
				redCount++
			case "⚪":
				whiteCount++
			}

			// 统计数字
			if num > 0 { // 只统计有效数字
				grid.Stats[fmt.Sprintf("num_%d", num)]++
			}
		}
	}

	grid.Stats["blue"] = blueCount
	grid.Stats["red"] = redCount
	grid.Stats["white"] = whiteCount
}
