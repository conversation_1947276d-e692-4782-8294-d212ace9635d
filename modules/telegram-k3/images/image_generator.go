package images

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"os"
	"s2/modules/telegram-k3/configs"
	"strconv"
	"unicode"

	"golang.org/x/image/font"
	"golang.org/x/image/font/opentype"
	"golang.org/x/image/math/fixed"
)

// ImageGenerator 图片生成器
type ImageGenerator struct {
	fontFace    font.Face // 英文字体（用于数字显示）
	chineseFont font.Face // 中文字体（用于统计信息）
	bgBlue      image.Image
	bgRed       image.Image
	bgGreen     image.Image
}

// NewImageGenerator 创建新的图片生成器
func NewImageGenerator() (*ImageGenerator, error) {
	// 加载英文字体
	englishFontPaths := []string{
		"statics/bahnschrift.ttf",
		"../../../service/telegram/statics/bahnschrift.ttf",
		"service/telegram/statics/bahnschrift.ttf",
	}

	var englishFontData []byte
	var err error
	for _, path := range englishFontPaths {
		englishFontData, err = os.ReadFile(path)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, fmt.Errorf("读取英文字体文件失败: %v", err)
	}

	englishTTF, err := opentype.Parse(englishFontData)
	if err != nil {
		return nil, fmt.Errorf("解析英文字体失败: %v", err)
	}

	englishFontFace, err := opentype.NewFace(englishTTF, &opentype.FaceOptions{
		Size:    28,
		DPI:     72,
		Hinting: font.HintingFull,
	})
	if err != nil {
		return nil, fmt.Errorf("创建英文字体失败: %v", err)
	}

	// 加载中文字体
	chineseFontPaths := []string{
		"statics/MicrosoftYaHei.ttf",
		"../../../service/telegram/statics/MicrosoftYaHei.ttf",
		"service/telegram/statics/MicrosoftYaHei.ttf",
	}

	var chineseFontData []byte
	for _, path := range chineseFontPaths {
		chineseFontData, err = os.ReadFile(path)
		if err == nil {
			break
		}
	}
	if err != nil {
		return nil, fmt.Errorf("读取中文字体文件失败: %v", err)
	}

	chineseTTF, err := opentype.Parse(chineseFontData)
	if err != nil {
		return nil, fmt.Errorf("解析中文字体失败: %v", err)
	}

	chineseFontFace, err := opentype.NewFace(chineseTTF, &opentype.FaceOptions{
		Size:    24, // 中文字体稍小一些
		DPI:     72,
		Hinting: font.HintingFull,
	})
	if err != nil {
		return nil, fmt.Errorf("创建中文字体失败: %v", err)
	}

	// 尝试多个可能的路径加载背景图片
	bgPaths := []string{
		"statics/",
		"../../../service/telegram/statics/",
		"service/telegram/statics/",
	}

	var bgBlue, bgRed, bgGreen image.Image
	for _, basePath := range bgPaths {
		bgBlue, err = loadImage(basePath + "bg_blue.png")
		if err == nil {
			bgRed, err = loadImage(basePath + "bg_red.png")
			if err == nil {
				bgGreen, err = loadImage(basePath + "bg_green.png")
				if err == nil {
					break
				}
			}
		}
	}

	if err != nil {
		return nil, fmt.Errorf("加载背景图片失败: %v", err)
	}

	return &ImageGenerator{
		fontFace:    englishFontFace,
		chineseFont: chineseFontFace,
		bgBlue:      bgBlue,
		bgRed:       bgRed,
		bgGreen:     bgGreen,
	}, nil
}

// loadImage 加载图片文件
func loadImage(path string) (image.Image, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	img, _, err := image.Decode(file)
	if err != nil {
		return nil, err
	}

	return img, nil
}

// resizeImage 调整图片大小
func resizeImage(src image.Image, width, height int) image.Image {
	dst := image.NewRGBA(image.Rect(0, 0, width, height))
	draw.Draw(dst, dst.Bounds(), src, image.Point{}, draw.Over)
	return dst
}

// GenerateDiceGridImage 生成骰子开奖网格图片
func (ig *ImageGenerator) GenerateDiceGridImage(grid *DiceGrid, statsNumbers []int) (string, error) {
	width := configs.GetConfig().GridWidth
	height := configs.GetConfig().GridHeight

	// 固定图片尺寸为 840×420
	imgWidth := 840
	imgHeight := 420

	// 计算单元格大小
	cellSize := imgWidth / width // 840 / 14 = 60
	margin := 0                  // 无边距，充分利用空间

	// 创建图片
	img := image.NewRGBA(image.Rect(0, 0, imgWidth, imgHeight))

	// 设置白色背景
	draw.Draw(img, img.Bounds(), &image.Uniform{color.White}, image.Point{}, draw.Src)

	// 绘制网格线
	ig.drawGridLines(img, width, height, cellSize, margin)

	// 绘制网格数据（只绘制前6行，第7行留空用于统计）
	for row := 0; row < height-1; row++ { // 只绘制前6行
		for col := 0; col < width; col++ {
			x := margin + col*cellSize
			y := margin + row*cellSize

			number := grid.Numbers[row][col]
			colorType := grid.Colors[row][col]

			if number > 0 {
				// 选择背景图片
				var bgImg image.Image
				switch colorType {
				case "🔵":
					bgImg = ig.bgBlue
				case "🔴":
					bgImg = ig.bgRed
				default:
					bgImg = ig.bgBlue // 默认使用蓝色
				}

				// 获取背景图片的原始尺寸
				bgBounds := bgImg.Bounds()
				bgWidth := bgBounds.Dx()
				bgHeight := bgBounds.Dy()

				// 计算背景图片在单元格中的居中位置
				bgX := x + (cellSize-bgWidth)/2  // 水平居中
				bgY := y + (cellSize-bgHeight)/2 // 垂直居中

				// 绘制背景图片 - 保持原始尺寸，居中显示
				bgRect := image.Rect(bgX, bgY, bgX+bgWidth, bgY+bgHeight)
				draw.Draw(img, bgRect, bgImg, image.Point{}, draw.Over)

				// 绘制数字 - 在单元格中心偏左下角显示
				numberStr := fmt.Sprintf("%02d", number)
				textX := x + cellSize/2 - 2  // 单元格中心X，稍微向左偏移
				textY := y + cellSize/2 + 10 // 单元格中心Y，稍微向下偏移
				ig.drawText(img, numberStr, textX, textY, color.White)
			}
			// 空位保持白色背景，不显示任何内容
		}
	}

	// 绘制统计行（在第7行）
	statsY := margin + (height-1)*cellSize // 第7行
	ig.drawStatistics(img, grid, statsNumbers, statsY, cellSize)

	// 保存图片
	filename := fmt.Sprintf("dice_grid_%dx%d.png", height, width)
	err := ig.saveImage(img, filename)
	if err != nil {
		return "", fmt.Errorf("保存图片失败: %v", err)
	}

	return filename, nil
}

// drawText 绘制文字
func (ig *ImageGenerator) drawText(img *image.RGBA, text string, x, y int, clr color.Color) {
	point := fixed.Point26_6{
		X: fixed.Int26_6(x * 64),
		Y: fixed.Int26_6(y * 64),
	}

	// 根据文本内容选择字体
	var selectedFont font.Face
	if containsChinese(text) {
		selectedFont = ig.chineseFont
	} else {
		selectedFont = ig.fontFace
	}

	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(clr),
		Face: selectedFont,
		Dot:  point,
	}

	// 计算文字宽度以居中显示
	bounds, _ := d.BoundString(text)
	textWidth := bounds.Max.X - bounds.Min.X
	offsetX := textWidth / 2

	d.Dot.X -= offsetX
	d.DrawString(text)
}

// containsChinese 检查文本是否包含中文字符
func containsChinese(text string) bool {
	for _, r := range text {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

// drawGridLines 绘制网格线
func (ig *ImageGenerator) drawGridLines(img *image.RGBA, width, height, cellSize, margin int) {
	// 绘制垂直线（只到第6行）
	for col := 0; col <= width; col++ {
		x := margin + col*cellSize
		for y := margin; y < margin+(height-1)*cellSize; y++ { // 只画到第6行
			img.Set(x, y, color.RGBA{220, 220, 220, 255}) // 浅灰色网格线
		}
	}

	// 绘制水平线（包括第7行的横线）
	for row := 0; row < height; row++ { // 画到第7行
		y := margin + row*cellSize
		for x := margin; x < margin+width*cellSize; x++ {
			img.Set(x, y, color.RGBA{220, 220, 220, 255}) // 浅灰色网格线
		}
	}
}

// drawStatistics 绘制统计信息
func (ig *ImageGenerator) drawStatistics(img *image.RGBA, grid *DiceGrid, statsNumbers []int, y, cellSize int) {
	// 绘制"统计："文字 - 往右调整，在第7行上下居中
	ig.drawText(img, "统计：", 40, y+cellSize/2+10, color.Black)

	// 绘制统计数字 - 使用矩形背景，确保在第7行上下居中
	startX := 80
	rectWidth := 60 // 矩形宽度
	spacing := 60   // 矩形间距

	for i, targetNum := range statsNumbers {
		count := grid.Stats[fmt.Sprintf("num_%d", targetNum)]
		x := startX + i*(rectWidth+spacing)

		// 获取绿色背景图片的原始尺寸
		bgBounds := ig.bgGreen.Bounds()
		bgWidth := bgBounds.Dx()
		bgHeight := bgBounds.Dy()

		// 计算背景图片在矩形区域中的居中位置
		bgX := x + (rectWidth-bgWidth)/2 // 水平居中
		bgY := y + (cellSize-bgHeight)/2 // 垂直居中

		// 检查边界，确保不超出图片范围
		if bgX+bgWidth > 840 {
			break // 如果超出图片宽度，停止绘制
		}

		// 绘制绿色背景图片 - 保持原始尺寸，居中显示
		bgRect := image.Rect(bgX, bgY, bgX+bgWidth, bgY+bgHeight)
		draw.Draw(img, bgRect, ig.bgGreen, image.Point{}, draw.Over)

		// 绘制数字（格式：03, 18）- 在背景图片中心显示
		numberStr := fmt.Sprintf("%02d", targetNum)
		textX := bgX + bgWidth/2 - 2
		textY := bgY + bgHeight/2 + 10
		ig.drawText(img, numberStr, textX, textY, color.White)

		// 绘制计数 - 在背景图片右侧，保持垂直居中
		countStr := strconv.Itoa(count)
		countX := bgX + bgWidth + 12
		countY := bgY + bgHeight/2 + 10
		ig.drawText(img, countStr, countX, countY, color.Black)
	}
}

// saveImage 保存图片
func (ig *ImageGenerator) saveImage(img *image.RGBA, filename string) error {
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	return png.Encode(file, img)
}

// Cleanup 清理临时文件
func (ig *ImageGenerator) Cleanup(filename string) {
	if filename != "" {
		os.Remove(filename)
	}
}
