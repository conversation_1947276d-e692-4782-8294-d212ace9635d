package models

import (
	"time"
)

// TelegramK3DiceResult 骰子结果模型
type TelegramK3DiceResult struct {
	ID        uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	PeriodID  string    `json:"period_id" gorm:"index;column:period_id"`
	ChatID    int64     `json:"chat_id" gorm:"index;column:chat_id"`
	UserID    int64     `json:"user_id" gorm:"column:user_id"` // 掷骰子的用户ID，0表示机器人
	UserName  string    `json:"user_name" gorm:"column:user_name"`
	PeriodNum int       `json:"period_num" gorm:"column:period_num"` // 期数
	Dice1     int       `json:"dice1" gorm:"column:dice1"`
	Dice2     int       `json:"dice2" gorm:"column:dice2"`
	Dice3     int       `json:"dice3" gorm:"column:dice3"`
	Sum       int       `json:"sum" gorm:"column:sum"`       // 总和
	Result    string    `json:"result" gorm:"column:result"` // 开奖结果
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
}

// TableName 指定表名
func (TelegramK3DiceResult) TableName() string {
	return "telegramk3_dice_result"
}
