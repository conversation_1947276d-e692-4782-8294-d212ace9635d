package models

import (
	"time"
)

// TelegramK3BetRecord 下注记录模型
type TelegramK3BetRecord struct {
	ID           uint      `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	PeriodID     string    `json:"period_id" gorm:"index;column:period_id"`
	ChatID       int64     `json:"chat_id" gorm:"index;column:chat_id"`
	UserID       int64     `json:"user_id" gorm:"index;column:user_id"`
	UserName     string    `json:"user_name" gorm:"column:user_name"`
	BetType      string    `json:"bet_type" gorm:"column:bet_type"`
	Amount       int64     `json:"amount" gorm:"column:amount"`         // 下注金额
	Odds         float64   `json:"odds" gorm:"column:odds"`             // 赔率
	WinAmount    int64     `json:"win_amount" gorm:"column:win_amount"` // 赢得金额
	IsWin        bool      `json:"is_win" gorm:"column:is_win"`
	PayoutStatus string    `json:"payout_status" gorm:"column:payout_status"` // 赔付状态: success, failed, no_win
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at"`
}

// TableName 指定表名
func (TelegramK3BetRecord) TableName() string {
	return "telegramk3_bet_record"
}
