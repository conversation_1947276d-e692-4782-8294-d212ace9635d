package models

import (
	"time"
)

// TelegramK3GamePeriod 游戏期号模型
type TelegramK3GamePeriod struct {
	ID        string    `json:"id" gorm:"primaryKey;column:id"`
	ChatID    int64     `json:"chat_id" gorm:"index;column:chat_id"`
	StartTime time.Time `json:"start_time" gorm:"column:start_time"`
	EndTime   time.Time `json:"end_time" gorm:"column:end_time"`
	State     string    `json:"state" gorm:"column:state"`
	PeriodNum int       `json:"period_num" gorm:"column:period_num"` // 期数 (1-84)
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at"`
}

// TableName 指定表名
func (TelegramK3GamePeriod) TableName() string {
	return "telegramk3_game_period"
}
