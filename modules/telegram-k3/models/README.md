# Telegram K3 游戏模型

本目录包含Telegram K3骰子下注游戏的所有GORM数据库模型。

## 模型概览

### 1. TelegramK3GamePeriod - 游戏期号模型
**文件**: `game_period.go`  
**表名**: `telegramk3_game_period`

**作用**: 记录每一期游戏的基本信息，包括期号、开始时间、结束时间、游戏状态等。

**主要字段**:
- `id`: 主键，期号ID
- `chat_id`: 群组ID，索引
- `start_time`: 游戏开始时间
- `end_time`: 游戏结束时间
- `state`: 游戏状态（waiting/betting/rolling/settling）
- `period_num`: 期数（1-84）
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 2. TelegramK3BetRecord - 下注记录模型
**文件**: `bet_record.go`  
**表名**: `telegramk3_bet_record`

**作用**: 记录每个玩家的下注信息，包括下注类型、金额、赔率、输赢结果等。

**主要字段**:
- `id`: 主键，自增ID
- `period_id`: 期号ID，索引
- `chat_id`: 群组ID，索引
- `user_id`: 用户ID，索引
- `user_name`: 用户名
- `bet_type`: 下注类型（big/small/odd/even等）
- `amount`: 下注金额
- `odds`: 赔率
- `win_amount`: 赢得金额
- `is_win`: 是否获胜
- `payout_status`: 赔付状态（success/failed/no_win）
- `created_at`: 创建时间

### 3. TelegramK3DiceResult - 骰子结果模型
**文件**: `dice_result.go`  
**表名**: `telegramk3_dice_result`

**作用**: 记录每期游戏的骰子结果，包括三个骰子的点数、总和、开奖结果等。

**主要字段**:
- `id`: 主键，自增ID
- `period_id`: 期号ID，索引
- `chat_id`: 群组ID，索引
- `user_id`: 掷骰子的用户ID（0表示机器人）
- `user_name`: 掷骰子的用户名
- `period_num`: 期数
- `dice1`: 第一个骰子点数
- `dice2`: 第二个骰子点数
- `dice3`: 第三个骰子点数
- `sum`: 三个骰子总和
- `result`: 开奖结果（big/small/odd/even等）
- `created_at`: 创建时间

## 命名规范

- **文件名**: 使用简洁的文件名，不加前缀
- **表名**: 所有表名都以`telegramk3_`为前缀
- **字段名**: 使用简洁的字段名，不加前缀
- **模型名**: 所有模型名都以`TelegramK3`为前缀

## 使用方式

在代码中使用这些模型时，可以通过以下方式：

```go
import "s2/modules/telegram-k3/models"

// 创建新的游戏期号
period := &models.TelegramK3GamePeriod{
    ID:        "period_id",
    ChatID:    123456789,
    StartTime: time.Now(),
    State:     "betting",
    PeriodNum: 1,
}

// 创建下注记录
bet := &models.TelegramK3BetRecord{
    PeriodID: "period_id",
    UserID:   987654321,
    BetType:  "big",
    Amount:   100,
    Odds:     2.0,
}

// 创建骰子结果
result := &models.TelegramK3DiceResult{
    PeriodID: "period_id",
    Dice1:    3,
    Dice2:    4,
    Dice3:    5,
    Sum:      12,
    Result:   "big",
}
```

## 数据库迁移

在模块初始化时，会自动创建这些表：

```go
mdb.Default().AutoMigrate(
    &models.TelegramK3GamePeriod{},
    &models.TelegramK3BetRecord{},
    &models.TelegramK3DiceResult{},
)
``` 