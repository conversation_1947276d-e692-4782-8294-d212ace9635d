# 模块说明文档

以下文档针对 `modules/` 目录下各子模块进行功能、主要文件及职责说明。

## 1. order  
**功能**：处理用户的充值（recharge）和提现（withdraw）业务，包含链上交互、订单状态管理、事件通知。  
**主要文件**：  
- `module.go`：模块注册与启动。  
- `handle.go`：HTTP/消息处理器，路由各类订单请求。  
- `recharge.go`：充值订单业务逻辑。  
- `withdraw.go`：提现订单业务逻辑。  
- `model.go`：订单数据模型定义（数据库表结构）。  
- `event.go`：订单相关事件定义与发布。  
- 子目录 `chains/`：各链（ETH/BSC…）的节点、RPC 接入实现；  
- 子目录 `wallet/`：钱包管理、签名和转账逻辑；  
- 子目录 `stypes/`、`coins/`：静态类型、币种配置。

## 2. telegram  
**功能**：Telegram Bot 服务，支持用户交互、群红包、管理员远程命令。  
**主要文件**：  
- `module.go`：模块入口，Bot 启动与配置。  
- `telegram.go`：Bot 连接、基础消息拉取。  
- `handle.go`：用户消息命令处理。  
- `adminHandle.go`：管理员命令（黑名单、公告等）。  
- `redpacket.go`：红包活动发放与领取逻辑。  
- `model.go`：数据库模型（用户绑定、红包记录等）。

## 3. gamedoor  
**功能**：游戏厂商接入网关，桥接第三方游戏（如 PP、PG）的 HTTP/表单接口与内部 `GameSpin` 服务。  
**主要文件**：  
- `module.go`：路由注册（`/gs2c/playGame.do`、`/ge/v4/gameService` 等）。  
- `pp.go`：PP 平台请求解析与转发。  
- `pg.go`：PG 平台接入逻辑。  
- `http.go`：共用 HTTP 处理模板与结果包装。  
- `history.go`/`pghistory.go`：历史记录查询接口。

## 4. defi  
**功能**：DeFi/奖池（Jackpot）模块，调用外部 DeFi 服务进行奖池尝试和分发。  
**主要文件**：  
- `module.go`：模块启动与消息路由注册。  
- `handle.go`：DeFi 相关消息处理（`DefiTryJackpotReq`）。  
- `service.go`：调用外部 DeFi 接口的业务逻辑。  
- `model.go`：数据模型（奖池记录等）。  
- 目录 `jackpot/`：奖池算法或链上交互实现。

## 5. blockhash  
**功能**：区块哈希获取与广播模块，定时从链上拉最新 `BlockHash` 并推送给订阅者（如 spin 算法）。  
**主要文件**：  
- `module.go`：定时任务与订阅者注册。  
- `handle.go`：接收并分发 `UpdateBlockHashMsg`。  
- 子目录 `scan/`：链节点扫描与拉取实现。

## 6. lobby  
**功能**：大厅（Lobby）微服务，聚合用户基础信息、资产余额、游戏列表等。  
**主要目录**：  
- `module.go`：服务启动与域（Domain）初始化。  
- `domain/`：  
  - `api/`：接口定义（`ILobby`）。  
  - `impl/`：具体业务实现（用户快照、统计、BI 日志等）。  
- `userops/`：用户操作（注册、登录、渠道埋点）相关逻辑。

## 7. hub  
**功能**：实时消息中心（Hub），为前端或其他服务提供推送通道。  
**主要文件**：  
- `module.go`：注册 Stream/Hub 组件。  
- `unigame.go`：统一游戏统计/推送接口实现。

## 8. door  
**功能**：统一网关（Door），处理客户端 HTTP/WebSocket 接入，并转发到内部模块。  
**主要文件**：  
- `module.go`：注册 HTTP 路由及中间件。  
- `http.go`：RESTful 接口入口。  
- `websocket.go`：WebSocket 连接管理与消息分发。  
- `handle.go`：基础消息解包与路由到 `adminops`、`userops` 等。

## 9. game  
**功能**：核心游戏域（Game Domain），提供转盘、下注、流水查询等业务。  
**主要目录**：  
- `module.go`：Game 服务入口。  
- `domain/`：游戏核心逻辑接口（`IGame`）及 Spin、SpinRecord 实现。  
- `userops/`：用户在游戏侧的操作上下文。

## 10. gm  
**功能**：GM（管理员）域，提供后台管理接口，如账号管理、BI 查询、配置管理、充值/提现审核。  
**主要目录**：  
- `module.go`：GM 服务启动。  
- `domain/`：  
  - `api/`：GM 接口定义（`IAccount`、`IBI`、`IConfig`）。  
  - `impl/`：各子域实现：  
    - `account/`：管理员账户登录/增删改。  
    - `bi/`：BI SQL 存储、查询。  
    - `config/`：动态表单配置管理。  
- `pb/gm.proto`：GM RPC/消息定义。

## 11. account  
**功能**：用户帐号微服务，支持用户注册、登录、邮箱/验证码、资料更新等。  
**主要文件**：  
- `module.go`：帐号服务入口。  
- `handle.go`：HTTP/消息处理（登录、注册、修改密码、邮箱、验证码）。  
- `model.go`：用户模型（MySQL）。  
- `config.go`：帐号相关配置。  
- `email.go`：邮件发送/校验。

**总结**：  
- **分层思路**：各模块职责单一、自治；  
- **通信方式**：HTTP、WebSocket、消息流（`message.Stream`）、AdminOps JSON 消息；  
- **数据存储**：MySQL（静态模型）、MongoDB（BI、配置）、ClickHouse（大数据分析）、Redis/Cache（Token、快照）；  
- **拓展性**：新游戏、新链、新渠道可新增子模块或在现有模块中插拔。
