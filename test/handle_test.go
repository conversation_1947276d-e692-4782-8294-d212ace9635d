package thirdapp_test

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
)

// appid 平台分配  eg:1
// Key: 平台分配16位字符串 eg:gNCD3fxsGi2OZSRw
// IV: 平台分配16位字符串 eg:BguU4a7ftj0nuPzj
const (
	appID   = "1"
	key     = "gNCD3fxsGi2OZSRw"
	iv      = "BguU4a7ftj0nuPzj"
	baseURL = "https://api.panlaxy.io/thirdapp"
)

// encrypt applies AES-CBC encryption with PKCS7 padding and returns a Base64-encoded ciphertext.
// 应用AES-CBC加密算法，使用PKCS7填充，对密文进行Base64编码并返回字符串
func encrypt(src []byte) string {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		panic(err)
	}
	bs := block.BlockSize()
	padding := bs - len(src)%bs
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	src = append(src, padtext...)
	dst := make([]byte, len(src))
	mode := cipher.NewCBCEncrypter(block, []byte(iv))
	mode.CryptBlocks(dst, src)
	encrypted := base64.StdEncoding.EncodeToString(dst)
	return encrypted
}

// doRequest sends Base64-encoded ciphertext of JSON payload as request body and returns response body and status code
func doRequest(t *testing.T, endpoint string, payload interface{}) ([]byte, int) {
	data, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("marshal payload error: %v", err)
	}
	enc := encrypt(data)
	fmt.Println(enc)
	req, err := http.NewRequest("POST", baseURL+endpoint, bytes.NewReader([]byte(enc)))
	if err != nil {
		t.Fatalf("new request error: %v", err)
	}
	req.Header.Set("appid", appID)
	req.Header.Set("Content-Type", "application/octet-stream")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		t.Fatalf("request error: %v", err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("read body error: %v", err)
	}
	return body, resp.StatusCode
}

func TestRegisterPlayer(t *testing.T) {
	body, status := doRequest(t, "/registerPlayer", map[string]interface{}{"Account": "1"})
	if status != http.StatusOK {
		t.Fatalf("expected status 200, got %d", status)
	}
	var resp map[string]interface{}
	if err := json.Unmarshal(body, &resp); err != nil {
		t.Fatalf("unmarshal error: %v", err)
	}
	if resp["Code"] != 0 {
		t.Errorf("expected Code THIRDAPP_SUCCESS, got %v", resp["Code"])
	}
	t.Logf("RegisterPlayer userID=%d", resp["UserID"])
}

func TestTransferIn(t *testing.T) {
	req := map[string]interface{}{"Account": "1", "Amount": 100.0, "AssetID": 10000}
	body, status := doRequest(t, "/transferIn", req)
	if status != http.StatusOK {
		t.Fatalf("expected status 200, got %d", status)
	}
	var resp map[string]interface{}
	if err := json.Unmarshal(body, &resp); err != nil {
		t.Fatalf("unmarshal error: %v", err)
	}
	if resp["Code"] != 0 {
		t.Errorf("expected Code THIRDAPP_SUCCESS, got %v", resp["Code"])
	}
	t.Logf("TransferIn new balance=%.2f", resp["Balance"])
}

func TestTransferOut(t *testing.T) {
	req := map[string]interface{}{"Account": "1", "AssetID": 10000}
	body, status := doRequest(t, "/transferOut", req)
	if status != http.StatusOK {
		t.Fatalf("expected status 200, got %d", status)
	}
	var resp map[string]interface{}
	if err := json.Unmarshal(body, &resp); err != nil {
		t.Fatalf("unmarshal error: %v", err)
	}
	if resp["Code"] != 0 {
		t.Errorf("expected Code THIRDAPP_SUCCESS, got %v", resp["Code"])
	}
	t.Logf("TransferOut amount=%v", resp["Amount"])
}

func TestBalance(t *testing.T) {
	req := map[string]interface{}{"Account": "1", "AssetID": 10000}
	body, status := doRequest(t, "/balance", req)
	if status != http.StatusOK {
		t.Fatalf("expected status 200, got %d", status)
	}
	var resp map[string]interface{}
	if err := json.Unmarshal(body, &resp); err != nil {
		t.Fatalf("unmarshal error: %v", err)
	}
	if resp["Code"] != 0 {
		t.Errorf("expected Code THIRDAPP_SUCCESS, got %v", resp["Code"])
	}
	t.Logf("Balance=%.2f", resp["Balance"])
}

func TestRecord(t *testing.T) {
	req := &map[string]interface{}{"StartTime": **********, "EndTime": **********, "Offset": 1, "Limit": 10}
	body, status := doRequest(t, "/record", req)
	if status != http.StatusOK {
		t.Fatalf("expected status 200, got %d", status)
	}
	var resp map[string]interface{}
	if err := json.Unmarshal(body, &resp); err != nil {
		t.Fatalf("unmarshal error: %v", err)
	}
	if resp["Code"] != 0 {
		t.Errorf("expected Code THIRDAPP_SUCCESS, got %v", resp["Code"])
	}
	t.Logf("Got %d record rows", len(resp["Rows"].([]interface{})))
}
