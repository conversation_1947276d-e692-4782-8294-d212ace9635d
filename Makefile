PHONY: run stop build clean pbc docker

proto:
	@python3 core/message/pbc.py source=pb

build: build_gate build_game build_master build_gms

build_game:
	@echo "Building game..."
	@go build -v -o bin/game ./service/game
	
# 构建 gate 服务
build_gate:
	@echo "Building gate..."
	@go build -v -o bin/gate ./service/gate

# 构建 gms 服务
build_gms:
	@echo "Building gms..."
	@go build -v -o bin/gms ./service/gms

# 构建 master 服务
build_master:
	@echo "Building master..."
	@go build -v -o bin/master ./service/master

dev:
	@echo "Starting dev..."
	go build  -o ./bin/dev $(shell pwd)/service/dev && bin/dev -c $(config) > $(shell pwd)/logs/dev.log 2>&1 &

BIN_DIR = $(shell pwd)/bin

SERVICES = $(shell find $(BIN_DIR) -maxdepth 1 -type f -executable -exec basename {} \;)

# 启动所有服务
start:
	@for service in $(SERVICES); do \
	  echo "Starting $$service..."; \
	  cd $(shell pwd)/service/$$service && $(BIN_DIR)/$$service >> $(shell pwd)/logs/$$service.log 2>&1 & \
	  sleep 1; \
	  PID=$$(pgrep -f "$(BIN_DIR)/$$service"); \
	  if [ -n "$$PID" ]; then \
	    CMD=$$(ps -p $$PID -o comm=); \
	    echo "Started process $$PID ($$CMD)"; \
	  else \
	    echo "Failed to start $$service"; \
	  fi; \
	done

# 停止所有服务
stop:
	@PIDS=$$(pgrep -f "$$(pwd)/bin/"); \
	if [ -n "$$PIDS" ]; then \
	  for PID in $$PIDS; do \
	    CMD=$$(ps -p $$PID -o comm=); \
	    echo "Killing process $$PID ($$CMD)"; \
	    kill $$PID; \
	  done; \
	else \
	  echo "No processes found for executables in $$(pwd)/bin/"; \
	fi

restart: stop run

# 清理日志文件
clean:
	rm -f logs/*.log

wscli:
	@node fakecli/wscli.js

tcpcli:
	@node fakecli/tcpcli.js

httpcli:
	@node fakecli/httpcli.js

gmcli:
	@node fakecli/gmcli.js

# example: make case name=Demo module=play
case:
	@python3 core/basic/domainops/create.py name=$(name) path=modules/$(module)

msgid:
	@node fakecli/msgid.js

pbc: msgid proto

pprof:
	@curl http://localhost:8888/debug/pprof/profile?seconds=30 > logs/cpu.pprof
	@go tool pprof -http=:8080 logs/cpu.pprof

config:
	@python3 core/conf/import.py path=gsconf/config/json db=s2

pull:
	@git pull
	@# @git submodule update --init --recursive
	@git submodule update --remote --merge
	@echo "Main repository and all submodules updated successfully."

update: pull pbc

docker: force
	@go work vendor
	@docker build -f docker/Dockerfile -t s2:$(tag) .

force: