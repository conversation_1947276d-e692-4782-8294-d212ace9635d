# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file

# env file
.env

logs
bin
node_modules
pb/*.go
pb/msgid.txt
repl.txt
test.*
.vscode
vendor
./static
.DS_Store
datax.release
igame
core
gsconf/config
pb
server
configs.yaml
update.py
debug
debug.ext
igameKing

service/telegram/*.png
service/telegram/telegram
