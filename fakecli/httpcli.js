import { decode, encode, initMsgBuilder, print, runCli } from "./common.js";
import axios from "axios";
import https from "https";

initMsgBuilder("pb")

const agent = new https.Agent({
    rejectUnauthorized: false // 忽略证书验证
});

var address = "127.0.0.1:9527"

async function connect(host = "127.0.0.1", port = 9527, account = "dev01") {
    address = `http://${host}:${port}/`
    let resp = await request("Account.AuthOrCreateAccountReq", { Account: account, Password: account })
    console.log("resp.Token:", resp.Token)
    request.token = resp.Token
}

async function request(msgName = 'SayHelloReq', msgBody = { text: "hello, server!" }) {
    try {
        let req = {
            Cmd: msgName,
            Data: JSON.stringify(msgBody),
        }
        let resp = await axios.post(address, req, {
            timeout: 60 * 60 * 1000,
            httpsAgent: agent,
            headers: {
                "Token": request.token
            }
        })
        console.log("req:", JSON.stringify(req), "resp:", JSON.stringify(resp.data))
        return resp.data
    } catch (err) {
        return {
            Status: err.response.status,
            Data: err.response.data
        }
    }
}

connect()

runCli({ request, connect })