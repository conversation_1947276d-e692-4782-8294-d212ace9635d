import { log } from "console";
import { decode, encode, initMsgBuilder, print, runCli, enumMsgName, nametoid } from "./common.js";
import { writeFileSync } from "fs";

initMsgBuilder("pb")

var NameToID = {}
var IDToName = {}

for (let name in enumMsgName) {
    let msgID = nametoid(name)
    if (IDToName[msgID]) {
        log(`duplicate msgID from ${name} and ${IDToName[msgID]}`)
        process.exit(1)
    }
    NameToID[name] = msgID
}

writeFileSync("pb/msgid.json", JSON.stringify(NameToID, null, 4))