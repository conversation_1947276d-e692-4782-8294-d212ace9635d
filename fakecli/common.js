import protobuf from 'protobufjs';
import { writeFileSync, readdirSync, readFileSync, existsSync } from 'fs'
import { fileURLToPath } from 'url';
import path from 'path';
import repl from 'repl'
import { log } from 'console';


const msgBuilders = {}
const msgidToName = {}
export const enumMsgName = {}
const historyFile = "repl.txt"
var r = null

export function initMsgBuilder(cwd) {
    let files = readdirSync(cwd)
    for (let name of files) {
        if (!name.endsWith(".proto")) {
            continue
        }
        const msgBuilder = new protobuf.Root()
        msgBuilder.resolvePath = function (origin, target) {
            return path.resolve(process.cwd(), target);
        }

        // console.log(path + '/' + name)
        msgBuilder.loadSync(cwd + '/' + name, { keepCase: true })
        for (let msgName in msgBuilder.nested.pb.nested) {
            msgBuilders[msgName] = msgBuilder.lookup(msgName)
            msgidToName[nametoid(msgName)] = msgName
            enumMsgName[msgName] = msgName
        }
    }
}

/**
 * encode
 * @param {string} msgName 
 * @param {object} msg 
 * @returns 
 */
export function encode(msgName, msg) {
    let protoMsg = msgBuilders[msgName].create(msg)
    protoMsg = msgBuilders[msgName].encode(protoMsg).finish()
    let buf = Buffer.alloc(5)
    buf.writeUint32LE(nametoid(msgName))
    buf.writeUIntLE(1, 4, 1)
    return Buffer.concat([buf, Buffer.from(protoMsg)])
}

/**
 * decode
 * @param {Buffer} buf 
 * @returns 
 */
export function decode(buf) {
    let msgid = buf.readUInt32LE()
    let msgName = msgidToName[msgid]
    const protoMsg = msgBuilders[msgName].decode(buf.slice(5), buf.length - 5)
    print(protoMsg)
    return [msgName, msgBuilders[msgName].toObject(protoMsg)]
}

export function nametoid(msgName) {
    let s = 31
    let v = 0
    for (let c of msgName) {
        v = uint32(v * s) + c.charCodeAt()
    }
    return uint32(v)
}


function uint32(x) {
    return x % Math.pow(2, 32);
}

export function print(...any) {
    var input = ''
    if (r != null) {
        input = r.line || ''
        r.output.write('\x1b[2K\r')
    }
    log(...any)
    r.output.write(`${r._prompt}${input}`);
    // process.stdout.write('> ') // 模拟prompt
}

export function runCli(context = {}) {
    context.pb = {}
    for (let name in enumMsgName) {
        /**
         * 
         * @param {Object} body 
         * @param {string} module 
         */
        if (!name.endsWith("Req") && !name.endsWith("Msg")) {
            continue
        }
        context.pb[name] = function (body = {}, module = undefined) {
            let msgName = module ? `${module}.${name}` : name
            let since = process.hrtime()
            if (name.endsWith("Req")) {
                context.request(msgName, body).then(response => {
                    print(log(response))
                    let duration = process.hrtime(since)
                    let ms = duration[0] * 1000 + duration[1] / 1e6
                    print('duration', ms, 'ms')
                }).catch(error => {
                    print(error)
                })
            } else if (name.endsWith("Msg")) {
                context.send(msgName, body)
            }
        }
    }
    const enums = enumContext(context)
    r = repl.start({
        prompt: `> `,
        preview: true,
        terminal: true,
        completer: (line) => {
            const historyMatches = enums.concat(r.history).filter((cmd) => cmd.startsWith(line));
            // log(historyMatches)
            enums.push()
            return [historyMatches || [], line];
        }
    });
    if (existsSync(historyFile)) {
        let history = readFileSync(historyFile, 'utf8').split('\n').reverse();
        history.forEach((line) => {
            if (line) r.history.push(line);
        });
    }
    r.on('exit', () => {
        writeFileSync(historyFile, r.history.reverse().join('\n'), 'utf8');
        process.exit();
    });

    Object.setPrototypeOf(r.context, context);
    global.console = r.context.console;
}

function enumContext(obj, prefix = "", enums = []) {
    for (let key in obj) {
        enums.push(prefix + key)
        if (typeof obj == 'object') {
            enumContext(obj[key], prefix + key + '.', enums)
        }
    }
    return enums
}
// function int(x) {
//     x = Number(x);
//     return x < 0 ? Math.ceil(x) : Math.floor(x);
// }

// function mod(a, b) {
//     return a - Math.floor(a / b) * b;
// }


// var m = encode("SayHelloReq", { text: "hello" })
// var n = decode(m)
// log(n)