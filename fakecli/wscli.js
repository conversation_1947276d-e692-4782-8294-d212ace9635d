import { WebSocket } from 'ws';
import { decode, encode, initMsgBuilder, print, runCli } from "./common.js";
import axios from "axios";
import https from "https";

var host = "127.0.0.1"
var port = 9527
var ws = null
var timer = null
var address = ""
var token = ""

const agent = new https.Agent({
    rejectUnauthorized: false // 忽略证书验证
});

async function connect(host = "127.0.0.1", port = 9527, account = "dev01") {
    address = `http://${host}:${port}/`
    let result = await request("Account.AuthOrCreateAccountReq", { Account: account, Password: account })
    token = result.Token
    ws = new WebSocket(`ws://${host}:${port}/ws?token=${token}`);
    ws.on('open', function open() {
        print('connect success');
        timer = setInterval(() => {
            ws.send(Buffer.alloc(0))
        }, 3000)
    });

    ws.on('message', function (data) {
        if (data.length == 0) {
            return
        }
        try {
            let [msgName, msg] = decode(data)
            print("recv server msg:", msgName, msg)
        } catch (e) {
            print("decode error", data, data.length, e)
        }
    });

    ws.on('close', function () {
        print('websocket close');
        clearInterval(timer)
    });

    ws.on('error', function error(err) {
        print('websocket error ', err.message);
    });
}

async function request(msgName = 'SayHelloMsg', msgBody = { text: "hello, server!" }) {
    try {
        let resp = await axios.post(address, {
            Cmd: msgName,
            Data: JSON.stringify(msgBody),
        }, {
            timeout: 60 * 60 * 1000,
            httpsAgent: agent,
            headers: {
                "Token": token
            }
        })
        return resp.data
    } catch (err) {
        print("error", err)
        return {
            err
        }
    }
}

async function benchmark(msgName, body, iterations = 1000, count = 1) {
    for (let n = 0; n < count; n++) {
        let startTime = Date.now();
        let promises = [];
        for (let i = 0; i < iterations; i++) {
            promises.push(
                request(msgName, body)
                    .then(response => {
                        // print(`Response ${i}:`, response);
                    })
                    .catch(err => {
                        print(`Error on iteration ${i}:`, err);
                    })
            );
        }
        await Promise.all(promises);
        let endTime = Date.now();
        let duration = (endTime - startTime) / 1000; // in seconds
        let tps = iterations / duration;
        print(`Stress test completed in ${duration} seconds`);
        print(`Transactions per second (TPS): ${tps}`);
    }
    print('Benchmark test completed');
}


async function send(msgName = 'SayHelloMsg', msgBody = { text: "hello, server!" }) {
    ws.send(encode(msgName, msgBody))
}

initMsgBuilder('pb')

connect(host, port)
runCli({ request, send, connect, benchmark })