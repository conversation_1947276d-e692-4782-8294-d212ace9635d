import { decode, encode, initMsgBuilder, print, runCli } from "./common.js";
import axios from "axios";
import https from "https";

initMsgBuilder("pb")

const agent = new https.Agent({
    rejectUnauthorized: false // 忽略证书验证
});

var address = "127.0.0.1:8066"

async function connect(host = "127.0.0.1", port = 8066, account = "superadmin") {
    address = `http://${host}:${port}/`
    let resp = await send("AdminLoginReq", { Username: account, Password: account })
    send.token = resp.Token
}

async function send(msgName = 'SayHelloReq', msgBody = { text: "hello, server!" }) {
    try {
        let resp = await axios.post(address, {
            Cmd: msgName,
            Data: JSON.stringify(msgBody),
        }, {
            timeout: 60 * 60 * 1000,
            httpsAgent: agent,
            headers: {
                "Token": send.token
            }
        })
        return resp.data
    } catch (err) {
        return {
            Status: err.response.status,
            Data: err.response.data
        }
    }
}

connect()

runCli({ send: send, connect })