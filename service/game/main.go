package main

import (
	"s2/modules/game"

	"github.com/jfcwrlight/core"
	"github.com/jfcwrlight/core/basic/timer"
	"github.com/jfcwrlight/core/infra/mgdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
)

func main() {
	app := core.DefaultApp()
	app.Append(
		game.New(),
	)
	Launch(app)
	defer Shutdown(app)
	system.WaitExitSignal()
}

func Launch(app *core.App) {
	app.Launch()
	timer.RecoverFromMongo(mgdb.Default().Collection("timer"))
}

func Shutdown(app *core.App) {
	defer log.Logger().Sync()
	app.Shutdown()
	timer.CancelAndSaveMongo(mgdb.Default().Collection("timer"))
}
