package main

import (
	"s2/define"
	"s2/modules/account"
	"s2/modules/blockhash"
	"s2/modules/defi"
	"s2/modules/door"
	"s2/modules/game"
	"s2/modules/gamedoor"
	"s2/modules/gm"
	"s2/modules/hub"
	"s2/modules/lobby"
	"s2/modules/order"
	"s2/modules/telegram"

	"github.com/jfcwrlight/core"
	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/basic/timer"
	"github.com/jfcwrlight/core/infra/mgdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils/pprof"
)

func main() {
	pprof.Start()
	app := core.DefaultApp()
	app.Append(
		account.New(),
		lobby.New(),
		door.New(),
		gm.New(),
		hub.New(),
		blockhash.New(),
		order.New(),
		defi.New(),
		game.New(),
		gamedoor.New(),
		order.New(),
		telegram.New(),
	)
	Launch(app)
	defer Shutdown(app)
	system.WaitExitSignal()
}

func Launch(app *core.App) {
	app.Launch()
	timer.RecoverFromMongo(mgdb.Default().Collection("timer"))
	events.Publish(define.SystemEvent.ServerLaunchFinish)
}

func Shutdown(app *core.App) {
	defer log.Logger().Sync()
	app.Shutdown()
	timer.CancelAndSaveMongo(mgdb.Default().Collection("timer"))
}
